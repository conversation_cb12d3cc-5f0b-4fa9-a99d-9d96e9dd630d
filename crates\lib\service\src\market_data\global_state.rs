use std::sync::Arc;
use tokio::sync::OnceCell;
use super::shared_state::ServiceStateManager;

/// 全局状态管理器实例
/// 
/// 使用OnceCell确保只初始化一次，并且可以在整个应用程序中共享
static GLOBAL_STATE_MANAGER: OnceCell<Arc<ServiceStateManager>> = OnceCell::const_new();

/// 初始化全局状态管理器
/// 
/// 这个函数应该在应用程序启动时调用一次
pub async fn init_global_state_manager() -> Arc<ServiceStateManager> {
    GLOBAL_STATE_MANAGER.get_or_init(|| async {
        Arc::new(ServiceStateManager::new())
    }).await.clone()
}

/// 获取全局状态管理器
/// 
/// 如果状态管理器还没有初始化，会自动初始化
pub async fn get_global_state_manager() -> Arc<ServiceStateManager> {
    GLOBAL_STATE_MANAGER.get_or_init(|| async {
        Arc::new(ServiceStateManager::new())
    }).await.clone()
}

/// 检查全局状态管理器是否已初始化
pub fn is_global_state_manager_initialized() -> bool {
    GLOBAL_STATE_MANAGER.get().is_some()
}
