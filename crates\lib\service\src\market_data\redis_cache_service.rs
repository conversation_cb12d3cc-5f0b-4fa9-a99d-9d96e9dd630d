// Redis缓存服务
// 负责缓存1分钟级别的K线数据，以天为单位存储，使用ZSet结构

use chrono::{DateTime, Utc, Duration, Datelike};
use repository::{
    connection::redis::RedisOperations,
    timescale::MarketDataRepository,
};
use common::domain::dto::{KlineDto, SimpleKline};
use crate::market_data::{CacheStatus, Result, MarketDataServiceError};
use std::collections::HashMap;

/// Redis缓存服务
#[derive(Clone)]
pub struct RedisCacheService {
    /// Redis操作接口
    redis_ops: RedisOperations,
    /// TimescaleDB数据仓库
    market_data_repo: MarketDataRepository,
    /// 交易所名称（用于构建Redis键）
    exchange_name: String,
}

impl RedisCacheService {
    /// 创建新的Redis缓存服务
    pub fn new(
        redis_ops: RedisOperations,
        market_data_repo: MarketDataRepository,
        exchange_name: String,
    ) -> Self {
        Self {
            redis_ops,
            market_data_repo,
            exchange_name,
        }
    }

    /// 为指定交易对缓存1分钟K线数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `start_date` - 开始日期
    /// * `end_date` - 结束日期（可选，默认为今天）
    pub async fn cache_klines_for_symbol(
        &self,
        symbol: &str,
        start_date: DateTime<Utc>,
        end_date: Option<DateTime<Utc>>,
    ) -> Result<()> {
        let end_date = end_date.unwrap_or_else(|| Utc::now());
        
        log::info!("开始为 {} 缓存1分钟K线数据，时间范围: {} 到 {}", 
            symbol, start_date, end_date);

        // 按天遍历时间范围
        let mut current_date = start_date.date_naive();
        let end_date_naive = end_date.date_naive();

        while current_date <= end_date_naive {
            let day_start = current_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
            let day_end = current_date.and_hms_opt(23, 59, 59).unwrap().and_utc();

            log::debug!("缓存 {} 在 {} 的数据", symbol, current_date);

            match self.cache_klines_for_day(symbol, day_start, day_end).await {
                Ok(count) => {
                    log::debug!("成功缓存 {} 条数据到 {}", count, current_date);
                }
                Err(e) => {
                    log::error!("缓存 {} 在 {} 的数据失败: {}", symbol, current_date, e);
                }
            }

            // 移动到下一天
            current_date += chrono::Duration::days(1);
        }

        log::info!("完成为 {} 缓存1分钟K线数据", symbol);
        Ok(())
    }

    /// 缓存指定天的K线数据
    async fn cache_klines_for_day(
        &self,
        symbol: &str,
        day_start: DateTime<Utc>,
        day_end: DateTime<Utc>,
    ) -> Result<i64> {
        // 从数据库查询1分钟K线数据
        let klines = self.market_data_repo.klines
            .get_klines(symbol, "1m", day_start, day_end, None)
            .await?;

        if klines.is_empty() {
            log::debug!("没有找到 {} 在 {} 的数据", symbol, day_start.format("%Y-%m-%d"));
            return Ok(0);
        }

        // 构建Redis键
        let date_str = day_start.format("%Y%m%d").to_string();
        let redis_key = self.build_redis_key(symbol, &date_str);

        // 准备ZSet数据
        let mut zset_data: Vec<(f64, String)> = Vec::new();
        
        for kline in &klines {
            // 转换为SimpleKline格式
            let simple_kline = SimpleKline::new(
                kline.open_price,
                kline.high_price,
                kline.low_price,
                kline.close_price,
                kline.volume,
                kline.time.timestamp(),
            );

            // 序列化为JSON
            let json_data = simple_kline.to_json()
                .map_err(|e| MarketDataServiceError::Serialization(e))?;

            // 使用时间戳作为score
            let score = kline.time.timestamp() as f64;
            zset_data.push((score, json_data));
        }

        // 批量写入Redis ZSet
        self.redis_ops.zadd_multiple(&redis_key, &zset_data)
            .map_err(|e| MarketDataServiceError::Cache(format!("Redis写入失败: {}", e)))?;

        // 设置过期时间
        let ttl = self.calculate_ttl_for_date(&date_str)?;
        if ttl > 0 {
            self.redis_ops.expire(&redis_key, ttl as usize)
                .map_err(|e| MarketDataServiceError::Cache(format!("设置TTL失败: {}", e)))?;
        }

        log::debug!("成功缓存 {} 条数据到Redis键: {}, TTL: {}秒", 
            zset_data.len(), redis_key, ttl);

        Ok(zset_data.len() as i64)
    }

    /// 从缓存中获取K线数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `start_time` - 开始时间
    /// * `end_time` - 结束时间
    /// * `limit` - 限制返回数量
    pub async fn get_cached_klines(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<i64>,
    ) -> Result<Vec<KlineDto>> {
        let mut all_klines = Vec::new();
        
        // 按天查询缓存数据
        let mut current_date = start_time.date_naive();
        let end_date_naive = end_time.date_naive();

        while current_date <= end_date_naive && 
              (limit.is_none() || all_klines.len() < limit.unwrap() as usize) {
            
            let date_str = current_date.format("%Y%m%d").to_string();
            let redis_key = self.build_redis_key(symbol, &date_str);

            // 计算当天的时间范围
            let day_start = if current_date == start_time.date_naive() {
                start_time.timestamp() as f64
            } else {
                current_date.and_hms_opt(0, 0, 0).unwrap().and_utc().timestamp() as f64
            };

            let day_end = if current_date == end_date_naive {
                end_time.timestamp() as f64
            } else {
                current_date.and_hms_opt(23, 59, 59).unwrap().and_utc().timestamp() as f64
            };

            // 从Redis ZSet查询数据
            match self.redis_ops.zrangebyscore::<&str, Vec<String>>(&redis_key, day_start, day_end) {
                Ok(cached_data) => {
                    for json_str in cached_data.iter() {
                        match self.parse_cached_kline(json_str, symbol) {
                            Ok(kline) => all_klines.push(kline),
                            Err(e) => {
                                log::warn!("解析缓存数据失败: {}", e);
                            }
                        }

                        // 检查是否达到限制
                        if let Some(limit_val) = limit {
                            if all_klines.len() >= limit_val as usize {
                                break;
                            }
                        }
                    }
                }
                Err(e) => {
                    log::debug!("从Redis获取 {} 数据失败: {}", redis_key, e);
                }
            }

            current_date += chrono::Duration::days(1);
        }

        Ok(all_klines)
    }

    /// 更新缓存中的最新数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `new_klines` - 新的K线数据
    pub async fn update_cache_with_new_klines(
        &self,
        symbol: &str,
        new_klines: &[KlineDto],
    ) -> Result<()> {
        if new_klines.is_empty() {
            return Ok(());
        }

        log::debug!("更新 {} 的缓存，新增 {} 条数据", symbol, new_klines.len());

        // 按天分组数据
        let mut klines_by_day: HashMap<String, Vec<&KlineDto>> = HashMap::new();
        
        for kline in new_klines {
            let date_str = kline.time.format("%Y%m%d").to_string();
            klines_by_day.entry(date_str).or_insert_with(Vec::new).push(kline);
        }

        // 更新每天的缓存
        for (date_str, day_klines) in klines_by_day {
            let redis_key = self.build_redis_key(symbol, &date_str);
            
            let mut zset_data: Vec<(f64, String)> = Vec::new();
            
            for kline in day_klines {
                let simple_kline = SimpleKline::new(
                    kline.open_price,
                    kline.high_price,
                    kline.low_price,
                    kline.close_price,
                    kline.volume,
                    kline.time.timestamp(),
                );

                let json_data = simple_kline.to_json()
                    .map_err(|e| MarketDataServiceError::Serialization(e))?;

                let score = kline.time.timestamp() as f64;
                zset_data.push((score, json_data));
            }

            // 添加到Redis ZSet（会自动覆盖相同score的数据）
            self.redis_ops.zadd_multiple(&redis_key, &zset_data)
                .map_err(|e| MarketDataServiceError::Cache(format!("Redis更新失败: {}", e)))?;

            // 更新TTL
            let ttl = self.calculate_ttl_for_date(&date_str)?;
            if ttl > 0 {
                self.redis_ops.expire(&redis_key, ttl as usize)
                    .map_err(|e| MarketDataServiceError::Cache(format!("更新TTL失败: {}", e)))?;
            }
        }

        Ok(())
    }

    /// 清理过期的缓存数据
    pub async fn cleanup_expired_cache(&self) -> Result<i64> {
        log::info!("开始清理过期的缓存数据");
        
        // 获取所有缓存键
        let pattern = format!("{}:*:*", self.exchange_name);
        let keys: Vec<String> = self.redis_ops.keys(&pattern)
            .map_err(|e| MarketDataServiceError::Cache(format!("获取Redis键失败: {}", e)))?;

        let mut deleted_count = 0;
        let one_year_ago = Utc::now() - Duration::days(365);

        for key in keys {
            // 解析日期
            if let Some(date_str) = self.extract_date_from_key(&key) {
                if let Ok(date) = chrono::NaiveDate::parse_from_str(&date_str, "%Y%m%d") {
                    let key_date = date.and_hms_opt(0, 0, 0).unwrap().and_utc();
                    
                    // 如果超过一年，删除键
                    if key_date < one_year_ago {
                        match self.redis_ops.del(&key) {
                            Ok(_) => {
                                deleted_count += 1;
                                log::debug!("删除过期缓存键: {}", key);
                            }
                            Err(e) => {
                                log::warn!("删除缓存键失败 {}: {}", key, e);
                            }
                        }
                    }
                }
            }
        }

        log::info!("清理完成，删除了 {} 个过期缓存键", deleted_count);
        Ok(deleted_count)
    }

    /// 获取缓存状态
    pub async fn get_cache_status(&self, symbol: &str) -> Result<CacheStatus> {
        let pattern = format!("{}:{}:*", self.exchange_name, symbol.to_lowercase());
        let keys: Vec<String> = self.redis_ops.keys(&pattern)
            .map_err(|e| MarketDataServiceError::Cache(format!("获取Redis键失败: {}", e)))?;

        let mut total_size = 0i64;
        let mut earliest_date: Option<DateTime<Utc>> = None;
        let mut latest_date: Option<DateTime<Utc>> = None;

        for key in &keys {
            // 获取ZSet大小
            if let Ok(size) = self.redis_ops.zcard(key) {
                total_size += size;
            }

            // 解析日期
            if let Some(date_str) = self.extract_date_from_key(key) {
                if let Ok(date) = chrono::NaiveDate::parse_from_str(&date_str, "%Y%m%d") {
                    let key_date = date.and_hms_opt(0, 0, 0).unwrap().and_utc();
                    
                    if earliest_date.is_none() || key_date < earliest_date.unwrap() {
                        earliest_date = Some(key_date);
                    }
                    
                    if latest_date.is_none() || key_date > latest_date.unwrap() {
                        latest_date = Some(key_date);
                    }
                }
            }
        }

        Ok(CacheStatus {
            symbol: symbol.to_string(),
            cached_days: keys.len() as i32,
            earliest_cached: earliest_date,
            latest_cached: latest_date,
            cache_size: total_size,
        })
    }

    /// 构建Redis键
    /// 格式: {exchange}:{symbol}:{YYYYMMDD}
    fn build_redis_key(&self, symbol: &str, date_str: &str) -> String {
        format!("{}:{}:{}", self.exchange_name, symbol.to_lowercase(), date_str)
    }

    /// 从Redis键中提取日期
    fn extract_date_from_key(&self, key: &str) -> Option<String> {
        let parts: Vec<&str> = key.split(':').collect();
        if parts.len() >= 3 {
            Some(parts[2].to_string())
        } else {
            None
        }
    }

    /// 计算TTL（秒）
    fn calculate_ttl_for_date(&self, date_str: &str) -> Result<i64> {
        let date = chrono::NaiveDate::parse_from_str(date_str, "%Y%m%d")
            .map_err(|e| MarketDataServiceError::Validation(format!("日期格式错误: {}", e)))?;
        
        let key_date = date.and_hms_opt(23, 59, 59).unwrap().and_utc();
        let now = Utc::now();
        let one_year_from_key_date = key_date + Duration::days(365);

        if one_year_from_key_date <= now {
            // 已经超过一年，应该立即过期
            Ok(1)
        } else {
            // 计算到一年后的剩余秒数
            let remaining_duration = one_year_from_key_date - now;
            Ok(remaining_duration.num_seconds().max(1))
        }
    }

    /// 解析缓存的K线数据
    fn parse_cached_kline(&self, json_str: &str, symbol: &str) -> Result<KlineDto> {
        let simple_kline: SimpleKline = serde_json::from_str(json_str)
            .map_err(|e| MarketDataServiceError::Serialization(e))?;

        let time = DateTime::from_timestamp(simple_kline.e, 0)
            .ok_or_else(|| MarketDataServiceError::Validation("无效的时间戳".to_string()))?;

        Ok(KlineDto {
            time,
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            open_time: time,
            close_time: time + Duration::minutes(1),
            open_price: simple_kline.o.parse()
                .map_err(|e| MarketDataServiceError::Validation(format!("解析开盘价失败: {}", e)))?,
            high_price: simple_kline.h.parse()
                .map_err(|e| MarketDataServiceError::Validation(format!("解析最高价失败: {}", e)))?,
            low_price: simple_kline.l.parse()
                .map_err(|e| MarketDataServiceError::Validation(format!("解析最低价失败: {}", e)))?,
            close_price: simple_kline.c.parse()
                .map_err(|e| MarketDataServiceError::Validation(format!("解析收盘价失败: {}", e)))?,
            volume: simple_kline.v.parse()
                .map_err(|e| MarketDataServiceError::Validation(format!("解析成交量失败: {}", e)))?,
            quote_asset_volume: Default::default(),
            number_of_trades: 0,
            taker_buy_base_asset_volume: Default::default(),
            taker_buy_quote_asset_volume: Default::default(),
        })
    }

    /// 预热缓存 - 为指定交易对加载最近的数据到缓存
    pub async fn warm_up_cache(&self, symbols: &[String]) -> Result<()> {
        log::info!("开始预热缓存，交易对数量: {}", symbols.len());
        
        let end_date = Utc::now();
        let start_date = end_date - Duration::days(30); // 预热最近30天的数据

        for symbol in symbols {
            log::debug!("预热 {} 的缓存", symbol);
            
            if let Err(e) = self.cache_klines_for_symbol(symbol, start_date, Some(end_date)).await {
                log::error!("预热 {} 缓存失败: {}", symbol, e);
            }
        }

        log::info!("缓存预热完成");
        Ok(())
    }
} 