#![allow(warnings)]
#![forbid(unsafe_code)]
pub mod api;
pub mod config;
pub mod router;

use utoipa::OpenApi;

use utoipa::{
    Modify,
    openapi::{
        Components,
        security::{HttpAuthScheme, HttpBuilder, SecurityScheme}
    }
};
use common::domain::r#enum::error::ErrorResponse;
use utoipa::openapi::SecurityRequirement;
use crate::api::auth::{LoginRequest, RegisterRequest, SendVerificationCodeRequest};
use common::domain::dto::user::{LoginUserData, RegisterUserData, UserInfoData};

// OpenAPI 文档定义
#[derive(OpenApi)]
#[openapi(
    paths(
        api::health::health_check,
        api::market_data::get_klines,
        api::market_data::get_tickers,
        api::market_data::get_order_book,
        api::market_data::get_24hr_stats,
        api::market_data::get_trades,
        api::debug::get_data_stats,
        api::debug::get_missing_data,
        api::debug::run_data_maintenance,
        api::auth::send_verification_code,
        api::auth::register,
        api::auth::login,
        api::auth::refresh_token,
        api::auth::get_mine,
        api::auth::get_mine_logs,
        api::maintenance::get_maintenance_status,
        api::maintenance::get_symbol_stats,
        api::maintenance::get_storage_stats,
        api::maintenance::execute_maintenance_action,
        api::maintenance::manage_symbols,
        api::maintenance::get_active_symbols,
    ),
    components(
        schemas(
            // API参数结构体
            api::market_data::KlineParams,
            api::market_data::TickerParams,
            api::market_data::OrderBookParams,
            api::market_data::StatsParams,
            api::market_data::TradeParams,

            // 认证相关结构体 - 新格式
            LoginUserData,
            api::auth::RefreshTokenData,
            UserInfoData,
            RegisterUserData,
            api::auth::SendCodeData,
            api::auth::SecurityLogEntry,
            SendVerificationCodeRequest,
            RegisterRequest,
            LoginRequest,

            // 维护相关结构体
            api::maintenance::MaintenanceStatusResponse,
            api::maintenance::SymbolStatsResponse,
            api::maintenance::StorageStatsResponse,
            api::maintenance::DataTimeRange,
            api::maintenance::MaintenanceActionParams,
            api::maintenance::MaintenanceActionResponse,
            api::maintenance::SymbolManagementRequest,
            api::maintenance::SymbolManagementResponse,

            // DTO响应结构体
            common::domain::dto::KlineDto,
            common::domain::dto::TickerDto,
            common::domain::dto::TradeDto,
            common::domain::dto::OrderBookDto,
            common::domain::dto::Stats24hrDto,
            common::domain::dto::KlineQueryDto,
            common::domain::dto::TickerQueryDto,
            common::domain::dto::OrderBookQueryDto,
            common::domain::dto::StatsQueryDto,
            common::domain::dto::TradeHistoryQueryDto,
            ErrorResponse,
        )
    ),
    tags(
        (name = "health", description = "健康检查接口"),
        (name = "market_data", description = "市场数据接口"),
        (name = "debug", description = "调试和维护接口"),
        (name = "auth", description = "用户认证接口"),
        (name = "maintenance", description = "系统维护接口"),
    ),
    info(
        title = "数据服务器 API",
        version = "1.0.0",
        description = "加密货币市场数据服务器API",
        contact(
            name = "开发团队",
            email = "<EMAIL>"
        )
    ),
    modifiers(&SecurityAddon)
)]
pub struct ApiDoc;

// 定义一个修改器，它会向 OpenAPI 文档中添加 JWT 安全方案
struct SecurityAddon;

impl Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        // an easy way to add security scheme
        // 1. unwrap the components object from openapi spec.
        // 2. add a new security scheme component.
        // 3. add the security scheme to the openapi spec.
        if let Some(components) = openapi.components.as_mut() {
            components.add_security_scheme(
                "bearer_auth", //  给这个安全方案起一个名字，后面会用到
                SecurityScheme::Http(
                    HttpBuilder::new()
                        .scheme(HttpAuthScheme::Bearer)
                        .bearer_format("Bearer ")
                        .build(),
                ),
            )
        }
    }
}

