use axum::{
    Router,
    extract::State,
    response::Json,
    routing::{get, post},
};

use std::sync::Arc;
// 导入其他模块
use common::domain::r#enum::error::AppError;
use common::domain::r#enum::error::ErrorResponse;
use crate::router::AppState;


/// 获取数据统计信息
#[utoipa::path(
    get,
    path = "/api/v1/debug/stats",
    responses(
        (status = 200, description = "成功获取数据统计", body = serde_json::Value),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "debug"
)]
pub async fn get_data_stats(State(state): State<AppState>) -> Result<Json<serde_json::Value>, AppError> {
    // 由于service库中的DataMaintenanceService没有get_data_stats方法，我们返回一个占位符
    let stats = serde_json::json!({
        "message": "数据统计功能待实现",
        "timestamp": chrono::Utc::now()
    });
    Ok(Json(stats))
}

/// 获取缺失数据信息
#[utoipa::path(
    get,
    path = "/api/v1/debug/missing",
    responses(
        (status = 200, description = "成功获取缺失数据信息", body = serde_json::Value),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "debug"
)]
pub async fn get_missing_data(State(state): State<AppState>) -> Result<Json<serde_json::Value>, AppError> {
    // 维护服务现在在独立线程中运行，通过维护API访问相关功能
    let info = serde_json::json!({
        "message": "缺失数据检测功能已迁移至维护API",
        "hint": "请使用 /api/v1/maintenance/storage/stats 获取存储统计信息",
        "note": "维护服务现在在独立后台线程中运行",
        "timestamp": chrono::Utc::now()
    });
    Ok(Json(info))
}

/// 手动执行数据维护
#[utoipa::path(
    post,
    path = "/api/v1/debug/maintenance",
    responses(
        (status = 200, description = "数据维护任务已开始", body = serde_json::Value),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "debug"
)]
pub async fn run_data_maintenance(State(state): State<AppState>) -> Result<Json<serde_json::Value>, AppError> {
    // 维护服务现在在独立线程中运行，通过维护API触发操作
    let result = serde_json::json!({
        "message": "数据维护功能已迁移至维护API",
        "hint": "请使用 POST /api/v1/maintenance/actions 触发维护操作",
        "available_actions": ["full_scan", "incremental_scan", "cleanup"],
        "note": "维护服务现在在独立后台线程中运行",
        "timestamp": chrono::Utc::now()
    });

    Ok(Json(result))
}

pub fn debug_routes() ->  Router<AppState> {
    Router::new()
        .route("/stats", get(get_data_stats))
        .route("/missing", get(get_missing_data))
        .route("/maintenance", post(run_data_maintenance))
}
