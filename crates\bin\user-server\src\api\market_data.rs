use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use chrono::DateTime;
use common::domain::dto::*;

use common::domain::r#enum::error::AppError;
use common::domain::r#enum::error::ErrorResponse;
use crate::router::AppState;
use utoipa::IntoParams;

#[derive(serde::Deserialize, IntoParams, utoipa::ToSchema)]
pub struct KlineParams {
    /// 交易对
    pub symbol: String,
    /// 时间间隔
    pub interval: String,
    /// 开始时间戳（毫秒）
    pub start_time: Option<i64>,
    /// 结束时间戳（毫秒）
    pub end_time: Option<i64>,
    /// 限制数量
    pub limit: Option<i64>,
}

#[derive(serde::Deserialize, IntoParams, utoipa::ToSchema)]
pub struct TickerParams {
    /// 交易对（可选，不指定则返回所有）
    pub symbol: Option<String>,
}

#[derive(serde::Deserialize, IntoParams, utoipa::ToSchema)]
pub struct OrderBookParams {
    /// 交易对
    pub symbol: String,
    /// 限制数量
    pub limit: Option<i32>,
}

#[derive(serde::Deserialize, IntoParams, utoipa::ToSchema)]
pub struct StatsParams {
    /// 交易对（可选，不指定则返回所有）
    pub symbol: Option<String>,
}

#[derive(serde::Deserialize, IntoParams, utoipa::ToSchema)]
pub struct TradeParams {
    /// 交易对
    pub symbol: String,
    /// 开始ID
    pub from_id: Option<i64>,
    /// 限制数量
    pub limit: Option<i64>,
}

/// 获取K线数据
#[utoipa::path(
    get,
    path = "/api/v1/market/klines",
    params(KlineParams),
    responses(
        (status = 200, description = "成功获取K线数据", body = Vec<KlineDto>),
        (status = 400, description = "参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "market_data"
)]
pub async fn get_klines(
    State(state): State<AppState>,
    Query(params): Query<KlineParams>,
) -> Result<Json<Vec<KlineDto>>, AppError> {
    let query = KlineQueryDto {
        symbol: params.symbol,
        interval: params.interval,
        start_time: params.start_time.map(|ts| DateTime::from_timestamp_millis(ts).unwrap_or_default()),
        end_time: params.end_time.map(|ts| DateTime::from_timestamp_millis(ts).unwrap_or_default()),
        limit: params.limit,
    };

    let klines = state.market_data_service
        .get_kline_data(query)
        .await
        .map_err(|e| AppError::Internal(e.to_string()))?;

    let dtos: Vec<KlineDto> = klines.into_iter().map(|entity| entity.into()).collect();
    Ok(Json(dtos))
}

/// 获取价格数据
#[utoipa::path(
    get,
    path = "/api/v1/market/tickers",
    params(TickerParams),
    responses(
        (status = 200, description = "成功获取价格数据", body = Vec<TickerDto>),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "market_data"
)]
pub async fn get_tickers(
    State(state): State<AppState>,
    Query(params): Query<TickerParams>,
) -> Result<Json<Vec<TickerDto>>, AppError> {
    let query = TickerQueryDto {
        symbol: params.symbol,
    };

    let tickers = state.market_data_service
        .get_ticker_price(query)
        .await
        .map_err(|e| AppError::Internal(e.to_string()))?;

    let dtos: Vec<TickerDto> = tickers.into_iter().map(|entity| entity.into()).collect();
    Ok(Json(dtos))
}

/// 获取深度数据
#[utoipa::path(
    get,
    path = "/api/v1/market/orderbook",
    params(OrderBookParams),
    responses(
        (status = 200, description = "成功获取深度数据", body = OrderBookDto),
        (status = 400, description = "参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "market_data"
)]
pub async fn get_order_book(
    State(state): State<AppState>,
    Query(params): Query<OrderBookParams>,
) -> Result<Json<OrderBookDto>, AppError> {
    let query = OrderBookQueryDto {
        symbol: params.symbol,
        limit: params.limit,
    };

    let order_book = state.market_data_service
        .get_order_book(query)
        .await
        .map_err(|e| AppError::Internal(e.to_string()))?;

    let dto: OrderBookDto = order_book.into();
    Ok(Json(dto))
}

/// 获取24小时统计数据
#[utoipa::path(
    get,
    path = "/api/v1/market/stats24hr",
    params(StatsParams),
    responses(
        (status = 200, description = "成功获取24小时统计数据", body = Vec<Stats24hrDto>),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "market_data"
)]
pub async fn get_24hr_stats(
    State(state): State<AppState>,
    Query(params): Query<StatsParams>,
) -> Result<Json<Vec<Stats24hrDto>>, AppError> {
    let query = StatsQueryDto {
        symbol: params.symbol,
    };

    let stats = state.market_data_service
        .get_24hr_stats(query)
        .await
        .map_err(|e| AppError::Internal(e.to_string()))?;

    
    let dtos: Vec<Stats24hrDto> = stats.into_iter().map(|entity| entity.into()).collect();
    Ok(Json(dtos))
}

/// 获取交易历史数据
#[utoipa::path(
    get,
    path = "/api/v1/market/trades",
    params(TradeParams),
    responses(
        (status = 200, description = "成功获取交易历史数据", body = Vec<TradeDto>),
        (status = 400, description = "参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "market_data"
)]
pub async fn get_trades(
    State(state): State<AppState>,
    Query(params): Query<TradeParams>,
) -> Result<Json<Vec<TradeDto>>, AppError> {
    let query = TradeHistoryQueryDto {
        symbol: params.symbol,
        from_id: params.from_id,
        start_time: None,
        end_time: None,
        limit: params.limit,
    };

    let trades = state.market_data_service
        .get_trade_history(query)
        .await
        .map_err(|e| AppError::Internal(e.to_string()))?;

    let dtos: Vec<TradeDto> = trades.into_iter().map(|entity| entity.into()).collect();
    Ok(Json(dtos))
}

pub fn market_data_routes() ->  Router<AppState> {
    Router::new()
        .route("/klines", get(get_klines))
        .route("/tickers", get(get_tickers))
        .route("/orderbook", get(get_order_book))
        .route("/stats24hr", get(get_24hr_stats))
        .route("/trades", get(get_trades))
        
} 