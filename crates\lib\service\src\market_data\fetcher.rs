use chrono::{DateTime, Duration as ChronoDuration, Utc};
use std::time::Duration;
use exchange::{binance::BinanceClient, ExchangeClient, KlineQuery};
use repository::{
    connection::timescale::TimescalePoolManager,
    timescale::{CreateMarketDataRequest, KlineRepository},
};

// 移除重复的导入
use common::domain::dto::KlineDto;
use crate::market_data::KlineInterval;

const BINANCE_API_LIMIT: i64 = 1000;
const API_REQUEST_TIMEOUT_SECS: u64 = 60;
const MAX_FETCH_RETRIES: u32 = 3;

/// 数据获取器
#[derive(Clone)]
pub struct DataFetcher {
    kline_repo: KlineRepository,
    binance_client: BinanceClient,
}

impl DataFetcher {
    pub fn new(pool: &TimescalePoolManager, binance_client: BinanceClient) -> Self {
        Self {
            kline_repo: KlineRepository::new(pool.pool().clone()),
            binance_client,
        }
    }

    /// 填充指定交易对和时间间隔的缺失数据
    pub async fn fill_missing_data(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Result<u64> {
        if missing_periods.is_empty() {
            return Ok(0);
        }

        log::info!(
            "开始为 {} {} 填充 {} 个缺失时间段的数据",
            symbol,
            interval,
            missing_periods.len()
        );

        // 1. 预处理时间段并创建智能批次
        let batches = self.create_intelligent_batches(interval, missing_periods);
        let total_batches = batches.len();
        log::info!("智能分批完成，共创建 {} 个批次", total_batches);

        let mut total_klines_saved = 0_u64;

        // 2. 遍历批次并获取数据
        for (i, batch) in batches.iter().enumerate() {
            log::info!("处理第 {}/{} 批次: {} to {}", i + 1, total_batches, batch.start, batch.end);

            match self.fetch_and_save_batch(symbol, interval, batch).await {
                Ok(saved_count) => {
                    total_klines_saved += saved_count;
                    log::debug!("批次 {}/{} 成功，保存了 {} 条K线", i + 1, total_batches, saved_count);
                }
                Err(e) => {
                    log::error!(
                        "处理批次 {}/{} ({} to {}) 失败: {}",
                        i + 1,
                        total_batches,
                        batch.start,
                        batch.end,
                        e
                    );
                    // 单个批次失败不中断整个流程，但会记录错误
                }
            }
             // 批次之间稍长的延迟，给API服务器缓冲时间
            if i < total_batches - 1 {
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            }
        }

        log::info!(
            "{} {} 数据填充完成，共保存了 {} 条K线",
            symbol,
            interval,
            total_klines_saved
        );

        Ok(total_klines_saved)
    }

    /// 获取并保存单个批次的数据
    async fn fetch_and_save_batch(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        batch: &FetchBatch,
    ) -> Result<u64> {
        // 从Binance API获取K线数据
        let klines = self
            .fetch_with_retry(symbol, interval, batch.start, batch.end)
            .await?;

        if klines.is_empty() {
            return Ok(0);
        }

        // 转换为数据库格式并保存
        let requests: Vec<CreateMarketDataRequest> = klines
            .into_iter()
            .map(|kline| self.kline_dto_to_request(kline, interval))
            .collect();
        
        let saved_count = self.kline_repo.batch_insert(requests).await?;
        
        Ok(saved_count)
    }

    /// 带重试机制的获取逻辑
    async fn fetch_with_retry(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<KlineDto>> {
        let mut retries = 0;
        loop {
            let query = KlineQuery {
                symbol: symbol.to_string(),
                interval: interval.to_binance_string().to_string(),
                start_time: Some(start_time.timestamp_millis()),
                end_time: Some(end_time.timestamp_millis()),
                limit: Some(BINANCE_API_LIMIT as i32),
            };

            let fetch_future = self.binance_client.get_klines(query);

            match tokio::time::timeout(Duration::from_secs(API_REQUEST_TIMEOUT_SECS), fetch_future).await {
                Ok(Ok(klines)) => {
                    let kline_dtos = klines.into_iter()
                        .filter_map(|k| self.convert_universal_kline_to_dto(k, symbol, interval.to_binance_string()).ok())
                        .collect();
                    return Ok(kline_dtos);
                },
                Ok(Err(e)) => { // API client error
                    retries += 1;
                    if retries > MAX_FETCH_RETRIES {
                        return Err(MarketDataServiceError::Api(format!("获取失败 (重试 {} 次后): {}", MAX_FETCH_RETRIES, e)));
                    }
                    log::warn!("API调用失败 (重试 {}/{}): {}", retries, MAX_FETCH_RETRIES, e);
                    tokio::time::sleep(Duration::from_millis(1000 * retries as u64)).await;
                }
                Err(_) => { // Timeout error
                    retries += 1;
                    if retries > MAX_FETCH_RETRIES {
                        return Err(MarketDataServiceError::Network("API调用超时".to_string()));
                    }
                     log::warn!("API调用超时 (重试 {}/{})", retries, MAX_FETCH_RETRIES);
                    tokio::time::sleep(Duration::from_millis(1000 * retries as u64)).await;
                }
            }
        }
    }

    /// 创建智能分批
    fn create_intelligent_batches(
        &self,
        interval: &KlineInterval,
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Vec<FetchBatch> {
        if missing_periods.is_empty() {
            return Vec::new();
        }

        let mut batches = Vec::new();
        let interval_duration = ChronoDuration::minutes(interval.to_minutes());

        // 核心修正：独立处理每一个缺失的时间段
        for &(period_start, period_end) in missing_periods {
            let mut current_start = period_start;
            
            // 为当前这一个时间段创建批次
            while current_start < period_end {
                // 计算这个批次可以获取多少个间隔的数据
                let num_klines_in_batch = BINANCE_API_LIMIT;
                let batch_duration = interval_duration * num_klines_in_batch as i32;
                let mut current_end = current_start + batch_duration;

                if current_end > period_end {
                    current_end = period_end;
                }
                
                // 只有当批次有实际时间跨度时才添加
                if current_start < current_end {
                    batches.push(FetchBatch {
                        start: current_start,
                        end: current_end,
                    });
                }
                
                current_start = current_end;
            }
        }

        batches
    }

    fn convert_universal_kline_to_dto(
        &self,
        kline: exchange::UniversalKline,
        symbol: &str,
        interval_str: &str,
    ) -> Result<KlineDto> {
        let open_time = DateTime::from_timestamp_millis(kline.open_time)
            .ok_or_else(|| MarketDataServiceError::DataConversion("无效的开盘时间".to_string()))?;
        let close_time = DateTime::from_timestamp_millis(kline.close_time)
            .ok_or_else(|| MarketDataServiceError::DataConversion("无效的收盘时间".to_string()))?;

        Ok(KlineDto {
            time: open_time,
            symbol: symbol.to_string(),
            interval: interval_str.to_string(),
            open_time,
            close_time,
            open_price: kline.open_price.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("开盘价解析失败: {}", e)))?,
            high_price: kline.high_price.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("最高价解析失败: {}", e)))?,
            low_price: kline.low_price.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("最低价解析失败: {}", e)))?,
            close_price: kline.close_price.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("收盘价解析失败: {}", e)))?,
            volume: kline.volume.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("成交量解析失败: {}", e)))?,
            quote_asset_volume: kline.quote_asset_volume.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("报价资产成交量解析失败: {}", e)))?,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("主动买入基础资产成交量解析失败: {}", e)))?,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse().map_err(|e| MarketDataServiceError::DataConversion(format!("主动买入报价资产成交量解析失败: {}", e)))?,
        })
    }

    fn kline_dto_to_request(&self, kline: KlineDto, interval: &KlineInterval) -> CreateMarketDataRequest {
        CreateMarketDataRequest {
            symbol: kline.symbol,
            timestamp: kline.time,
            open: kline.open_price,
            high: kline.high_price,
            low: kline.low_price,
            close: kline.close_price,
            volume: kline.volume,
            interval_type: interval.to_binance_string().to_string(),
            quote_asset_volume: kline.quote_asset_volume,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            close_time: kline.close_time,
        }
    }
}

/// 表示一个获取任务的批次
struct FetchBatch {
    start: DateTime<Utc>,
    end: DateTime<Utc>,
}
