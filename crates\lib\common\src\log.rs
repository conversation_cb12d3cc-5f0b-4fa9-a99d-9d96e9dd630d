use dotenvy::dotenv;
use log::LevelFilter;
use log4rs::{
    append::{
        console::ConsoleAppender,
        rolling_file::{
            RollingFileAppender,
            policy::compound::{CompoundPolicy, roll::fixed_window::FixedWindowRoller, trigger::size::SizeTrigger},
        },
    },
    config::{Appe<PERSON>, Config, Lo<PERSON>, Root},
    encode::pattern::PatternEncoder,
};
use anyhow::Result;
use std::fs;
use std::str::FromStr;


pub const API_PREFIX: &str = "/backtest";



const SIMPLE_PATTERN: &str = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} {f}:{L} - {m}{n}";

pub fn init_simple_logging() -> Result<()> {
    dotenv().ok();
    let level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    // 尝试将命令行参数字符串解析为 LevelFilter
    let log_level = match LevelFilter::from_str(&level.to_lowercase()) {
        Ok(level) => {
            // 解析成功
            level
        }
        Err(_) => {
            eprintln!("Warning: Invalid log level '{}' provided. Defaulting to Info.", level.to_lowercase());
            // 提供一个默认的日志级别，例如 LevelFilter::Info
            LevelFilter::Info
        }
    };
    fs::create_dir_all("log")?;

    let stdout_appender = ConsoleAppender::builder().encoder(Box::new(PatternEncoder::new(SIMPLE_PATTERN))).build();

    let file_policy = CompoundPolicy::new(
        Box::new(SizeTrigger::new(100 * 1024 * 1024)),                      // 100MB per file
        Box::new(FixedWindowRoller::builder().build("log/app.log.{}", 5)?), // Keep 5 old files, no gzip
    );

    let file_appender = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(SIMPLE_PATTERN)))
        .build("log/app.log", Box::new(file_policy))?;

    let config = Config::builder()
        .appender(Appender::builder().build("stdout", Box::new(stdout_appender)))
        .appender(Appender::builder().build("file_log", Box::new(file_appender)))
        .logger(Logger::builder().build("app::backend::db", LevelFilter::Info)) // Example custom logger
        .build(Root::builder().appender("stdout").appender("file_log").build(log_level))?;
    log4rs::init_config(config)?;

    Ok(())
}
