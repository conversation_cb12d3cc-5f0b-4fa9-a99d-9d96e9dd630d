#![allow(warnings)]

// Repository层 - 简化版SQLx结构优化
// 专注于PostgreSQL + TimescaleDB组合，提取和重用数据库操作代码

pub mod postgres;     // PostgreSQL相关操作
pub mod timescale;    // TimescaleDB相关操作
pub mod connection;   // 连接池管理
pub mod error;        // 错误处理
pub mod cache;        // 缓存层


// 重新导出主要类型
pub use error::{RepositoryError, Result};

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}
