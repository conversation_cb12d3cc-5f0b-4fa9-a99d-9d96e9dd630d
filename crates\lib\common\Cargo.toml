[package]
name = "common"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true

[dependencies]

bigdecimal.workspace = true
validator.workspace = true
uuid.workspace = true
thiserror.workspace = true
chrono.workspace = true
serde.workspace = true
rand.workspace = true
rs-snowflake.workspace = true
anyhow.workspace = true
tempfile.workspace = true
structopt.workspace = true

rand_distr.workspace = true
axum.workspace = true

log.workspace = true
log4rs.workspace = true
dotenvy.workspace = true

rust_decimal.workspace = true
sqlx.workspace = true

serde_json.workspace = true

utoipa.workspace = true


jsonwebtoken.workspace = true

[dev-dependencies]
tokio = { workspace = true, features = ["test-util"] }