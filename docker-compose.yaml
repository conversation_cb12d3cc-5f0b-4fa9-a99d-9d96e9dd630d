services:
  data_server:
    build:
      context: .
      dockerfile: ./Dockerfile
      target: data-grpc
    container_name: data-grpc
    restart: always
    logging:
      driver: json-file
      options:
        max-size: 500m
    ports:
      - "50051:50051"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    environment:
      - NACOS_SERVER_ADDR=***********:8848
      #- NACOS_NAMESPACE=
      - NACOS_GROUP=DEFAULT_GROUP
      - NACOS_USERNAME=admin
      - NACOS_PASSWORD=admin
      - NACOS_SERVICE_NAME=trade-server
      - NACOS_CLIENT_IP=***********
      - NACOS_CLIENT_PORT=8091
      - LOG_LEVEL=info


  data_http:
    build:
      context: .
      dockerfile: ./Dockerfile
      target: data-http
    container_name: data-http
    restart: always
    logging:
      driver: json-file
      options:
        max-size: 500m
    ports:
      - "8091:8091"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    environment:
      - REDIS_HOST=***********
      - REDIS_PORT=16379
      - REDIS_PASSWORD=123456


  dcaut-runner:
    build:
      context: .
      dockerfile: ./Dockerfile
      target: dcaut-runner
    container_name: dcaut-runner
    restart: always
    logging:
      driver: json-file
      options:
        max-size: 500m
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro