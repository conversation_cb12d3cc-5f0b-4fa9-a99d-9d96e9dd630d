-- =====================================================
-- 数据完整性审计和延迟监控查询
-- =====================================================

-- 1. 数据完整性审计 - 使用time_bucket_gapfill发现数据空洞
-- 检查指定交易对在指定时间范围内的数据完整性
CREATE OR REPLACE FUNCTION check_kline_data_integrity(
    p_symbol TEXT,
    p_exchange TEXT DEFAULT 'binance',
    p_start_time TIMESTAMPTZ DEFAULT NOW() - INTERVAL '7 days',
    p_end_time TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    time_bucket TIMESTAMPTZ,
    symbol TEXT,
    exchange TEXT,
    data_count BIGINT,
    is_missing BOOLEAN,
    gap_duration_minutes INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH expected_buckets AS (
        SELECT 
            time_bucket_gapfill('1 minute', time, p_start_time, p_end_time) AS time_bucket,
            p_symbol AS symbol,
            p_exchange AS exchange,
            COUNT(*) AS data_count
        FROM kline_1m k
        WHERE k.symbol = p_symbol 
          AND k.exchange = p_exchange
          AND k.time >= p_start_time 
          AND k.time <= p_end_time
        GROUP BY time_bucket_gapfill('1 minute', time, p_start_time, p_end_time)
        ORDER BY time_bucket
    ),
    gap_analysis AS (
        SELECT 
            eb.time_bucket,
            eb.symbol,
            eb.exchange,
            eb.data_count,
            CASE WHEN eb.data_count = 0 THEN TRUE ELSE FALSE END AS is_missing,
            CASE 
                WHEN eb.data_count = 0 THEN 
                    EXTRACT(EPOCH FROM (
                        LEAD(eb.time_bucket) OVER (ORDER BY eb.time_bucket) - eb.time_bucket
                    ))::INTEGER / 60
                ELSE 0 
            END AS gap_duration_minutes
        FROM expected_buckets eb
    )
    SELECT * FROM gap_analysis WHERE is_missing = TRUE;
END;
$$ LANGUAGE plpgsql;

-- 2. 批量数据完整性检查 - 检查所有活跃交易对
CREATE OR REPLACE FUNCTION check_all_symbols_integrity(
    p_exchange TEXT DEFAULT 'binance',
    p_hours_back INTEGER DEFAULT 24
)
RETURNS TABLE (
    symbol TEXT,
    exchange TEXT,
    total_expected_minutes BIGINT,
    actual_data_points BIGINT,
    missing_data_points BIGINT,
    completeness_percentage NUMERIC(5,2),
    latest_data_time TIMESTAMPTZ,
    data_lag_minutes INTEGER
) AS $$
DECLARE
    start_time TIMESTAMPTZ := NOW() - (p_hours_back || ' hours')::INTERVAL;
    end_time TIMESTAMPTZ := NOW();
BEGIN
    RETURN QUERY
    WITH active_symbols AS (
        SELECT DISTINCT k.symbol, k.exchange
        FROM kline_1m k
        WHERE k.exchange = p_exchange
          AND k.time >= start_time - INTERVAL '1 hour'  -- 稍微扩大范围确保获取活跃交易对
    ),
    symbol_stats AS (
        SELECT 
            s.symbol,
            s.exchange,
            EXTRACT(EPOCH FROM (end_time - start_time))::BIGINT / 60 AS total_expected_minutes,
            COUNT(k.time) AS actual_data_points,
            MAX(k.time) AS latest_data_time
        FROM active_symbols s
        LEFT JOIN kline_1m k ON s.symbol = k.symbol 
                             AND s.exchange = k.exchange
                             AND k.time >= start_time 
                             AND k.time <= end_time
        GROUP BY s.symbol, s.exchange
    )
    SELECT 
        ss.symbol,
        ss.exchange,
        ss.total_expected_minutes,
        ss.actual_data_points,
        ss.total_expected_minutes - ss.actual_data_points AS missing_data_points,
        ROUND((ss.actual_data_points::NUMERIC / ss.total_expected_minutes::NUMERIC) * 100, 2) AS completeness_percentage,
        ss.latest_data_time,
        EXTRACT(EPOCH FROM (NOW() - ss.latest_data_time))::INTEGER / 60 AS data_lag_minutes
    FROM symbol_stats ss
    ORDER BY completeness_percentage ASC, ss.symbol;
END;
$$ LANGUAGE plpgsql;

-- 3. 延迟监控查询 - 监控每个交易对的数据延迟
CREATE OR REPLACE VIEW v_kline_data_lag AS
SELECT 
    symbol,
    exchange,
    MAX(time) AS latest_data_time,
    NOW() AS current_time,
    EXTRACT(EPOCH FROM (NOW() - MAX(time)))::INTEGER / 60 AS lag_minutes,
    CASE 
        WHEN EXTRACT(EPOCH FROM (NOW() - MAX(time))) / 60 <= 5 THEN 'HEALTHY'
        WHEN EXTRACT(EPOCH FROM (NOW() - MAX(time))) / 60 <= 15 THEN 'WARNING'
        ELSE 'CRITICAL'
    END AS lag_status,
    COUNT(*) FILTER (WHERE time >= NOW() - INTERVAL '1 hour') AS data_points_last_hour,
    COUNT(*) FILTER (WHERE time >= NOW() - INTERVAL '24 hours') AS data_points_last_24h
FROM kline_1m
WHERE time >= NOW() - INTERVAL '24 hours'
GROUP BY symbol, exchange
ORDER BY lag_minutes DESC;

-- 4. 数据质量检查 - 检查异常数据
CREATE OR REPLACE FUNCTION check_data_quality(
    p_symbol TEXT,
    p_exchange TEXT DEFAULT 'binance',
    p_hours_back INTEGER DEFAULT 24
)
RETURNS TABLE (
    time TIMESTAMPTZ,
    symbol TEXT,
    issue_type TEXT,
    issue_description TEXT,
    open_price DECIMAL,
    high_price DECIMAL,
    low_price DECIMAL,
    close_price DECIMAL,
    volume DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        k.time,
        k.symbol,
        'PRICE_ANOMALY' AS issue_type,
        CASE 
            WHEN k.high_price < k.low_price THEN 'High price less than low price'
            WHEN k.open_price > k.high_price OR k.open_price < k.low_price THEN 'Open price outside high-low range'
            WHEN k.close_price > k.high_price OR k.close_price < k.low_price THEN 'Close price outside high-low range'
            WHEN k.high_price = 0 OR k.low_price = 0 OR k.open_price = 0 OR k.close_price = 0 THEN 'Zero price detected'
            ELSE 'Unknown price anomaly'
        END AS issue_description,
        k.open_price,
        k.high_price,
        k.low_price,
        k.close_price,
        k.volume
    FROM kline_1m k
    WHERE k.symbol = p_symbol
      AND k.exchange = p_exchange
      AND k.time >= NOW() - (p_hours_back || ' hours')::INTERVAL
      AND (
          k.high_price < k.low_price
          OR k.open_price > k.high_price 
          OR k.open_price < k.low_price
          OR k.close_price > k.high_price 
          OR k.close_price < k.low_price
          OR k.high_price = 0 
          OR k.low_price = 0 
          OR k.open_price = 0 
          OR k.close_price = 0
      )
    
    UNION ALL
    
    SELECT 
        k.time,
        k.symbol,
        'VOLUME_ANOMALY' AS issue_type,
        CASE 
            WHEN k.volume < 0 THEN 'Negative volume'
            WHEN k.quote_volume < 0 THEN 'Negative quote volume'
            WHEN k.trades_count <= 0 THEN 'Zero or negative trades count'
            ELSE 'Unknown volume anomaly'
        END AS issue_description,
        k.open_price,
        k.high_price,
        k.low_price,
        k.close_price,
        k.volume
    FROM kline_1m k
    WHERE k.symbol = p_symbol
      AND k.exchange = p_exchange
      AND k.time >= NOW() - (p_hours_back || ' hours')::INTERVAL
      AND (
          k.volume < 0
          OR k.quote_volume < 0
          OR k.trades_count <= 0
      )
    ORDER BY time DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. 聚合视图数据一致性检查
CREATE OR REPLACE FUNCTION check_aggregation_consistency(
    p_symbol TEXT,
    p_exchange TEXT DEFAULT 'binance',
    p_date DATE DEFAULT CURRENT_DATE - 1
)
RETURNS TABLE (
    time_period TEXT,
    source_count BIGINT,
    aggregate_count BIGINT,
    is_consistent BOOLEAN,
    variance_percentage NUMERIC(5,2)
) AS $$
DECLARE
    start_time TIMESTAMPTZ := p_date::TIMESTAMPTZ;
    end_time TIMESTAMPTZ := (p_date + 1)::TIMESTAMPTZ;
BEGIN
    RETURN QUERY
    -- 检查5分钟聚合一致性
    WITH source_5m AS (
        SELECT COUNT(*) as cnt
        FROM kline_1m 
        WHERE symbol = p_symbol AND exchange = p_exchange 
          AND time >= start_time AND time < end_time
    ),
    agg_5m AS (
        SELECT COUNT(*) * 5 as cnt  -- 5分钟聚合，每个聚合点代表5个1分钟数据点
        FROM kline_5m 
        WHERE symbol = p_symbol AND exchange = p_exchange 
          AND time >= start_time AND time < end_time
    )
    SELECT 
        '5m' AS time_period,
        s.cnt AS source_count,
        a.cnt AS aggregate_count,
        s.cnt = a.cnt AS is_consistent,
        CASE WHEN s.cnt > 0 THEN ABS(s.cnt - a.cnt)::NUMERIC / s.cnt * 100 ELSE 0 END AS variance_percentage
    FROM source_5m s, agg_5m a
    
    UNION ALL
    
    -- 检查1小时聚合一致性
    WITH source_1h AS (
        SELECT COUNT(*) as cnt
        FROM kline_1m 
        WHERE symbol = p_symbol AND exchange = p_exchange 
          AND time >= start_time AND time < end_time
    ),
    agg_1h AS (
        SELECT COUNT(*) * 60 as cnt  -- 1小时聚合，每个聚合点代表60个1分钟数据点
        FROM kline_1h 
        WHERE symbol = p_symbol AND exchange = p_exchange 
          AND time >= start_time AND time < end_time
    )
    SELECT 
        '1h' AS time_period,
        s.cnt AS source_count,
        a.cnt AS aggregate_count,
        s.cnt = a.cnt AS is_consistent,
        CASE WHEN s.cnt > 0 THEN ABS(s.cnt - a.cnt)::NUMERIC / s.cnt * 100 ELSE 0 END AS variance_percentage
    FROM source_1h s, agg_1h a;
END;
$$ LANGUAGE plpgsql;
