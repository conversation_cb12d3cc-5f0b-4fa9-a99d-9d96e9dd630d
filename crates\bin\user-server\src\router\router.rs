use crate::ApiDoc;
use crate::api::create_api_v1_router;
use crate::router::UserServerState;
use axum::Router;
use firebase_auth::FirebaseAuth;
use service::initialize_and_start_app_state;
use tower_http::compression::CompressionLayer;
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

/// 初始化应用
pub async fn init_app() -> Router {
    // --- 1. 使用统一的初始化方式创建service层的AppState ---
    let service_state = initialize_and_start_app_state().await
        .expect("Failed to initialize and start service app state");

    // --- 2. 创建user-server特有的服务实例 ---

    // 初始化 FirebaseAuth
    let firebase_project_id = std::env::var("FIREBASE_PROJECT_ID")
        .expect("需要设置 FIREBASE_PROJECT_ID 环境变量");
    let firebase_auth = FirebaseAuth::new(&firebase_project_id).await;

    // --- 3. 创建user-server的状态 ---
    let app_state = UserServerState {
        service_state: service_state.clone(),
        firebase_auth,
    };

    // --- 4. 构建路由 ---
    let api_v1_router = create_api_v1_router();

    // 构建应用
    Router::new()
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .nest("/api", api_v1_router)
        .layer(TraceLayer::new_for_http())
        .layer(CompressionLayer::new())
        .layer(CorsLayer::permissive())
        .with_state(app_state)
}
