use crate::ApiDoc;
use crate::api::create_api_v1_router;
use crate::api::debug::debug_routes;
use crate::api::health::health_routes;
use crate::api::market_data::market_data_routes;
use crate::router::{UserServerState, AppState};
use crate::router::auth_jwt::auth_middleware;
use axum::{
    Router,
    http::Method,
    routing::{get, post},
};
use firebase_auth::FirebaseAuth;
// 移除旧的DataMaintenanceService依赖
// use service::data_service::DataMaintenanceService;
use service::{ initialize_app_state, AppInitializer};
use repository::connection::timescale::TimescalePoolManager;
use repository::connection::redis::{RedisPoolManager, RedisOperations};
use repository::{CacheRepository, RedisCacheRepository};
use common::{TRADERDATABASE_CONFIG, REDIS_CONFIG};

use std::sync::Arc;
use tower_http::compression::CompressionLayer;
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;

use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;
use service::market_data::MarketDataCrudService;

/// 初始化应用
pub async fn init_app() -> Router {
    // --- 1. 使用统一的初始化方式创建service层的AppState ---
    let service_state = initialize_app_state().await
        .expect("Failed to initialize service app state");

    // --- 2. 创建user-server特有的服务实例 ---

    // 初始化 FirebaseAuth
    let firebase_project_id = std::env::var("FIREBASE_PROJECT_ID")
        .expect("需要设置 FIREBASE_PROJECT_ID 环境变量");
    let firebase_auth = FirebaseAuth::new(&firebase_project_id).await;

    // 创建市场数据CRUD服务
    let market_data_service = create_market_data_crud_service().await
        .expect("Failed to create market data CRUD service");

    // --- 3. 创建user-server的状态 ---
    let app_state = UserServerState {
        service_state: service_state.clone(),
        firebase_auth,
        market_data_service: Arc::new(market_data_service),
    };

    // --- 4. 构建路由 ---
    let api_v1_router = create_api_v1_router();

    // 构建应用
    Router::new()
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .nest("/api", api_v1_router)
        .layer(TraceLayer::new_for_http())
        .layer(CompressionLayer::new())
        .layer(CorsLayer::permissive())
        .with_state(app_state)
}

/// 创建市场数据CRUD服务
async fn create_market_data_crud_service() -> anyhow::Result<MarketDataCrudService> {
    // 等待配置可用
    let timescale_config = TRADERDATABASE_CONFIG.get()
        .ok_or_else(|| anyhow::anyhow!("TimescaleDB配置未初始化"))?;

    let redis_config = REDIS_CONFIG.get()
        .ok_or_else(|| anyhow::anyhow!("Redis配置未初始化"))?;

    // 创建连接池
    let timescale_pool = TimescalePoolManager::new(timescale_config.clone()).await?;

    // 创建Redis缓存仓库
    let redis_pool_manager = RedisPoolManager::new(redis_config.clone()).await?;
    let redis_ops = RedisOperations::new(redis_pool_manager);
    let cache_repo = Arc::new(RedisCacheRepository::new(redis_ops)) as Arc<dyn CacheRepository>;

    // 创建CRUD服务
    MarketDataCrudService::new(timescale_pool, cache_repo).await
}
