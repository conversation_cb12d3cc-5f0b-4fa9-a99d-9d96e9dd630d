use std::sync::Arc;
use anyhow::Result;
use chrono::{DateTime, Utc};
use sqlx::PgPool;
use tokio::sync::RwLock;

use repository::timescale::{
    KlineRepository, SymbolRepository,
    CreateMarketDataRequest, SymbolStats, Symbol
};

/// TimescaleDB存储管理器 - 简化版本
pub struct TimescaleStorageManager {
    /// 符号管理器
    symbol_manager: Arc<SymbolManager>,
    /// K线管理器
    kline_manager: Arc<KlineManager>,
    /// 数据库连接池
    pool: PgPool,
}

impl TimescaleStorageManager {
    /// 创建新的存储管理器
    pub async fn new(pool: PgPool) -> Result<Self> {
        let symbol_manager = Arc::new(SymbolManager::new(pool.clone()).await?);
        let kline_manager = Arc::new(KlineManager::new(pool.clone()).await?);

        Ok(Self {
            symbol_manager,
            kline_manager,
            pool,
        })
    }

    /// 初始化数据库表结构
    pub async fn initialize_database(&self) -> Result<()> {
        log::info!("初始化TimescaleDB表结构...");

        // 初始化symbols表
        self.symbol_manager.initialize_tables().await?;
        
        // 初始化klines表
        self.kline_manager.initialize_tables().await?;

        log::info!("TimescaleDB表结构初始化完成");
        Ok(())
    }

    /// 获取符号管理器
    pub fn symbol_manager(&self) -> Arc<SymbolManager> {
        self.symbol_manager.clone()
    }

    /// 获取K线管理器
    pub fn kline_manager(&self) -> Arc<KlineManager> {
        self.kline_manager.clone()
    }

    /// 执行数据维护操作
    pub async fn perform_maintenance(&self) -> Result<MaintenanceResult> {
        log::info!("开始执行数据维护操作...");

        let mut result = MaintenanceResult::default();

        // 清理旧数据（保留1年）
        let cleanup_threshold = Utc::now() - chrono::Duration::days(365);
        result.cleaned_records += self.kline_manager.cleanup_old_data(cleanup_threshold).await?;

        // 更新统计信息
        sqlx::query("ANALYZE klines, symbols;")
            .execute(&self.pool)
            .await?;
        result.updated_stats = true;

        log::info!("数据维护操作完成: {:?}", result);
        Ok(result)
    }
}

/// 符号管理器 - 简化版本
pub struct SymbolManager {
    repository: SymbolRepository,
    cache: RwLock<SymbolCache>,
}

impl SymbolManager {
    /// 创建新的符号管理器
    pub async fn new(pool: PgPool) -> Result<Self> {
        let repository = SymbolRepository::new(pool);
        let cache = RwLock::new(SymbolCache::new());

        let manager = Self {
            repository,
            cache,
        };

        // 预加载活跃符号到缓存
        manager.refresh_cache().await?;

        Ok(manager)
    }

    /// 初始化表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        self.repository.initialize_tables().await
            .map_err(|e| anyhow::anyhow!("Failed to initialize symbol tables: {}", e))
    }

    /// 获取所有活跃符号代码
    pub async fn get_active_symbols(&self) -> Result<Vec<String>> {
        // 先尝试从缓存获取
        {
            let cache = self.cache.read().await;
            if !cache.is_expired() {
                return Ok(cache.active_symbols.clone());
            }
        }

        // 缓存过期，从数据库刷新
        self.refresh_cache().await?;
        
        let cache = self.cache.read().await;
        Ok(cache.active_symbols.clone())
    }

    /// 获取所有活跃符号对象
    pub async fn get_active_symbol_objects(&self) -> Result<Vec<Symbol>> {
        self.repository.get_active_symbols().await
            .map_err(|e| anyhow::anyhow!("获取活跃符号对象失败: {}", e))
    }

    /// 检查符号是否存在
    pub async fn symbol_exists(&self, symbol: &str) -> Result<bool> {
        let cache = self.cache.read().await;
        if cache.active_symbols.contains(&symbol.to_string()) {
            return Ok(true);
        }
        
        // 缓存中没有，查询数据库
        self.repository.exists(symbol).await
            .map_err(|e| anyhow::anyhow!("Failed to check symbol existence: {}", e))
    }

    /// 获取符号统计信息
    pub async fn get_stats(&self) -> Result<SymbolStats> {
        self.repository.get_stats().await
            .map_err(|e| anyhow::anyhow!("Failed to get symbol stats: {}", e))
    }

    /// 刷新缓存
    async fn refresh_cache(&self) -> Result<()> {
        let active_symbols = self.repository.get_active_symbol_codes().await
            .map_err(|e| anyhow::anyhow!("Failed to get active symbol codes: {}", e))?;
        
        let mut cache = self.cache.write().await;
        cache.active_symbols = active_symbols;
        cache.last_updated = Utc::now();
        
        Ok(())
    }
}

/// K线管理器 - 简化版本
pub struct KlineManager {
    repository: KlineRepository,
    batch_buffer: RwLock<Vec<CreateMarketDataRequest>>,
    batch_size: usize,
}

impl KlineManager {
    /// 创建新的K线管理器
    pub async fn new(pool: PgPool) -> Result<Self> {
        let repository = KlineRepository::new(pool);
        
        Ok(Self {
            repository,
            batch_buffer: RwLock::new(Vec::new()),
            batch_size: 1000, // 默认批量大小
        })
    }

    /// 初始化表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        self.repository.initialize_tables().await
            .map_err(|e| anyhow::anyhow!("Failed to initialize kline tables: {}", e))
    }

    /// 批量插入K线数据
    pub async fn batch_insert(&self, requests: Vec<CreateMarketDataRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        // 分批插入以避免单次插入过多数据
        let mut total_inserted = 0;
        for chunk in requests.chunks(self.batch_size) {
            let inserted = self.repository.batch_insert(chunk.to_vec()).await
                .map_err(|e| anyhow::anyhow!("Failed to batch insert kline data: {}", e))?;
            total_inserted += inserted;
        }

        Ok(total_inserted)
    }

    /// 清理旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        self.repository.cleanup_old_data(before_time).await
            .map_err(|e| anyhow::anyhow!("Failed to cleanup old kline data: {}", e))
    }
}

/// 符号缓存 - 简化版本
struct SymbolCache {
    active_symbols: Vec<String>,
    last_updated: DateTime<Utc>,
    cache_duration: chrono::Duration,
}

impl SymbolCache {
    fn new() -> Self {
        Self {
            active_symbols: Vec::new(),
            last_updated: DateTime::<Utc>::MIN_UTC,
            cache_duration: chrono::Duration::minutes(5), // 5分钟缓存
        }
    }

    fn is_expired(&self) -> bool {
        Utc::now() - self.last_updated > self.cache_duration
    }
}

/// 维护操作结果
#[derive(Debug, Default)]
pub struct MaintenanceResult {
    pub cleaned_records: u64,
    pub updated_stats: bool,
} 