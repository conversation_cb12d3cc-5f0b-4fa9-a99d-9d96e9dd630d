# 市场数据维护服务配置文件
# 支持多环境配置和动态配置更新

# 环境配置 (Development, Testing, Staging, Production)
environment = "Development"

[service]
name = "market-data-maintenance"
version = "1.0.0"
startup_mode = "Standard"  # Fast, Standard, Full
graceful_shutdown_timeout_seconds = 30
health_check_interval_seconds = 60

[maintenance]
# 全量扫描间隔（小时）
full_scan_interval_hours = 24
# 增量扫描间隔（分钟）
incremental_scan_interval_minutes = 5
# 批量处理大小
batch_size = 1000
# 最大并发符号数
max_concurrent_symbols = 10
# 数据保留天数
data_retention_days = 365
# 是否启用自动修复
enable_auto_repair = true
# 修复重试次数
repair_retry_count = 3

[cache_sync]
# 同步间隔（秒）
sync_interval_seconds = 60
# 预热缓存天数
warmup_days = 7
# 是否启用智能预热
enable_smart_warmup = true
# 最大缓存大小（MB）
max_cache_size_mb = 1024

# TTL策略配置
[cache_sync.ttl_strategy]
# 策略类型: Fixed, Dynamic, Smart
type = "Dynamic"
base_hours = 24
decay_factor = 0.1

[gap_detection]
# 检测的时间间隔
intervals = ["1m", "5m", "15m", "1h", "4h", "1d"]
# 批量检测窗口大小（天）
batch_window_days = 7
# 是否启用智能采样
enable_smart_sampling = true
# 采样率
sampling_rate = 0.1
# 严重缺口阈值
severe_gap_threshold = 0.8

# 交易所配置
[exchanges.binance]
name = "Binance"
base_url = "https://api.binance.com"
timeout_seconds = 30
rate_limit_per_minute = 1200
enabled = true

[exchanges.binance.retry]
max_attempts = 3
base_delay_ms = 1000
max_delay_ms = 10000
backoff_strategy = "Exponential"  # Fixed, Linear, Exponential

[exchanges.okx]
name = "OKX"
base_url = "https://www.okx.com"
timeout_seconds = 30
rate_limit_per_minute = 600
enabled = false

[exchanges.okx.retry]
max_attempts = 3
base_delay_ms = 1000
max_delay_ms = 10000
backoff_strategy = "Exponential"

[monitoring]
# 是否启用指标收集
enable_metrics = true
# 指标收集间隔（秒）
metrics_interval_seconds = 60

[monitoring.alerts]
# 是否启用告警
enabled = true
# 缺口检测告警阈值
gap_detection_threshold = 10
# 同步延迟告警阈值（分钟）
sync_delay_threshold_minutes = 10
# 错误率告警阈值
error_rate_threshold = 0.05

[monitoring.logging]
# 日志级别
level = "info"
# 是否启用结构化日志
structured = true
# 日志轮转大小（MB）
rotation_size_mb = 100
# 日志保留天数
retention_days = 30

[performance]
# 数据库连接池大小
db_pool_size = 20
# 缓存连接池大小
cache_pool_size = 10
# 工作线程数（None表示自动检测）
worker_threads = 8
# 内存限制（MB，None表示无限制）
memory_limit_mb = 2048
# 是否启用压缩
enable_compression = true
