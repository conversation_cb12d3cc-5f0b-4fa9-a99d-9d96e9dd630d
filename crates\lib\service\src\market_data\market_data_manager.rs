// 市场数据管理器
// 统一管理数据完整性检查、Redis缓存和增量更新服务

use chrono::{DateTime, Utc, Duration};
use repository::{
    timescale::MarketDataRepository,
    connection::{
        timescale::TimescalePoolManager,
        redis::RedisOperations,
    },
};
use exchange::binance::BinanceClient;
use crate::market_data::{
    DataIntegrityService,
    RedisCacheService,
    IncrementalUpdateService,
    IntegrityCheckResult,
    CacheStatus,
    IncrementalUpdateResult,
    Result,
    MarketDataServiceError,
};
use std::collections::HashMap;
use tokio::task::Join<PERSON><PERSON><PERSON>;

/// 市场数据管理器配置
#[derive(Debug, Clone)]
pub struct MarketDataManagerConfig {
    /// 交易所名称
    pub exchange_name: String,
    /// 增量更新间隔（分钟）
    pub update_interval_minutes: u64,
    /// 活跃交易对列表
    pub active_symbols: Vec<String>,
    /// 数据完整性检查的开始日期
    pub integrity_check_start_date: DateTime<Utc>,
    /// 是否启用自动增量更新
    pub enable_auto_update: bool,
    /// 是否启用Redis缓存
    pub enable_redis_cache: bool,
}

impl Default for MarketDataManagerConfig {
    fn default() -> Self {
        Self {
            exchange_name: "binance".to_string(),
            update_interval_minutes: 5,
            active_symbols: vec![
                "BTCUSDT".to_string(),
                "ETHUSDT".to_string(),
                "BNBUSDT".to_string(),
            ],
            integrity_check_start_date: Utc::now() - Duration::days(30),
            enable_auto_update: true,
            enable_redis_cache: true,
        }
    }
}

/// 市场数据管理器
/// 负责协调所有市场数据相关的服务
pub struct MarketDataManager {
    /// 配置
    config: MarketDataManagerConfig,
    /// 数据完整性检查服务
    integrity_service: DataIntegrityService,
    /// Redis缓存服务
    cache_service: Option<RedisCacheService>,
    /// 增量更新服务
    update_service: IncrementalUpdateService,
    /// 后台任务句柄
    background_task: Option<JoinHandle<()>>,
}

impl MarketDataManager {
    /// 创建新的市场数据管理器
    pub async fn new(
        config: MarketDataManagerConfig,
        timescale_pool: TimescalePoolManager,
        redis_ops: Option<RedisOperations>,
        binance_client: BinanceClient,
    ) -> Result<Self> {
        log::info!("初始化市场数据管理器...");

        // 创建数据完整性检查服务
        let integrity_service = DataIntegrityService::new(
            &timescale_pool,
            binance_client.clone(),
        ).await?;

        // 创建Redis缓存服务（如果启用）
        let cache_service = if config.enable_redis_cache && redis_ops.is_some() {
            let pool = timescale_pool.pool().clone();
            let market_data_repo = MarketDataRepository::new(pool);
            
            Some(RedisCacheService::new(
                redis_ops.unwrap(),
                market_data_repo,
                config.exchange_name.clone(),
            ))
        } else {
            log::warn!("Redis缓存服务未启用或Redis连接不可用");
            None
        };

        // 创建增量更新服务
        let update_service = if let Some(ref cache_service) = cache_service {
            IncrementalUpdateService::new(
                timescale_pool,
                cache_service.clone(),
                binance_client,
                config.update_interval_minutes,
            ).await?
        } else {
            // 如果没有缓存服务，创建一个空的缓存服务用于占位
            let pool = timescale_pool.pool().clone();
            let market_data_repo = MarketDataRepository::new(pool.clone());
            let dummy_redis = RedisOperations::new(
                repository::connection::redis::RedisPoolManager::new(
                    common::config::redis_config::RedisConfig::from_env()
                ).await.map_err(|e| MarketDataServiceError::Configuration(e.to_string()))?
            );
            let dummy_cache = RedisCacheService::new(
                dummy_redis,
                market_data_repo,
                config.exchange_name.clone(),
            );
            
            IncrementalUpdateService::new(
                timescale_pool,
                dummy_cache,
                binance_client,
                config.update_interval_minutes,
            ).await?
        };

        Ok(Self {
            config,
            integrity_service,
            cache_service,
            update_service,
            background_task: None,
        })
    }

    /// 启动市场数据管理器
    /// 执行完整的初始化流程：全量检查 -> 缓存预热 -> 启动增量更新
    pub async fn start(&mut self) -> Result<()> {
        log::info!("启动市场数据管理器...");

        // 1. 执行全量数据完整性检查
        log::info!("步骤 1/3: 执行全量数据完整性检查");
        let integrity_results = self.perform_full_integrity_check().await?;
        
        let total_symbols = integrity_results.len();
        let incomplete_symbols = integrity_results
            .iter()
            .filter(|r| r.completeness_percentage < 100.0)
            .count();
        
        log::info!(
            "数据完整性检查完成: {} 个交易对检查完毕，{} 个需要补充数据",
            total_symbols,
            incomplete_symbols
        );

        // 记录详细的完整性信息
        for result in &integrity_results {
            if result.completeness_percentage < 100.0 {
                log::warn!(
                    "⚠️  {} {} 完整性: {:.2}% ({}/{}), 缺失 {} 个时间段", 
                    result.symbol, 
                    result.interval.to_binance_string(),
                    result.completeness_percentage,
                    result.actual_count,
                    result.expected_count,
                    result.missing_periods.len()
                );
            } else {
                log::info!(
                    "✅ {} {} 完整性: 100% ({} 条记录)", 
                    result.symbol, 
                    result.interval.to_binance_string(),
                    result.actual_count
                );
            }
        }

        // 2. 预热Redis缓存（如果启用）
        if let Some(ref cache_service) = self.cache_service {
            log::info!("步骤 2/3: 预热Redis缓存");
            if let Err(e) = cache_service.warm_up_cache(&self.config.active_symbols).await {
                log::warn!("缓存预热失败: {}", e);
            } else {
                log::info!("缓存预热完成");
            }
        } else {
            log::info!("步骤 2/3: 跳过Redis缓存预热（未启用）");
        }

        // 3. 启动增量更新服务
        if self.config.enable_auto_update {
            log::info!("步骤 3/3: 启动增量更新服务");
            self.start_background_update().await?;
        } else {
            log::info!("步骤 3/3: 跳过增量更新服务（未启用）");
        }

        log::info!("市场数据管理器启动完成");
        Ok(())
    }

    /// 启动时执行初始数据检查和同步
    /// 这是一个一次性的启动任务，会立即执行完整性检查和数据同步
    pub async fn perform_startup_tasks(&self) -> Result<()> {
        log::info!("🚀 执行启动任务...");
        
        // 1. 立即执行完整性检查
        log::info!("执行启动完整性检查...");
        let integrity_results = self.perform_full_integrity_check().await?;
        
        // 2. 立即执行一次增量更新
        log::info!("执行启动增量更新...");
        let update_results = self.trigger_manual_update().await?;
        
        log::info!("✅ 启动任务完成: 完整性检查 {} 项，增量更新 {} 项", 
            integrity_results.len(), update_results.len());
        
        Ok(())
    }

    /// 执行全量数据完整性检查
    pub async fn perform_full_integrity_check(&self) -> Result<Vec<IntegrityCheckResult>> {
        log::info!("开始全量数据完整性检查...");
        
        let results = self.integrity_service
            .perform_full_integrity_check(
                self.config.integrity_check_start_date,
                Some(Utc::now()),
            )
            .await?;

        // 记录检查结果统计
        let mut symbol_stats: HashMap<String, f64> = HashMap::new();
        for result in &results {
            let entry = symbol_stats.entry(result.symbol.clone()).or_insert(0.0);
            *entry += result.completeness_percentage;
        }

        for (symbol, total_percentage) in symbol_stats {
            let avg_percentage = total_percentage / 11.0; // 11个时间间隔
            log::info!("{} 平均完整性: {:.2}%", symbol, avg_percentage);
        }

        Ok(results)
    }

    /// 启动后台增量更新任务
    async fn start_background_update(&mut self) -> Result<()> {
        if self.background_task.is_some() {
            log::warn!("增量更新任务已在运行");
            return Ok(());
        }

        // 设置活跃交易对
        let mut update_service = self.update_service.clone();
        update_service.set_active_symbols(self.config.active_symbols.clone());

        // 启动后台任务
        let task = tokio::spawn(async move {
            if let Err(e) = update_service.start_update_loop().await {
                log::error!("增量更新服务异常退出: {}", e);
            }
        });

        self.background_task = Some(task);
        log::info!("增量更新后台任务已启动");
        Ok(())
    }

    /// 手动触发增量更新
    pub async fn trigger_manual_update(&self) -> Result<Vec<IncrementalUpdateResult>> {
        log::info!("手动触发增量更新");
        self.update_service.trigger_immediate_update().await
    }

    /// 获取缓存状态
    pub async fn get_cache_status(&self) -> Result<HashMap<String, CacheStatus>> {
        if let Some(ref cache_service) = self.cache_service {
            let mut statuses = HashMap::new();
            
            for symbol in &self.config.active_symbols {
                match cache_service.get_cache_status(symbol).await {
                    Ok(status) => {
                        statuses.insert(symbol.clone(), status);
                    }
                    Err(e) => {
                        log::warn!("获取 {} 缓存状态失败: {}", symbol, e);
                    }
                }
            }
            
            Ok(statuses)
        } else {
            Ok(HashMap::new())
        }
    }

    /// 清理过期缓存
    pub async fn cleanup_expired_cache(&self) -> Result<i64> {
        if let Some(ref cache_service) = self.cache_service {
            cache_service.cleanup_expired_cache().await
        } else {
            Ok(0)
        }
    }

    /// 获取服务统计信息
    pub async fn get_service_statistics(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut stats = HashMap::new();

        // 基本配置信息
        stats.insert("exchange_name".to_string(), 
            serde_json::Value::String(self.config.exchange_name.clone()));
        stats.insert("active_symbols_count".to_string(), 
            serde_json::Value::Number(serde_json::Number::from(self.config.active_symbols.len())));
        stats.insert("update_interval_minutes".to_string(), 
            serde_json::Value::Number(serde_json::Number::from(self.config.update_interval_minutes)));
        stats.insert("auto_update_enabled".to_string(), 
            serde_json::Value::Bool(self.config.enable_auto_update));
        stats.insert("redis_cache_enabled".to_string(), 
            serde_json::Value::Bool(self.config.enable_redis_cache));

        // 后台任务状态
        stats.insert("background_task_running".to_string(), 
            serde_json::Value::Bool(self.background_task.is_some()));

        // 增量更新统计
        if let Ok(update_stats) = self.update_service.get_update_statistics().await {
            for (key, value) in update_stats {
                stats.insert(format!("update_{}", key), 
                    serde_json::Value::Number(serde_json::Number::from(value as i64)));
            }
        }

        // 数据完整性统计
        if let Ok(integrity_stats) = self.integrity_service.get_integrity_statistics().await {
            for (key, value) in integrity_stats {
                if let Some(num) = serde_json::Number::from_f64(value) {
                    stats.insert(format!("integrity_{}", key), 
                        serde_json::Value::Number(num));
                }
            }
        }

        Ok(stats)
    }

    /// 更新配置
    pub async fn update_config(&mut self, new_config: MarketDataManagerConfig) -> Result<()> {
        log::info!("更新市场数据管理器配置");
        
        let old_symbols = self.config.active_symbols.clone();
        self.config = new_config;

        // 如果活跃交易对发生变化，更新增量更新服务
        if old_symbols != self.config.active_symbols {
            self.update_service.set_active_symbols(self.config.active_symbols.clone());
            log::info!("已更新活跃交易对列表");
        }

        Ok(())
    }

    /// 停止服务
    pub async fn stop(&mut self) -> Result<()> {
        log::info!("停止市场数据管理器...");

        // 停止后台任务
        if let Some(task) = self.background_task.take() {
            task.abort();
            log::info!("后台增量更新任务已停止");
        }

        // 停止增量更新服务
        self.update_service.stop().await?;

        log::info!("市场数据管理器已停止");
        Ok(())
    }

    /// 获取配置
    pub fn get_config(&self) -> &MarketDataManagerConfig {
        &self.config
    }

    /// 获取市场数据仓库
    pub fn get_repository(&self) -> &MarketDataRepository {
        self.integrity_service.get_repository()
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<HashMap<String, bool>> {
        let mut health = HashMap::new();

        // 检查数据库连接
        health.insert("database".to_string(), true); // 简化实现

        // 检查Redis连接
        if self.cache_service.is_some() {
            health.insert("redis".to_string(), true); // 简化实现
        }

        // 检查后台任务状态
        health.insert("background_task".to_string(), 
            self.background_task.as_ref().map_or(false, |t| !t.is_finished()));

        Ok(health)
    }
}

 