use axum::{
    extract::{FromRequestParts, State},
    http::{StatusCode, request::Parts},
    response::{IntoResponse, Json, Response},
};
use headers::{Authorization, HeaderMapExt, authorization::Bearer};
use jsonwebtoken::{Dec<PERSON><PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, Header, Validation, decode, encode, errors::Error as JwtError};
use serde::{Deserialize, Serialize};
use std::env;
use thiserror::Error;

// 1. Claims (JWT 载荷)
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // 用户ID
    pub exp: usize,  // 过期时间
    pub iss: String, // 签发者
}

// 2. 认证错误类型
#[derive(Debug, Error)]
pub enum AuthError {
    #[error("Missing authentication token")]
    MissingToken,
    #[error("Invalid token format")]
    InvalidTokenFormat,
    #[error("Invalid token: {0}")]
    InvalidToken(#[from] JwtError),
}

impl IntoResponse for AuthError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            AuthError::MissingToken => (StatusCode::UNAUTHORIZED, "缺少认证令牌".to_string()),
            AuthError::InvalidTokenFormat => (StatusCode::BAD_REQUEST, "令牌格式错误, 需要 'Bearer <token>'".to_string()),
            AuthError::InvalidToken(e) => (StatusCode::UNAUTHORIZED, format!("无效的令牌: {}", e)),
        };
        (status, Json(serde_json::json!({ "error": message }))).into_response()
    }
}

// 3. 提取器本身
pub struct AuthenticatedUser(pub Claims);

impl<S> FromRequestParts<S> for AuthenticatedUser
where
    S: Send + Sync,
{
    type Rejection = AuthError;

    fn from_request_parts(
        parts: &mut Parts,
        _state: &S,
    ) -> impl std::future::Future<Output = Result<Self, Self::Rejection>> + Send {
        async move {
            // 从请求头中提取 "Authorization: Bearer <token>"
            let auth_header = parts.headers.typed_get::<Authorization<Bearer>>().ok_or(AuthError::MissingToken)?;
            let token = auth_header.token();
            // 获取密钥
            let secret = env::var("JWT_SECRET").unwrap_or_else(|_| "your-secret".to_string());
            // 配置验证选项
            let mut validation = Validation::new(jsonwebtoken::Algorithm::HS256);
            validation.set_issuer(&["my-app"]);

            // 解码和验证
            let token_data = decode::<Claims>(token, &DecodingKey::from_secret(secret.as_bytes()), &validation)?; // JwtError 会通过 From trait 自动转换为 AuthError::InvalidToken

            // 成功则返回包含 Claims 的结构体
            Ok(AuthenticatedUser(token_data.claims))
        }
    }
}

use crate::router::AppState;
use firebase_auth::{FirebaseAuth, FirebaseUser};

// 这个提取器专门用于 Firebase
pub struct FirebaseAuthenticatedUser(pub FirebaseUser);

impl FromRequestParts<AppState> for FirebaseAuthenticatedUser {
    type Rejection = (StatusCode, String);

    fn from_request_parts(
        parts: &mut Parts,
        state: &AppState,
    ) -> impl std::future::Future<Output = Result<Self, Self::Rejection>> + Send {
        async move {
            // 从具体的 state 中直接访问字段
            let firebase_auth = &state.firebase_auth;

            // 从请求头获取 Bearer Token
            let auth_header = parts.headers.typed_get::<Authorization<Bearer>>()
                .ok_or((StatusCode::UNAUTHORIZED, "缺少认证令牌".to_string()))?;

            // 使用 firebase_auth 实例来验证 token
            // firebase_auth.verify() 返回 Option<FirebaseUser>，不是 Result
            match firebase_auth.verify(auth_header.token()) {
                Some(user) => Ok(FirebaseAuthenticatedUser(user)), // 验证成功
                None => Err((StatusCode::UNAUTHORIZED, "Firebase Token 无效".to_string())),
            }
        }
    }
}
