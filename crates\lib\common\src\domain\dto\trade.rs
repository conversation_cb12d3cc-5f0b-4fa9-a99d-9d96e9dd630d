use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use serde::{Deserialize, Serialize};

use crate::domain::entity::TradeEntity;

/// 交易数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TradeDto {
    /// 时间戳
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 交易ID
    pub trade_id: i64,
    /// 价格
    pub price: Decimal,
    /// 数量
    pub qty: Decimal,
    /// 计价资产数量
    pub quote_qty: Decimal,
    /// 是否为买方主动成交
    pub is_buyer_maker: bool,
    /// 是否为最佳匹配
    pub is_best_match: bool,
}

impl From<TradeEntity> for TradeDto {
    fn from(entity: TradeEntity) -> Self {
        Self {
            time: entity.time,
            symbol: entity.symbol,
            trade_id: entity.trade_id,
            price: entity.price,
            qty: entity.qty,
            quote_qty: entity.quote_qty,
            is_buyer_maker: entity.is_buyer_maker,
            is_best_match: entity.is_best_match,
        }
    }
}

impl From<TradeDto> for TradeEntity {
    fn from(dto: TradeDto) -> Self {
        Self {
            time: dto.time,
            symbol: dto.symbol,
            trade_id: dto.trade_id,
            price: dto.price,
            qty: dto.qty,
            quote_qty: dto.quote_qty,
            is_buyer_maker: dto.is_buyer_maker,
            is_best_match: dto.is_best_match,
        }
    }
} 