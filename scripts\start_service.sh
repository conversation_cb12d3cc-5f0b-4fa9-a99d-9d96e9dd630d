#!/bin/bash

# 市场数据维护服务启动脚本
# 支持多环境配置和优雅启动

set -e

# 默认配置
DEFAULT_ENV="development"
DEFAULT_CONFIG_DIR="./config"
DEFAULT_LOG_LEVEL="info"
DEFAULT_STARTUP_MODE="standard"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
市场数据维护服务启动脚本

用法: $0 [选项]

选项:
    -e, --env ENV           环境 (development|testing|staging|production) [默认: $DEFAULT_ENV]
    -c, --config DIR        配置文件目录 [默认: $DEFAULT_CONFIG_DIR]
    -l, --log-level LEVEL   日志级别 (debug|info|warn|error) [默认: $DEFAULT_LOG_LEVEL]
    -m, --mode MODE         启动模式 (fast|standard|full) [默认: $DEFAULT_STARTUP_MODE]
    -d, --daemon            后台运行
    -h, --help              显示此帮助信息
    --check-config          仅检查配置文件
    --export-config FILE    导出当前配置到文件
    --validate              验证配置和环境

示例:
    $0 --env production --mode standard
    $0 --env development --log-level debug
    $0 --check-config --env production
    $0 --export-config ./exported_config.toml

EOF
}

# 解析命令行参数
parse_args() {
    ENV="$DEFAULT_ENV"
    CONFIG_DIR="$DEFAULT_CONFIG_DIR"
    LOG_LEVEL="$DEFAULT_LOG_LEVEL"
    STARTUP_MODE="$DEFAULT_STARTUP_MODE"
    DAEMON_MODE=false
    CHECK_CONFIG_ONLY=false
    EXPORT_CONFIG=""
    VALIDATE_ONLY=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENV="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_DIR="$2"
                shift 2
                ;;
            -l|--log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            -m|--mode)
                STARTUP_MODE="$2"
                shift 2
                ;;
            -d|--daemon)
                DAEMON_MODE=true
                shift
                ;;
            --check-config)
                CHECK_CONFIG_ONLY=true
                shift
                ;;
            --export-config)
                EXPORT_CONFIG="$2"
                shift 2
                ;;
            --validate)
                VALIDATE_ONLY=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证环境参数
validate_env() {
    case "$ENV" in
        development|testing|staging|production)
            ;;
        *)
            print_error "无效的环境: $ENV"
            print_info "支持的环境: development, testing, staging, production"
            exit 1
            ;;
    esac
}

# 验证启动模式
validate_startup_mode() {
    case "$STARTUP_MODE" in
        fast|standard|full)
            ;;
        *)
            print_error "无效的启动模式: $STARTUP_MODE"
            print_info "支持的启动模式: fast, standard, full"
            exit 1
            ;;
    esac
}

# 检查配置文件
check_config_file() {
    local config_file="$CONFIG_DIR/$ENV.toml"
    
    if [[ ! -f "$config_file" ]]; then
        print_warning "环境配置文件不存在: $config_file"
        print_info "尝试使用默认配置文件: $CONFIG_DIR/market_data_maintenance.toml"
        
        config_file="$CONFIG_DIR/market_data_maintenance.toml"
        if [[ ! -f "$config_file" ]]; then
            print_error "默认配置文件也不存在: $config_file"
            return 1
        fi
    fi
    
    print_success "找到配置文件: $config_file"
    echo "$config_file"
}

# 设置环境变量
setup_environment() {
    export ENVIRONMENT="$ENV"
    export LOG_LEVEL="$LOG_LEVEL"
    export STARTUP_MODE="$STARTUP_MODE"
    export RUST_LOG="$LOG_LEVEL"
    
    # 根据环境设置特定的环境变量
    case "$ENV" in
        development)
            export FULL_SCAN_INTERVAL_HOURS=1
            export INCREMENTAL_SCAN_INTERVAL_MINUTES=2
            export CACHE_SYNC_INTERVAL_SECONDS=30
            ;;
        testing)
            export FULL_SCAN_INTERVAL_HOURS=6
            export INCREMENTAL_SCAN_INTERVAL_MINUTES=5
            export CACHE_SYNC_INTERVAL_SECONDS=60
            ;;
        staging)
            export FULL_SCAN_INTERVAL_HOURS=12
            export INCREMENTAL_SCAN_INTERVAL_MINUTES=5
            export CACHE_SYNC_INTERVAL_SECONDS=60
            ;;
        production)
            export FULL_SCAN_INTERVAL_HOURS=24
            export INCREMENTAL_SCAN_INTERVAL_MINUTES=5
            export CACHE_SYNC_INTERVAL_SECONDS=60
            ;;
    esac
    
    print_info "环境变量已设置:"
    print_info "  ENVIRONMENT=$ENVIRONMENT"
    print_info "  LOG_LEVEL=$LOG_LEVEL"
    print_info "  STARTUP_MODE=$STARTUP_MODE"
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查Rust工具链
    if ! command -v cargo &> /dev/null; then
        print_error "Cargo未安装，请先安装Rust工具链"
        exit 1
    fi
    
    # 检查数据库连接
    if [[ -n "${DATABASE_URL:-}" ]]; then
        print_info "数据库URL: $DATABASE_URL"
    else
        print_warning "DATABASE_URL环境变量未设置"
    fi
    
    # 检查Redis连接
    if [[ -n "${REDIS_URL:-}" ]]; then
        print_info "Redis URL: $REDIS_URL"
    else
        print_warning "REDIS_URL环境变量未设置"
    fi
    
    print_success "依赖检查完成"
}

# 主函数
main() {
    print_info "市场数据维护服务启动脚本"
    print_info "================================"
    
    # 解析参数
    parse_args "$@"
    
    # 验证参数
    validate_env
    validate_startup_mode
    
    # 检查配置文件
    config_file=$(check_config_file)
    if [[ $? -ne 0 ]]; then
        exit 1
    fi
    
    # 如果只是检查配置
    if [[ "$CHECK_CONFIG_ONLY" == true ]]; then
        print_success "配置文件检查完成"
        exit 0
    fi
    
    # 如果只是验证
    if [[ "$VALIDATE_ONLY" == true ]]; then
        print_info "验证环境和配置..."
        setup_environment
        check_dependencies
        print_success "验证完成"
        exit 0
    fi
    
    # 设置环境
    setup_environment
    
    # 检查依赖
    check_dependencies
    
    # 构建服务
    print_info "构建服务..."
    if ! cargo build --release; then
        print_error "服务构建失败"
        exit 1
    fi
    print_success "服务构建完成"
    
    # 启动服务
    print_info "启动市场数据维护服务..."
    print_info "环境: $ENV"
    print_info "配置文件: $config_file"
    print_info "启动模式: $STARTUP_MODE"
    
    if [[ "$DAEMON_MODE" == true ]]; then
        print_info "以守护进程模式启动..."
        nohup ./target/release/user-server --config "$config_file" > "logs/service_$ENV.log" 2>&1 &
        echo $! > "pids/service_$ENV.pid"
        print_success "服务已在后台启动，PID: $(cat pids/service_$ENV.pid)"
    else
        print_info "以前台模式启动..."
        exec ./target/release/user-server --config "$config_file"
    fi
}

# 创建必要的目录
mkdir -p logs pids

# 运行主函数
main "$@"
