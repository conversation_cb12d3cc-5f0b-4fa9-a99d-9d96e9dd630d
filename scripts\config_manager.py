#!/usr/bin/env python3
"""
市场数据维护服务配置管理工具

功能:
- 生成不同环境的配置文件
- 验证配置文件
- 配置文件格式转换
- 配置差异比较
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any
import toml
import yaml


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.templates_dir = Path("config/templates")
        
    def generate_config(self, env: str, output_file: str = None) -> None:
        """生成指定环境的配置文件"""
        print(f"🔧 生成 {env} 环境配置...")
        
        # 基础配置模板
        base_config = self._get_base_config()
        
        # 环境特定配置
        env_config = self._get_env_specific_config(env)
        
        # 合并配置
        final_config = self._merge_configs(base_config, env_config)
        
        # 输出文件路径
        if output_file is None:
            output_file = self.config_dir / f"{env}.toml"
        else:
            output_file = Path(output_file)
            
        # 确保目录存在
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入配置文件
        with open(output_file, 'w', encoding='utf-8') as f:
            toml.dump(final_config, f)
            
        print(f"✅ 配置文件已生成: {output_file}")
        
    def validate_config(self, config_file: str) -> bool:
        """验证配置文件"""
        print(f"🔍 验证配置文件: {config_file}")
        
        config_path = Path(config_file)
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_file}")
            return False
            
        try:
            # 加载配置
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.toml':
                    config = toml.load(f)
                elif config_path.suffix.lower() in ['.yaml', '.yml']:
                    config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config = json.load(f)
                else:
                    print(f"❌ 不支持的配置文件格式: {config_path.suffix}")
                    return False
                    
            # 验证必需字段
            required_sections = ['service', 'maintenance', 'cache_sync', 'gap_detection', 'monitoring', 'performance']
            for section in required_sections:
                if section not in config:
                    print(f"❌ 缺少必需配置节: {section}")
                    return False
                    
            # 验证具体字段
            validation_errors = []
            
            # 验证服务配置
            service = config.get('service', {})
            if not service.get('name'):
                validation_errors.append("service.name 不能为空")
            if not service.get('version'):
                validation_errors.append("service.version 不能为空")
                
            # 验证维护配置
            maintenance = config.get('maintenance', {})
            if maintenance.get('full_scan_interval_hours', 0) <= 0:
                validation_errors.append("maintenance.full_scan_interval_hours 必须大于0")
            if maintenance.get('incremental_scan_interval_minutes', 0) <= 0:
                validation_errors.append("maintenance.incremental_scan_interval_minutes 必须大于0")
                
            # 验证缺口检测配置
            gap_detection = config.get('gap_detection', {})
            sampling_rate = gap_detection.get('sampling_rate', 0)
            if not (0 < sampling_rate <= 1):
                validation_errors.append("gap_detection.sampling_rate 必须在0到1之间")
                
            if validation_errors:
                print("❌ 配置验证失败:")
                for error in validation_errors:
                    print(f"  - {error}")
                return False
                
            print("✅ 配置文件验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件解析失败: {e}")
            return False
            
    def convert_config(self, input_file: str, output_file: str) -> None:
        """转换配置文件格式"""
        print(f"🔄 转换配置文件: {input_file} -> {output_file}")
        
        input_path = Path(input_file)
        output_path = Path(output_file)
        
        if not input_path.exists():
            print(f"❌ 输入文件不存在: {input_file}")
            return
            
        # 加载输入文件
        with open(input_path, 'r', encoding='utf-8') as f:
            if input_path.suffix.lower() == '.toml':
                config = toml.load(f)
            elif input_path.suffix.lower() in ['.yaml', '.yml']:
                config = yaml.safe_load(f)
            elif input_path.suffix.lower() == '.json':
                config = json.load(f)
            else:
                print(f"❌ 不支持的输入格式: {input_path.suffix}")
                return
                
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入输出文件
        with open(output_path, 'w', encoding='utf-8') as f:
            if output_path.suffix.lower() == '.toml':
                toml.dump(config, f)
            elif output_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif output_path.suffix.lower() == '.json':
                json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                print(f"❌ 不支持的输出格式: {output_path.suffix}")
                return
                
        print(f"✅ 配置文件转换完成: {output_file}")
        
    def compare_configs(self, file1: str, file2: str) -> None:
        """比较两个配置文件的差异"""
        print(f"🔍 比较配置文件: {file1} vs {file2}")
        
        # 加载配置文件
        config1 = self._load_config(file1)
        config2 = self._load_config(file2)
        
        if config1 is None or config2 is None:
            return
            
        # 比较配置
        differences = self._find_differences(config1, config2)
        
        if not differences:
            print("✅ 配置文件完全相同")
        else:
            print("📋 发现以下差异:")
            for diff in differences:
                print(f"  {diff}")
                
    def _get_base_config(self) -> Dict[str, Any]:
        """获取基础配置模板"""
        return {
            "environment": "Development",
            "service": {
                "name": "market-data-maintenance",
                "version": "1.0.0",
                "startup_mode": "Standard",
                "graceful_shutdown_timeout_seconds": 30,
                "health_check_interval_seconds": 60
            },
            "maintenance": {
                "full_scan_interval_hours": 24,
                "incremental_scan_interval_minutes": 5,
                "batch_size": 1000,
                "max_concurrent_symbols": 10,
                "data_retention_days": 365,
                "enable_auto_repair": True,
                "repair_retry_count": 3
            },
            "cache_sync": {
                "sync_interval_seconds": 60,
                "warmup_days": 7,
                "enable_smart_warmup": True,
                "max_cache_size_mb": 1024,
                "ttl_strategy": {
                    "Dynamic": {
                        "base_hours": 24,
                        "decay_factor": 0.1
                    }
                }
            },
            "gap_detection": {
                "intervals": ["1m", "5m", "15m", "1h", "4h", "1d"],
                "batch_window_days": 7,
                "enable_smart_sampling": True,
                "sampling_rate": 0.1,
                "severe_gap_threshold": 0.8
            },
            "exchanges": {
                "binance": {
                    "name": "Binance",
                    "base_url": "https://api.binance.com",
                    "timeout_seconds": 30,
                    "rate_limit_per_minute": 1200,
                    "enabled": True,
                    "retry": {
                        "max_attempts": 3,
                        "base_delay_ms": 1000,
                        "max_delay_ms": 10000,
                        "backoff_strategy": "Exponential"
                    }
                }
            },
            "monitoring": {
                "enable_metrics": True,
                "metrics_interval_seconds": 60,
                "alerts": {
                    "enabled": True,
                    "gap_detection_threshold": 10,
                    "sync_delay_threshold_minutes": 10,
                    "error_rate_threshold": 0.05
                },
                "logging": {
                    "level": "info",
                    "structured": True,
                    "rotation_size_mb": 100,
                    "retention_days": 30
                }
            },
            "performance": {
                "db_pool_size": 20,
                "cache_pool_size": 10,
                "enable_compression": True
            }
        }
        
    def _get_env_specific_config(self, env: str) -> Dict[str, Any]:
        """获取环境特定配置"""
        env_configs = {
            "development": {
                "environment": "Development",
                "maintenance": {
                    "full_scan_interval_hours": 1,
                    "incremental_scan_interval_minutes": 2,
                    "batch_size": 100,
                    "max_concurrent_symbols": 3,
                    "enable_auto_repair": False
                },
                "gap_detection": {
                    "intervals": ["1m", "5m"],
                    "sampling_rate": 0.5
                },
                "monitoring": {
                    "alerts": {"enabled": False},
                    "logging": {"level": "debug", "structured": False}
                },
                "performance": {
                    "db_pool_size": 5,
                    "cache_pool_size": 3,
                    "enable_compression": False
                }
            },
            "testing": {
                "environment": "Testing",
                "maintenance": {
                    "enable_auto_repair": False
                },
                "monitoring": {
                    "alerts": {"enabled": False}
                }
            },
            "staging": {
                "environment": "Staging",
                "gap_detection": {
                    "sampling_rate": 0.1
                },
                "monitoring": {
                    "alerts": {"enabled": False}
                }
            },
            "production": {
                "environment": "Production",
                "gap_detection": {
                    "sampling_rate": 0.05
                },
                "performance": {
                    "db_pool_size": 50,
                    "cache_pool_size": 30,
                    "worker_threads": 16,
                    "memory_limit_mb": 8192
                }
            }
        }
        
        return env_configs.get(env, {})
        
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并配置"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def _load_config(self, file_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        path = Path(file_path)
        if not path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return None
            
        try:
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() == '.toml':
                    return toml.load(f)
                elif path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f)
                elif path.suffix.lower() == '.json':
                    return json.load(f)
                else:
                    print(f"❌ 不支持的文件格式: {path.suffix}")
                    return None
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return None
            
    def _find_differences(self, config1: Dict[str, Any], config2: Dict[str, Any], path: str = "") -> list:
        """查找配置差异"""
        differences = []
        
        # 检查config1中的键
        for key, value1 in config1.items():
            current_path = f"{path}.{key}" if path else key
            
            if key not in config2:
                differences.append(f"❌ {current_path}: 仅在文件1中存在")
            else:
                value2 = config2[key]
                if isinstance(value1, dict) and isinstance(value2, dict):
                    differences.extend(self._find_differences(value1, value2, current_path))
                elif value1 != value2:
                    differences.append(f"🔄 {current_path}: {value1} -> {value2}")
                    
        # 检查config2中独有的键
        for key in config2:
            if key not in config1:
                current_path = f"{path}.{key}" if path else key
                differences.append(f"➕ {current_path}: 仅在文件2中存在")
                
        return differences


def main():
    parser = argparse.ArgumentParser(description="市场数据维护服务配置管理工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成配置命令
    gen_parser = subparsers.add_parser('generate', help='生成环境配置文件')
    gen_parser.add_argument('env', choices=['development', 'testing', 'staging', 'production'], help='环境名称')
    gen_parser.add_argument('-o', '--output', help='输出文件路径')
    
    # 验证配置命令
    val_parser = subparsers.add_parser('validate', help='验证配置文件')
    val_parser.add_argument('config_file', help='配置文件路径')
    
    # 转换配置命令
    conv_parser = subparsers.add_parser('convert', help='转换配置文件格式')
    conv_parser.add_argument('input_file', help='输入文件路径')
    conv_parser.add_argument('output_file', help='输出文件路径')
    
    # 比较配置命令
    comp_parser = subparsers.add_parser('compare', help='比较配置文件')
    comp_parser.add_argument('file1', help='第一个配置文件')
    comp_parser.add_argument('file2', help='第二个配置文件')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    manager = ConfigManager()
    
    if args.command == 'generate':
        manager.generate_config(args.env, args.output)
    elif args.command == 'validate':
        if not manager.validate_config(args.config_file):
            sys.exit(1)
    elif args.command == 'convert':
        manager.convert_config(args.input_file, args.output_file)
    elif args.command == 'compare':
        manager.compare_configs(args.file1, args.file2)


if __name__ == '__main__':
    main()
