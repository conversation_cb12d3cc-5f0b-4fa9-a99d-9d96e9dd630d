[package]
name = "user-server"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true



[dependencies]
# Workspace dependencies
common.workspace = true
service.workspace = true
repository.workspace = true
exchange.workspace = true
nacos.workspace = true

# Core dependencies
dotenvy.workspace = true
sqlx.workspace = true
tokio.workspace = true
anyhow.workspace = true
chrono.workspace = true
rust_decimal.workspace = true

# Axum web framework
axum.workspace = true
tower.workspace = true
tower-http.workspace = true

# OpenAPI documentation
utoipa.workspace = true
utoipa-swagger-ui.workspace = true

# Logging and tracing
tracing.workspace = true
tracing-subscriber.workspace = true

# Serialization
serde.workspace = true
serde_json.workspace = true

# Utilities
uuid.workspace = true
mimalloc.workspace = true
num_cpus.workspace = true
thiserror.workspace = true
strum.workspace = true
strum_macros.workspace = true


async-trait.workspace = true

# Authentication and security
headers.workspace = true


rand = "0.8.5"
validator = { version = "0.18.1", features = ["derive"] }

# Additional dependencies
axum-extra = { workspace = true }
tower-sessions = { workspace = true }
tower-sessions-redis-store = { workspace = true }
redis = { workspace = true }
http = "0.2.12"
jsonwebtoken.workspace = true

firebase-auth.workspace = true

