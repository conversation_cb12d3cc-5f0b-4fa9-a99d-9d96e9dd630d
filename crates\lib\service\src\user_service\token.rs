use chrono::{Utc, Duration};
use common::domain::entity::user::User;
use common::domain::dto::{Claims, Refresh<PERSON>laims, AuthError, constants};
use jsonwebtoken::{encode, decode, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use uuid::Uuid;
use std::env;
use anyhow::{anyhow, Result};

/// JWT工具结构体
pub struct JwtService;

impl JwtService {
    /// 获取JWT密钥
    fn get_secret() -> String {
        env::var(constants::JWT_SECRET_ENV)
            .unwrap_or_else(|_| constants::JWT_SECRET_DEFAULT.to_string())
    }

    /// 生成访问令牌
    pub fn generate_access_token(user: &User) -> Result<String> {
        let secret = Self::get_secret();
        let now = Utc::now();
        let exp = (now + Duration::seconds(constants::ACCESS_TOKEN_EXPIRY)).timestamp() as usize;
        let iat = now.timestamp() as usize;
        let nbf = iat; // 立即生效
        let jti = Uuid::new_v4().to_string(); // 唯一JWT ID

        let claims = Claims {
            sub: user.id.clone(),
            email: user.email.clone(),
            nickname: user.nickname.clone(),
            exp,
            iat,
            nbf,
            iss: constants::JWT_ISSUER.to_string(),
            aud: constants::JWT_AUDIENCE.to_string(),
            jti,
            roles: vec!["user".to_string()], // 默认角色
            permissions: vec!["read".to_string()], // 默认权限
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        )
        .map_err(|e| anyhow!("JWT生成失败: {}", e))
    }

    /// 生成访问令牌（从基本信息）
    pub fn generate_access_token_from_info(user_id: &str, email: &str, nickname: &str) -> Result<String, AuthError> {
        let secret = Self::get_secret();
        let now = Utc::now();
        let exp = (now + Duration::seconds(constants::ACCESS_TOKEN_EXPIRY)).timestamp() as usize;
        let iat = now.timestamp() as usize;
        let nbf = iat;
        let jti = Uuid::new_v4().to_string();

        let claims = Claims {
            sub: user_id.to_string(),
            email: email.to_string(),
            nickname: nickname.to_string(),
            exp,
            iat,
            nbf,
            iss: constants::JWT_ISSUER.to_string(),
            aud: constants::JWT_AUDIENCE.to_string(),
            jti,
            roles: vec!["user".to_string()],
            permissions: vec!["read".to_string()],
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        )
        .map_err(|e| AuthError::Other(e.to_string()))
    }

    /// 生成刷新令牌
    pub fn generate_refresh_token(user: &User) -> Result<String> {
        let secret = Self::get_secret();
        let now = Utc::now();
        let exp = (now + Duration::seconds(constants::REFRESH_TOKEN_EXPIRY)).timestamp() as usize;
        let iat = now.timestamp() as usize;
        let jti = Uuid::new_v4().to_string();

        let claims = RefreshClaims {
            sub: user.id.clone(),
            exp,
            iat,
            iss: constants::JWT_ISSUER.to_string(),
            token_type: "refresh".to_string(),
            jti,
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        )
        .map_err(|e| anyhow!("刷新令牌生成失败: {}", e))
    }

    /// 验证访问令牌（仅JWT验证，不检查Redis）
    pub fn verify_access_token(token: &str) -> Result<Claims, AuthError> {
        let secret = Self::get_secret();

        // 配置JWT验证选项
        let mut validation = Validation::default();
        validation.validate_exp = true; // 验证过期时间
        validation.validate_nbf = true; // 验证不早于时间
        validation.set_issuer(&[constants::JWT_ISSUER]); // 验证签发者
        validation.set_audience(&[constants::JWT_AUDIENCE]); // 验证接收者

        // 解码和验证JWT
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        )
        .map_err(|e| match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => AuthError::ExpiredToken,
            jsonwebtoken::errors::ErrorKind::InvalidToken => AuthError::InvalidToken("无效的令牌".to_string()),
            jsonwebtoken::errors::ErrorKind::InvalidIssuer => AuthError::InvalidToken("令牌签发者无效".to_string()),
            jsonwebtoken::errors::ErrorKind::InvalidAudience => AuthError::InvalidToken("令牌接收者无效".to_string()),
            _ => AuthError::InvalidToken(e.to_string()),
        })?;

        Ok(token_data.claims)
    }

    /// 验证刷新令牌（仅JWT验证，不检查Redis）
    pub fn verify_refresh_token(token: &str) -> Result<RefreshClaims, AuthError> {
        let secret = Self::get_secret();

        // 配置JWT验证选项
        let mut validation = Validation::default();
        validation.validate_exp = true;
        validation.set_issuer(&[constants::JWT_ISSUER]);

        // 解码和验证刷新令牌
        let token_data = decode::<RefreshClaims>(
            token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        )
        .map_err(|e| match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => AuthError::ExpiredToken,
            _ => AuthError::InvalidToken(e.to_string()),
        })?;

        let claims = token_data.claims;
        
        // 检查令牌类型
        if claims.token_type != "refresh" {
            return Err(AuthError::InvalidToken("无效的令牌类型".to_string()));
        }

        Ok(claims)
    }

    /// 解码JWT Claims（不验证签名和过期时间，仅用于获取信息）
    pub fn decode_token_claims(token: &str) -> Result<Claims> {
        let secret = Self::get_secret();

        let mut validation = Validation::default();
        validation.validate_exp = false; // 这里只解码，不验证过期时间
        validation.validate_nbf = false;
        validation.insecure_disable_signature_validation(); // 仅用于获取claims，不验证签名

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        )
        .map_err(|e| anyhow!("令牌解码失败: {}", e))?;

        Ok(token_data.claims)
    }

    /// 从Authorization头提取Bearer token
    pub fn extract_bearer_token(auth_header: &str) -> Result<&str, AuthError> {
        auth_header
            .strip_prefix("Bearer ")
            .ok_or(AuthError::InvalidToken("Invalid token format".into()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use common::domain::dto::user::UserStatus;
    use chrono::Utc;

    fn create_test_user() -> User {
        User {
            id: "test-user-123".to_string(),
            email: "<EMAIL>".to_string(),
            password: "hashed_password".to_string(),
            nickname: "testuser".to_string(),
            status: UserStatus::Active,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[test]
    fn test_generate_and_verify_access_token() {
        let user = create_test_user();
        
        // 生成访问令牌
        let token = JwtService::generate_access_token(&user).expect("生成访问令牌失败");
        
        // 验证访问令牌
        let claims = JwtService::verify_access_token(&token).expect("验证访问令牌失败");
        
        assert_eq!(claims.sub, user.id);
        assert_eq!(claims.email, user.email);
        assert_eq!(claims.nickname, user.nickname);
        assert_eq!(claims.iss, constants::JWT_ISSUER);
        assert_eq!(claims.aud, constants::JWT_AUDIENCE);
    }

    #[test]
    fn test_generate_and_verify_refresh_token() {
        let user = create_test_user();
        
        // 生成刷新令牌
        let token = JwtService::generate_refresh_token(&user).expect("生成刷新令牌失败");
        
        // 验证刷新令牌
        let claims = JwtService::verify_refresh_token(&token).expect("验证刷新令牌失败");
        
        assert_eq!(claims.sub, user.id);
        assert_eq!(claims.token_type, "refresh");
        assert_eq!(claims.iss, constants::JWT_ISSUER);
    }

    #[test]
    fn test_extract_bearer_token() {
        let auth_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        let token = JwtService::extract_bearer_token(auth_header).expect("提取Bearer token失败");
        assert_eq!(token, "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...");
        
        // 测试无效格式
        let invalid_header = "InvalidFormat token";
        assert!(JwtService::extract_bearer_token(invalid_header).is_err());
    }

    #[test]
    fn test_generate_from_info() {
        let token = JwtService::generate_access_token_from_info(
            "user123",
            "<EMAIL>", 
            "testuser"
        ).expect("从信息生成访问令牌失败");
        
        let claims = JwtService::verify_access_token(&token).expect("验证令牌失败");
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.email, "<EMAIL>");
        assert_eq!(claims.nickname, "testuser");
    }
}
