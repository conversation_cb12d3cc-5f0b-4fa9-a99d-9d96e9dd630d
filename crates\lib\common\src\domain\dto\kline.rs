use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use std::str::FromStr;

use serde::{Deserialize, Serialize};

use crate::domain::entity::KlineEntity;

/// 极简K线数据结构体 - 仅包含核心OHLCV数据，用于JSON存储
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, PartialEq)]
pub struct SimpleKline {
    /// 开盘价 (Open) - 使用String防止精度问题
    pub o: String,
    /// 最高价 (High) - 使用String防止精度问题
    pub h: String,
    /// 最低价 (Low) - 使用String防止精度问题
    pub l: String,
    /// 收盘价 (Close) - 使用String防止精度问题
    pub c: String,
    /// 成交量 (Volume) - 使用String防止精度问题
    pub v: String,
    /// 时间戳 (Epoch timestamp)
    pub e: i64,
}

impl SimpleKline {
    /// 创建新的极简K线数据
    pub fn new(open: Decimal, high: Decimal, low: Decimal, close: Decimal, volume: Decimal, timestamp: i64) -> Self {
        Self {
            o: open.normalize().to_string(),
            h: high.normalize().to_string(),
            l: low.normalize().to_string(),
            c: close.normalize().to_string(),
            v: volume.normalize().to_string(),
            e: timestamp,
        }
    }

    /// 将String转换为Decimal，用于内部计算
    fn parse_decimal(s: &str) -> Result<Decimal, rust_decimal::Error> {
        Decimal::from_str(s)
    }

    /// 转换为JSON字符串
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 从JSON字符串解析
    pub fn from_json(json_str: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json_str)
    }

    /// 转换为紧凑JSON字符串（无空格）
    pub fn to_compact_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 验证数据有效性
    pub fn is_valid(&self) -> bool {
        // 尝试解析所有价格数据
        let Ok(open) = Self::parse_decimal(&self.o) else { return false; };
        let Ok(high) = Self::parse_decimal(&self.h) else { return false; };
        let Ok(low) = Self::parse_decimal(&self.l) else { return false; };
        let Ok(close) = Self::parse_decimal(&self.c) else { return false; };
        let Ok(volume) = Self::parse_decimal(&self.v) else { return false; };

        // 检查价格数据的基本有效性
        open > Decimal::ZERO 
            && high > Decimal::ZERO 
            && low > Decimal::ZERO 
            && close > Decimal::ZERO
            && volume >= Decimal::ZERO  // 成交量可以为0
            && high >= low  // 最高价应该大于等于最低价
            && high >= open  // 最高价应该大于等于开盘价
            && high >= close  // 最高价应该大于等于收盘价
            && low <= open  // 最低价应该小于等于开盘价
            && low <= close  // 最低价应该小于等于收盘价
            && self.e > 0  // 时间戳应该为正数
    }
}

impl From<&KlineDto> for SimpleKline {
    fn from(kline: &KlineDto) -> Self {
        Self {
            o: kline.open_price.normalize().to_string(),
            h: kline.high_price.normalize().to_string(),
            l: kline.low_price.normalize().to_string(),
            c: kline.close_price.normalize().to_string(),
            v: kline.volume.normalize().to_string(),
            e: kline.time.timestamp(),
        }
    }
}

impl From<KlineDto> for SimpleKline {
    fn from(kline: KlineDto) -> Self {
        Self {
            o: kline.open_price.normalize().to_string(),
            h: kline.high_price.normalize().to_string(),
            l: kline.low_price.normalize().to_string(),
            c: kline.close_price.normalize().to_string(),
            v: kline.volume.normalize().to_string(),
            e: kline.time.timestamp(),
        }
    }
}

impl From<&KlineEntity> for SimpleKline {
    fn from(entity: &KlineEntity) -> Self {
        Self {
            o: entity.open_price.normalize().to_string(),
            h: entity.high_price.normalize().to_string(),
            l: entity.low_price.normalize().to_string(),
            c: entity.close_price.normalize().to_string(),
            v: entity.volume.normalize().to_string(),
            e: entity.time.timestamp(),
        }
    }
}

impl From<KlineEntity> for SimpleKline {
    fn from(entity: KlineEntity) -> Self {
        Self {
            o: entity.open_price.normalize().to_string(),
            h: entity.high_price.normalize().to_string(),
            l: entity.low_price.normalize().to_string(),
            c: entity.close_price.normalize().to_string(),
            v: entity.volume.normalize().to_string(),
            e: entity.time.timestamp(),
        }
    }
}

/// K线数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct KlineDto {
    /// 时间戳（主键时间）
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 时间间隔（可选，用于兼容性）
    pub interval: Option<String>,
    /// 开盘时间
    pub open_time: DateTime<Utc>,
    /// 收盘时间
    pub close_time: DateTime<Utc>,
    /// 开盘价
    pub open_price: Decimal,
    /// 最高价
    pub high_price: Decimal,
    /// 最低价
    pub low_price: Decimal,
    /// 收盘价
    pub close_price: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 计价资产成交量
    pub quote_asset_volume: Decimal,
    /// 成交笔数
    pub number_of_trades: i32,
    /// 主动买入成交量
    pub taker_buy_base_asset_volume: Decimal,
    /// 主动买入计价资产成交量
    pub taker_buy_quote_asset_volume: Decimal,
}

impl From<KlineEntity> for KlineDto {
    fn from(entity: KlineEntity) -> Self {
        Self {
            time: entity.time,
            symbol: entity.symbol,
            interval: entity.interval,
            open_time: entity.time,  // 使用 time 作为 open_time
            close_time: entity.close_time,
            open_price: entity.open_price,
            high_price: entity.high_price,
            low_price: entity.low_price,
            close_price: entity.close_price,
            volume: entity.volume,
            quote_asset_volume: entity.quote_asset_volume,
            number_of_trades: entity.number_of_trades,
            taker_buy_base_asset_volume: entity.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: entity.taker_buy_quote_asset_volume,
        }
    }
}

impl From<KlineDto> for KlineEntity {
    fn from(dto: KlineDto) -> Self {
        Self {
            time: dto.time,
            symbol: dto.symbol,
            interval: dto.interval,
            open_price: dto.open_price,
            high_price: dto.high_price,
            low_price: dto.low_price,
            close_price: dto.close_price,
            volume: dto.volume,
            quote_asset_volume: dto.quote_asset_volume,
            number_of_trades: dto.number_of_trades,
            taker_buy_base_asset_volume: dto.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: dto.taker_buy_quote_asset_volume,
            close_time: dto.close_time,
        }
    }
}

/// Redis缓存相关的错误类型
#[derive(Debug, thiserror::Error)]
pub enum KlineCacheError {
    #[error("Invalid compact string format: {0}")]
    InvalidFormat(String),
    #[error("Failed to parse decimal: {0}")]
    DecimalParseError(#[from] rust_decimal::Error),
    #[error("Failed to parse timestamp: {0}")]
    TimestampParseError(String),
}

impl KlineDto {
    
    /// 转换为缓存用的JSON字符串格式
    /// 使用SimpleKline格式：{o:open, h:high, l:low, c:close, v:volume, e:timestamp}
    pub fn to_cache_json(&self) -> Result<String, serde_json::Error> {
        let simple_kline = self.to_simple_kline();
        simple_kline.to_json()
    }

    /// 转换为极简K线结构体
    pub fn to_simple_kline(&self) -> SimpleKline {
        SimpleKline::from(self)
    }

    /// 将K线数据转换为JSON格式存储（使用极简结构体）
    pub fn to_json_string(&self) -> Result<String, serde_json::Error> {
        let simple_kline = self.to_simple_kline();
        simple_kline.to_json()
    }

    /// 从JSON字符串和元数据创建KlineDto
    pub fn from_json_string(
        json_str: &str,
        symbol: &str,
        interval: &str,
        timestamp: i64,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let simple_kline = SimpleKline::from_json(json_str)?;
        let time = DateTime::from_timestamp(timestamp, 0)
            .ok_or("Invalid timestamp")?;
        
        Ok(Self {
            time,
            symbol: symbol.to_string(),
            interval: Some(interval.to_string()),
            open_time: time,
            close_time: time, // 简化处理，实际应该根据interval计算
            open_price: SimpleKline::parse_decimal(&simple_kline.o)?,
            high_price: SimpleKline::parse_decimal(&simple_kline.h)?,
            low_price: SimpleKline::parse_decimal(&simple_kline.l)?,
            close_price: SimpleKline::parse_decimal(&simple_kline.c)?,
            volume: SimpleKline::parse_decimal(&simple_kline.v)?,
            quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            number_of_trades: 0, // 缓存中不存储，需要时从DB获取
            taker_buy_base_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            taker_buy_quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
        })
    }
    pub fn from_cache_json(
        json_str: &str,
        symbol: &str,
        interval: &str,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let simple_kline = SimpleKline::from_json(json_str)?;
        let time = DateTime::from_timestamp(simple_kline.e, 0)
            .ok_or("Invalid timestamp")?;
        
        Ok(Self {
            time,
            symbol: symbol.to_string(),
            interval: Some(interval.to_string()),
            open_time: time,
            close_time: time, // 简化处理，实际应该根据interval计算
            open_price: SimpleKline::parse_decimal(&simple_kline.o)?,
            high_price: SimpleKline::parse_decimal(&simple_kline.h)?,
            low_price: SimpleKline::parse_decimal(&simple_kline.l)?,
            close_price: SimpleKline::parse_decimal(&simple_kline.c)?,
            volume: SimpleKline::parse_decimal(&simple_kline.v)?,
            quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            number_of_trades: 0, // 缓存中不存储，需要时从DB获取
            taker_buy_base_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            taker_buy_quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
        })
    }

    /// 获取用于Redis Sorted Set的score值
    /// 使用时间戳作为score，便于按时间范围查询
    pub fn get_redis_score(&self) -> f64 {
        self.time.timestamp() as f64
    }

    /// 生成Redis键名
    /// 格式: kline:{symbol}:{date}
    /// 例如: kline:btcusdt:20240615
    pub fn generate_redis_key(symbol: &str, date: &str) -> String {
        format!("kline:{}:{}", symbol.to_lowercase(), date)
    }

    /// 从时间戳生成日期字符串 (YYYYMMDD格式)
    pub fn timestamp_to_date_string(timestamp: i64) -> String {
        DateTime::from_timestamp(timestamp, 0)
            .unwrap_or_else(|| Utc::now())
            .format("%Y%m%d")
            .to_string()
    }
}