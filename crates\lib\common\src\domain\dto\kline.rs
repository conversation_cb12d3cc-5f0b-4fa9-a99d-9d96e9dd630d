use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use std::str::FromStr;

use serde::{Deserialize, Serialize};

use crate::domain::entity::KlineEntity;

/// 极简K线数据结构体 - 仅包含核心OHLCV数据，用于JSON存储
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, PartialEq)]
pub struct SimpleKline {
    /// 开盘价 (Open) - 使用String防止精度问题
    pub o: String,
    /// 最高价 (High) - 使用String防止精度问题
    pub h: String,
    /// 最低价 (Low) - 使用String防止精度问题
    pub l: String,
    /// 收盘价 (Close) - 使用String防止精度问题
    pub c: String,
    /// 成交量 (Volume) - 使用String防止精度问题
    pub v: String,
    /// 时间戳 (Epoch timestamp)
    pub e: i64,
}

impl SimpleKline {
    /// 创建新的极简K线数据
    pub fn new(open: Decimal, high: Decimal, low: Decimal, close: Decimal, volume: Decimal, timestamp: i64) -> Self {
        Self {
            o: open.normalize().to_string(),
            h: high.normalize().to_string(),
            l: low.normalize().to_string(),
            c: close.normalize().to_string(),
            v: volume.normalize().to_string(),
            e: timestamp,
        }
    }

    /// 将String转换为Decimal，用于内部计算
    fn parse_decimal(s: &str) -> Result<Decimal, rust_decimal::Error> {
        Decimal::from_str(s)
    }

    /// 转换为JSON字符串
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 从JSON字符串解析
    pub fn from_json(json_str: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json_str)
    }

    /// 转换为紧凑JSON字符串（无空格）
    pub fn to_compact_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 验证数据有效性
    pub fn is_valid(&self) -> bool {
        // 尝试解析所有价格数据
        let Ok(open) = Self::parse_decimal(&self.o) else { return false; };
        let Ok(high) = Self::parse_decimal(&self.h) else { return false; };
        let Ok(low) = Self::parse_decimal(&self.l) else { return false; };
        let Ok(close) = Self::parse_decimal(&self.c) else { return false; };
        let Ok(volume) = Self::parse_decimal(&self.v) else { return false; };

        // 检查价格数据的基本有效性
        open > Decimal::ZERO 
            && high > Decimal::ZERO 
            && low > Decimal::ZERO 
            && close > Decimal::ZERO
            && volume >= Decimal::ZERO  // 成交量可以为0
            && high >= low  // 最高价应该大于等于最低价
            && high >= open  // 最高价应该大于等于开盘价
            && high >= close  // 最高价应该大于等于收盘价
            && low <= open  // 最低价应该小于等于开盘价
            && low <= close  // 最低价应该小于等于收盘价
            && self.e > 0  // 时间戳应该为正数
    }
}

impl From<&KlineDto> for SimpleKline {
    fn from(kline: &KlineDto) -> Self {
        Self {
            o: kline.open_price.normalize().to_string(),
            h: kline.high_price.normalize().to_string(),
            l: kline.low_price.normalize().to_string(),
            c: kline.close_price.normalize().to_string(),
            v: kline.volume.normalize().to_string(),
            e: kline.time.timestamp(),
        }
    }
}

impl From<KlineDto> for SimpleKline {
    fn from(kline: KlineDto) -> Self {
        Self {
            o: kline.open_price.normalize().to_string(),
            h: kline.high_price.normalize().to_string(),
            l: kline.low_price.normalize().to_string(),
            c: kline.close_price.normalize().to_string(),
            v: kline.volume.normalize().to_string(),
            e: kline.time.timestamp(),
        }
    }
}

impl From<&KlineEntity> for SimpleKline {
    fn from(entity: &KlineEntity) -> Self {
        Self {
            o: entity.open_price.normalize().to_string(),
            h: entity.high_price.normalize().to_string(),
            l: entity.low_price.normalize().to_string(),
            c: entity.close_price.normalize().to_string(),
            v: entity.volume.normalize().to_string(),
            e: entity.time.timestamp(),
        }
    }
}

impl From<KlineEntity> for SimpleKline {
    fn from(entity: KlineEntity) -> Self {
        Self {
            o: entity.open_price.normalize().to_string(),
            h: entity.high_price.normalize().to_string(),
            l: entity.low_price.normalize().to_string(),
            c: entity.close_price.normalize().to_string(),
            v: entity.volume.normalize().to_string(),
            e: entity.time.timestamp(),
        }
    }
}

/// K线数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct KlineDto {
    /// 时间戳（主键时间）
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 时间间隔
    pub interval: String,
    /// 开盘时间
    pub open_time: DateTime<Utc>,
    /// 收盘时间
    pub close_time: DateTime<Utc>,
    /// 开盘价
    pub open_price: Decimal,
    /// 最高价
    pub high_price: Decimal,
    /// 最低价
    pub low_price: Decimal,
    /// 收盘价
    pub close_price: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 计价资产成交量
    pub quote_asset_volume: Decimal,
    /// 成交笔数
    pub number_of_trades: i32,
    /// 主动买入成交量
    pub taker_buy_base_asset_volume: Decimal,
    /// 主动买入计价资产成交量
    pub taker_buy_quote_asset_volume: Decimal,
}

impl From<KlineEntity> for KlineDto {
    fn from(entity: KlineEntity) -> Self {
        Self {
            time: entity.time,
            symbol: entity.symbol,
            interval: entity.interval,
            open_time: entity.time,  // 使用 time 作为 open_time
            close_time: entity.close_time,
            open_price: entity.open_price,
            high_price: entity.high_price,
            low_price: entity.low_price,
            close_price: entity.close_price,
            volume: entity.volume,
            quote_asset_volume: entity.quote_asset_volume,
            number_of_trades: entity.number_of_trades,
            taker_buy_base_asset_volume: entity.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: entity.taker_buy_quote_asset_volume,
        }
    }
}

impl From<KlineDto> for KlineEntity {
    fn from(dto: KlineDto) -> Self {
        Self {
            time: dto.time,
            symbol: dto.symbol,
            interval: dto.interval,
            open_price: dto.open_price,
            high_price: dto.high_price,
            low_price: dto.low_price,
            close_price: dto.close_price,
            volume: dto.volume,
            quote_asset_volume: dto.quote_asset_volume,
            number_of_trades: dto.number_of_trades,
            taker_buy_base_asset_volume: dto.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: dto.taker_buy_quote_asset_volume,
            close_time: dto.close_time,
        }
    }
}

/// Redis缓存相关的错误类型
#[derive(Debug, thiserror::Error)]
pub enum KlineCacheError {
    #[error("Invalid compact string format: {0}")]
    InvalidFormat(String),
    #[error("Failed to parse decimal: {0}")]
    DecimalParseError(#[from] rust_decimal::Error),
    #[error("Failed to parse timestamp: {0}")]
    TimestampParseError(String),
}

impl KlineDto {
    
    /// 转换为缓存用的JSON字符串格式
    /// 使用SimpleKline格式：{o:open, h:high, l:low, c:close, v:volume, e:timestamp}
    pub fn to_cache_json(&self) -> Result<String, serde_json::Error> {
        let simple_kline = self.to_simple_kline();
        simple_kline.to_json()
    }

    /// 转换为极简K线结构体
    pub fn to_simple_kline(&self) -> SimpleKline {
        SimpleKline::from(self)
    }

    /// 将K线数据转换为JSON格式存储（使用极简结构体）
    pub fn to_json_string(&self) -> Result<String, serde_json::Error> {
        let simple_kline = self.to_simple_kline();
        simple_kline.to_json()
    }

    /// 从JSON字符串和元数据创建KlineDto
    pub fn from_json_string(
        json_str: &str,
        symbol: &str,
        interval: &str,
        timestamp: i64,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let simple_kline = SimpleKline::from_json(json_str)?;
        let time = DateTime::from_timestamp(timestamp, 0)
            .ok_or("Invalid timestamp")?;
        
        Ok(Self {
            time,
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            open_time: time,
            close_time: time, // 简化处理，实际应该根据interval计算
            open_price: SimpleKline::parse_decimal(&simple_kline.o)?,
            high_price: SimpleKline::parse_decimal(&simple_kline.h)?,
            low_price: SimpleKline::parse_decimal(&simple_kline.l)?,
            close_price: SimpleKline::parse_decimal(&simple_kline.c)?,
            volume: SimpleKline::parse_decimal(&simple_kline.v)?,
            quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            number_of_trades: 0, // 缓存中不存储，需要时从DB获取
            taker_buy_base_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            taker_buy_quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
        })
    }

    /// 从缓存JSON格式解析K线数据
    /// 
    /// # Arguments
    /// * `json_str` - JSON格式字符串 {"o":"open","h":"high","l":"low","c":"close","v":"volume","e":timestamp}
    /// * `symbol` - 交易对符号
    /// * `interval` - 时间间隔
    /// 
    /// # Examples
    /// ```
    /// use common::domain::dto::KlineDto;
    /// 
    /// let json_str = r#"{"o":"50000","h":"50100","l":"49900","c":"50050","v":"1.5","e":1718448600}"#;
    /// let kline = KlineDto::from_cache_json(json_str, "BTCUSDT", "1m").unwrap();
    /// 
    /// assert_eq!(kline.symbol, "BTCUSDT");
    /// assert_eq!(kline.interval, "1m");
    /// ```
    pub fn from_cache_json(
        json_str: &str,
        symbol: &str,
        interval: &str,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let simple_kline = SimpleKline::from_json(json_str)?;
        let time = DateTime::from_timestamp(simple_kline.e, 0)
            .ok_or("Invalid timestamp")?;
        
        Ok(Self {
            time,
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            open_time: time,
            close_time: time, // 简化处理，实际应该根据interval计算
            open_price: SimpleKline::parse_decimal(&simple_kline.o)?,
            high_price: SimpleKline::parse_decimal(&simple_kline.h)?,
            low_price: SimpleKline::parse_decimal(&simple_kline.l)?,
            close_price: SimpleKline::parse_decimal(&simple_kline.c)?,
            volume: SimpleKline::parse_decimal(&simple_kline.v)?,
            quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            number_of_trades: 0, // 缓存中不存储，需要时从DB获取
            taker_buy_base_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
            taker_buy_quote_asset_volume: Decimal::ZERO, // 缓存中不存储，需要时从DB获取
        })
    }

    /// 获取用于Redis Sorted Set的score值
    /// 使用时间戳作为score，便于按时间范围查询
    pub fn get_redis_score(&self) -> f64 {
        self.time.timestamp() as f64
    }

    /// 生成Redis键名
    /// 格式: kline:{symbol}:{date}
    /// 例如: kline:btcusdt:20240615
    pub fn generate_redis_key(symbol: &str, date: &str) -> String {
        format!("kline:{}:{}", symbol.to_lowercase(), date)
    }

    /// 从时间戳生成日期字符串 (YYYYMMDD格式)
    pub fn timestamp_to_date_string(timestamp: i64) -> String {
        DateTime::from_timestamp(timestamp, 0)
            .unwrap_or_else(|| Utc::now())
            .format("%Y%m%d")
            .to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::TimeZone;

    #[test]
    fn test_kline_cache_json_conversion() {
        let kline = KlineDto {
            time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            open_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            close_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 31, 0).unwrap(),
            open_price: Decimal::from_str("50000.00").unwrap(),
            high_price: Decimal::from_str("50100.00").unwrap(),
            low_price: Decimal::from_str("49900.00").unwrap(),
            close_price: Decimal::from_str("50050.00").unwrap(),
            volume: Decimal::from_str("1.5").unwrap(),
            quote_asset_volume: Decimal::from_str("75075.00").unwrap(),
            number_of_trades: 25,
            taker_buy_base_asset_volume: Decimal::from_str("0.8").unwrap(),
            taker_buy_quote_asset_volume: Decimal::from_str("40040.00").unwrap(),
        };

        // 测试转换为缓存JSON字符串
        let cache_json = kline.to_cache_json().unwrap();
        let expected_timestamp = kline.time.timestamp();
        
        // 验证JSON包含所有必要字段
        assert!(cache_json.contains("\"o\":\"50000\""));
        assert!(cache_json.contains("\"h\":\"50100\""));
        assert!(cache_json.contains("\"l\":\"49900\""));
        assert!(cache_json.contains("\"c\":\"50050\""));
        assert!(cache_json.contains("\"v\":\"1.5\""));
        assert!(cache_json.contains(&format!("\"e\":{}", expected_timestamp)));

        // 测试从缓存JSON解析
        let parsed_kline = KlineDto::from_cache_json(&cache_json, "BTCUSDT", "1m").unwrap();
        
        assert_eq!(parsed_kline.symbol, "BTCUSDT");
        assert_eq!(parsed_kline.interval, "1m");
        assert_eq!(parsed_kline.open_price, Decimal::from_str("50000").unwrap());
        assert_eq!(parsed_kline.high_price, Decimal::from_str("50100").unwrap());
        assert_eq!(parsed_kline.low_price, Decimal::from_str("49900").unwrap());
        assert_eq!(parsed_kline.close_price, Decimal::from_str("50050").unwrap());
        assert_eq!(parsed_kline.volume, Decimal::from_str("1.5").unwrap());
        assert_eq!(parsed_kline.time.timestamp(), expected_timestamp);
    }

    #[test]
    fn test_redis_key_generation() {
        let key = KlineDto::generate_redis_key("BTCUSDT", "20240615");
        assert_eq!(key, "kline:btcusdt:20240615");
    }

    #[test]
    fn test_timestamp_to_date_string() {
        let date_str = KlineDto::timestamp_to_date_string(1718448600);
        assert_eq!(date_str, "20240615");
    }

    #[test]
    fn test_redis_score() {
        let time = Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap();
        let kline = KlineDto {
            time,
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            open_time: time,
            close_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 31, 0).unwrap(),
            open_price: Decimal::from_str("50000.00").unwrap(),
            high_price: Decimal::from_str("50100.00").unwrap(),
            low_price: Decimal::from_str("49900.00").unwrap(),
            close_price: Decimal::from_str("50050.00").unwrap(),
            volume: Decimal::from_str("1.5").unwrap(),
            quote_asset_volume: Decimal::from_str("75075.00").unwrap(),
            number_of_trades: 25,
            taker_buy_base_asset_volume: Decimal::from_str("0.8").unwrap(),
            taker_buy_quote_asset_volume: Decimal::from_str("40040.00").unwrap(),
        };

        let score = kline.get_redis_score();
        let expected_score = time.timestamp() as f64;
        assert_eq!(score, expected_score);
    }

    #[test]
    fn test_simple_kline_creation() {
        let simple_kline = SimpleKline::new(
            Decimal::from_str("50000.00").unwrap(),
            Decimal::from_str("50100.00").unwrap(),
            Decimal::from_str("49900.00").unwrap(),
            Decimal::from_str("50050.00").unwrap(),
            Decimal::from_str("1.5").unwrap(),
            1718448600,
        );

        assert_eq!(simple_kline.o, "50000");
        assert_eq!(simple_kline.h, "50100");
        assert_eq!(simple_kline.l, "49900");
        assert_eq!(simple_kline.c, "50050");
        assert_eq!(simple_kline.v, "1.5");
        assert_eq!(simple_kline.e, 1718448600);
    }

    #[test]
    fn test_simple_kline_json_conversion() {
        let simple_kline = SimpleKline {
            o: "50000".to_string(),
            h: "50100".to_string(),
            l: "49900".to_string(),
            c: "50050".to_string(),
            v: "1.5".to_string(),
            e: 1718448600,
        };

        // 测试转换为JSON
        let json_str = simple_kline.to_json().unwrap();
        assert!(json_str.contains("\"o\":\"50000\""));
        assert!(json_str.contains("\"h\":\"50100\""));
        assert!(json_str.contains("\"l\":\"49900\""));
        assert!(json_str.contains("\"c\":\"50050\""));
        assert!(json_str.contains("\"v\":\"1.5\""));
        assert!(json_str.contains("\"e\":1718448600"));

        // 测试从JSON解析
        let parsed_kline = SimpleKline::from_json(&json_str).unwrap();
        assert_eq!(parsed_kline.o, "50000");
        assert_eq!(parsed_kline.h, "50100");
        assert_eq!(parsed_kline.l, "49900");
        assert_eq!(parsed_kline.c, "50050");
        assert_eq!(parsed_kline.v, "1.5");
        assert_eq!(parsed_kline.e, 1718448600);
    }

    #[test]
    fn test_simple_kline_validation() {
        // 有效的K线数据
        let valid_kline = SimpleKline {
            o: "50000".to_string(),
            h: "50100".to_string(),
            l: "49900".to_string(),
            c: "50050".to_string(),
            v: "1.5".to_string(),
            e: 1718448600,
        };
        assert!(valid_kline.is_valid());

        // 无效的K线数据 - 最高价小于最低价
        let invalid_kline = SimpleKline {
            o: "50000".to_string(),
            h: "49800".to_string(), // 最高价小于最低价
            l: "49900".to_string(),
            c: "50050".to_string(),
            v: "1.5".to_string(),
            e: 1718448600,
        };
        assert!(!invalid_kline.is_valid());

        // 无效的K线数据 - 负数价格
        let invalid_price_kline = SimpleKline {
            o: "-50000".to_string(),
            h: "50100".to_string(),
            l: "49900".to_string(),
            c: "50050".to_string(),
            v: "1.5".to_string(),
            e: 1718448600,
        };
        assert!(!invalid_price_kline.is_valid());

        // 无效的时间戳
        let invalid_timestamp_kline = SimpleKline {
            o: "50000".to_string(),
            h: "50100".to_string(),
            l: "49900".to_string(),
            c: "50050".to_string(),
            v: "1.5".to_string(),
            e: -1, // 负数时间戳
        };
        assert!(!invalid_timestamp_kline.is_valid());
    }

    #[test]
    fn test_kline_dto_to_simple_kline_conversion() {
        let kline_dto = KlineDto {
            time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            open_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            close_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 31, 0).unwrap(),
            open_price: Decimal::from_str("50000.00").unwrap(),
            high_price: Decimal::from_str("50100.00").unwrap(),
            low_price: Decimal::from_str("49900.00").unwrap(),
            close_price: Decimal::from_str("50050.00").unwrap(),
            volume: Decimal::from_str("1.5").unwrap(),
            quote_asset_volume: Decimal::from_str("75075.00").unwrap(),
            number_of_trades: 25,
            taker_buy_base_asset_volume: Decimal::from_str("0.8").unwrap(),
            taker_buy_quote_asset_volume: Decimal::from_str("40040.00").unwrap(),
        };

        let simple_kline = kline_dto.to_simple_kline();
        
        assert_eq!(simple_kline.o, "50000");
        assert_eq!(simple_kline.h, "50100");
        assert_eq!(simple_kline.l, "49900");
        assert_eq!(simple_kline.c, "50050");
        assert_eq!(simple_kline.v, "1.5");
        assert_eq!(simple_kline.e, kline_dto.time.timestamp());

        // 测试From trait实现
        let simple_kline_from = SimpleKline::from(&kline_dto);
        assert_eq!(simple_kline_from.o, simple_kline.o);
        assert_eq!(simple_kline_from.h, simple_kline.h);
        assert_eq!(simple_kline_from.l, simple_kline.l);
        assert_eq!(simple_kline_from.c, simple_kline.c);
        assert_eq!(simple_kline_from.v, simple_kline.v);
        assert_eq!(simple_kline_from.e, simple_kline.e);
    }

    #[test]
    fn test_kline_dto_json_string_conversion() {
        let kline_dto = KlineDto {
            time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            open_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            close_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 31, 0).unwrap(),
            open_price: Decimal::from_str("50000.00").unwrap(),
            high_price: Decimal::from_str("50100.00").unwrap(),
            low_price: Decimal::from_str("49900.00").unwrap(),
            close_price: Decimal::from_str("50050.00").unwrap(),
            volume: Decimal::from_str("1.5").unwrap(),
            quote_asset_volume: Decimal::from_str("75075.00").unwrap(),
            number_of_trades: 25,
            taker_buy_base_asset_volume: Decimal::from_str("0.8").unwrap(),
            taker_buy_quote_asset_volume: Decimal::from_str("40040.00").unwrap(),
        };

        // 测试to_json_string方法
        let json_string = kline_dto.to_json_string().unwrap();
        let expected_timestamp = kline_dto.time.timestamp();
        
        assert!(json_string.contains("\"o\":\"50000\""));
        assert!(json_string.contains("\"h\":\"50100\""));
        assert!(json_string.contains("\"l\":\"49900\""));
        assert!(json_string.contains("\"c\":\"50050\""));
        assert!(json_string.contains("\"v\":\"1.5\""));
        assert!(json_string.contains(&format!("\"e\":{}", expected_timestamp)));

        // 测试from_json_string方法
        let parsed_kline = KlineDto::from_json_string(
            &json_string,
            "BTCUSDT",
            "1m",
            expected_timestamp,
        ).unwrap();
        
        assert_eq!(parsed_kline.symbol, "BTCUSDT");
        assert_eq!(parsed_kline.interval, "1m");
        assert_eq!(parsed_kline.open_price, Decimal::from_str("50000").unwrap());
        assert_eq!(parsed_kline.high_price, Decimal::from_str("50100").unwrap());
        assert_eq!(parsed_kline.low_price, Decimal::from_str("49900").unwrap());
        assert_eq!(parsed_kline.close_price, Decimal::from_str("50050").unwrap());
        assert_eq!(parsed_kline.volume, Decimal::from_str("1.5").unwrap());
        assert_eq!(parsed_kline.time.timestamp(), expected_timestamp);
    }
}
