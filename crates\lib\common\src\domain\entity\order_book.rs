use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 深度数据实体 - 数据库映射
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct OrderBookEntity {
    /// 时间戳
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 最后更新ID
    pub last_update_id: i64,
    /// 买单深度（JSON格式存储）
    pub bids: sqlx::types::<PERSON><PERSON><Vec<(Decimal, Decimal)>>,
    /// 卖单深度（JSON格式存储）
    pub asks: sqlx::types::<PERSON><PERSON><Vec<(Decimal, Decimal)>>,
}

