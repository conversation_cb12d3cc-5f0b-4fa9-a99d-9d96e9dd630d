#!/bin/bash

# 市场数据维护服务测试运行脚本
# 支持单元测试、集成测试、基准测试和覆盖率分析

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
市场数据维护服务测试运行脚本

用法: $0 [选项]

选项:
    -u, --unit              运行单元测试
    -i, --integration       运行集成测试
    -b, --benchmark         运行基准测试
    -c, --coverage          运行覆盖率分析
    -a, --all               运行所有测试
    -p, --package PACKAGE   指定测试包 (service|repository|common|exchange)
    -t, --test TEST         运行特定测试
    -v, --verbose           详细输出
    -q, --quiet             静默模式
    --no-fail-fast          不在第一个失败时停止
    --release               使用release模式
    -h, --help              显示此帮助信息

示例:
    $0 --unit --package service
    $0 --integration --verbose
    $0 --benchmark --test benchmark_state_manager
    $0 --coverage --package service
    $0 --all

EOF
}

# 默认配置
RUN_UNIT=false
RUN_INTEGRATION=false
RUN_BENCHMARK=false
RUN_COVERAGE=false
PACKAGE=""
SPECIFIC_TEST=""
VERBOSE=false
QUIET=false
FAIL_FAST=true
RELEASE_MODE=false

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--unit)
                RUN_UNIT=true
                shift
                ;;
            -i|--integration)
                RUN_INTEGRATION=true
                shift
                ;;
            -b|--benchmark)
                RUN_BENCHMARK=true
                shift
                ;;
            -c|--coverage)
                RUN_COVERAGE=true
                shift
                ;;
            -a|--all)
                RUN_UNIT=true
                RUN_INTEGRATION=true
                RUN_BENCHMARK=true
                shift
                ;;
            -p|--package)
                PACKAGE="$2"
                shift 2
                ;;
            -t|--test)
                SPECIFIC_TEST="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -q|--quiet)
                QUIET=true
                shift
                ;;
            --no-fail-fast)
                FAIL_FAST=false
                shift
                ;;
            --release)
                RELEASE_MODE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 构建测试命令
build_test_command() {
    local test_type="$1"
    local cmd="cargo test"
    
    # 添加包参数
    if [[ -n "$PACKAGE" ]]; then
        cmd="$cmd -p $PACKAGE"
    fi
    
    # 添加特定测试
    if [[ -n "$SPECIFIC_TEST" ]]; then
        cmd="$cmd $SPECIFIC_TEST"
    fi
    
    # 添加模式参数
    if [[ "$RELEASE_MODE" == true ]]; then
        cmd="$cmd --release"
    fi
    
    # 添加详细输出
    if [[ "$VERBOSE" == true ]]; then
        cmd="$cmd --verbose"
    fi
    
    # 添加静默模式
    if [[ "$QUIET" == true ]]; then
        cmd="$cmd --quiet"
    fi
    
    # 添加fail-fast设置
    if [[ "$FAIL_FAST" == false ]]; then
        cmd="$cmd --no-fail-fast"
    fi
    
    # 根据测试类型添加特定参数
    case "$test_type" in
        "unit")
            cmd="$cmd --lib"
            ;;
        "integration")
            cmd="$cmd --test integration_tests"
            ;;
        "benchmark")
            cmd="$cmd --bench benchmarks"
            if [[ "$VERBOSE" == true ]]; then
                cmd="$cmd -- --nocapture"
            fi
            ;;
    esac
    
    echo "$cmd"
}

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    
    local cmd=$(build_test_command "unit")
    print_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        print_success "单元测试通过"
        return 0
    else
        print_error "单元测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    print_warning "注意: 集成测试需要数据库连接"
    
    # 检查环境变量
    if [[ -z "${DATABASE_URL:-}" ]]; then
        print_warning "DATABASE_URL未设置，某些集成测试可能被跳过"
    fi
    
    local cmd=$(build_test_command "integration")
    print_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        print_success "集成测试通过"
        return 0
    else
        print_error "集成测试失败"
        return 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    print_info "运行基准测试..."
    
    local cmd=$(build_test_command "benchmark")
    print_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        print_success "基准测试完成"
        return 0
    else
        print_error "基准测试失败"
        return 1
    fi
}

# 运行覆盖率分析
run_coverage_analysis() {
    print_info "运行覆盖率分析..."
    
    # 检查是否安装了tarpaulin
    if ! command -v cargo-tarpaulin &> /dev/null; then
        print_warning "cargo-tarpaulin未安装，正在安装..."
        cargo install cargo-tarpaulin
    fi
    
    local cmd="cargo tarpaulin --verbose --all-features --workspace --timeout 120"
    
    if [[ -n "$PACKAGE" ]]; then
        cmd="$cmd --packages $PACKAGE"
    fi
    
    # 输出格式
    cmd="$cmd --out Html --out Xml"
    
    print_info "执行命令: $cmd"
    
    if eval "$cmd"; then
        print_success "覆盖率分析完成"
        print_info "HTML报告: tarpaulin-report.html"
        print_info "XML报告: cobertura.xml"
        return 0
    else
        print_error "覆盖率分析失败"
        return 1
    fi
}

# 检查环境
check_environment() {
    print_info "检查测试环境..."
    
    # 检查Rust工具链
    if ! command -v cargo &> /dev/null; then
        print_error "Cargo未安装"
        exit 1
    fi
    
    # 检查项目结构
    if [[ ! -f "Cargo.toml" ]]; then
        print_error "不在Rust项目根目录"
        exit 1
    fi
    
    # 检查测试目录
    if [[ ! -d "crates/lib/service/src/market_data" ]]; then
        print_error "找不到服务模块目录"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 清理测试环境
cleanup_test_environment() {
    print_info "清理测试环境..."
    
    # 清理临时文件
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "test_*.db" -delete 2>/dev/null || true
    
    # 清理测试日志
    if [[ -d "test_logs" ]]; then
        rm -rf test_logs
    fi
    
    print_success "测试环境清理完成"
}

# 生成测试报告
generate_test_report() {
    print_info "生成测试报告..."
    
    local report_file="test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# 市场数据维护服务测试报告

生成时间: $(date)

## 测试配置

- 包: ${PACKAGE:-"所有包"}
- 特定测试: ${SPECIFIC_TEST:-"无"}
- 详细模式: $VERBOSE
- Release模式: $RELEASE_MODE

## 测试结果

EOF

    if [[ "$RUN_UNIT" == true ]]; then
        echo "- ✅ 单元测试: 已运行" >> "$report_file"
    fi
    
    if [[ "$RUN_INTEGRATION" == true ]]; then
        echo "- ✅ 集成测试: 已运行" >> "$report_file"
    fi
    
    if [[ "$RUN_BENCHMARK" == true ]]; then
        echo "- ✅ 基准测试: 已运行" >> "$report_file"
    fi
    
    if [[ "$RUN_COVERAGE" == true ]]; then
        echo "- ✅ 覆盖率分析: 已运行" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "## 环境信息" >> "$report_file"
    echo "" >> "$report_file"
    echo "- Rust版本: $(rustc --version)" >> "$report_file"
    echo "- Cargo版本: $(cargo --version)" >> "$report_file"
    echo "- 操作系统: $(uname -s)" >> "$report_file"
    
    print_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    print_info "市场数据维护服务测试运行器"
    print_info "================================"
    
    # 解析参数
    parse_args "$@"
    
    # 如果没有指定任何测试类型，默认运行单元测试
    if [[ "$RUN_UNIT" == false && "$RUN_INTEGRATION" == false && "$RUN_BENCHMARK" == false && "$RUN_COVERAGE" == false ]]; then
        print_info "未指定测试类型，默认运行单元测试"
        RUN_UNIT=true
    fi
    
    # 检查环境
    check_environment
    
    # 清理环境
    cleanup_test_environment
    
    local exit_code=0
    
    # 运行测试
    if [[ "$RUN_UNIT" == true ]]; then
        if ! run_unit_tests; then
            exit_code=1
        fi
    fi
    
    if [[ "$RUN_INTEGRATION" == true ]]; then
        if ! run_integration_tests; then
            exit_code=1
        fi
    fi
    
    if [[ "$RUN_BENCHMARK" == true ]]; then
        if ! run_benchmark_tests; then
            exit_code=1
        fi
    fi
    
    if [[ "$RUN_COVERAGE" == true ]]; then
        if ! run_coverage_analysis; then
            exit_code=1
        fi
    fi
    
    # 生成报告
    generate_test_report
    
    # 最终清理
    cleanup_test_environment
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "所有测试完成"
    else
        print_error "某些测试失败"
    fi
    
    exit $exit_code
}

# 运行主函数
main "$@"
