// =====================================================
// 重构的K线数据维护服务
// 基于新架构：TimescaleDB + Redis + 连续聚合
// =====================================================

use chrono::{DateTime, Utc, Duration};
use anyhow::Result;
use tracing::{info, warn, error, debug};
use tokio::time::{interval, Duration as TokioDuration};
use std::collections::HashMap;

use repository::timescale::kline_repository_v2::{
    KlineRepositoryV2, InsertKlineRequest, IntegrityCheckResult, 
    TimeInterval, DataLagStatus, LagStatus
};
use repository::cache::kline_cache::KlineCache;
use exchange::{ExchangeClient, KlineQuery, UniversalKline};

/// 维护服务配置
#[derive(Debug, Clone)]
pub struct MaintenanceConfig {
    /// 交易所名称
    pub exchange_name: String,
    /// 活跃交易对列表
    pub symbols: Vec<String>,
    /// 增量更新间隔（分钟）
    pub update_interval_minutes: u64,
    /// 完整性检查间隔（小时）
    pub integrity_check_interval_hours: u64,
    /// 数据保留天数
    pub retention_days: i32,
    /// 是否启用自动修复
    pub enable_auto_fix: bool,
    /// 是否启用缓存
    pub enable_cache: bool,
}

impl Default for MaintenanceConfig {
    fn default() -> Self {
        Self {
            exchange_name: "binance".to_string(),
            symbols: vec!["BTCUSDT".to_string(), "ETHUSDT".to_string()],
            update_interval_minutes: 5,
            integrity_check_interval_hours: 6,
            retention_days: 730, // 2年
            enable_auto_fix: true,
            enable_cache: true,
        }
    }
}

/// 重构的K线维护服务
pub struct KlineMaintenanceServiceV2 {
    config: MaintenanceConfig,
    repository: KlineRepositoryV2,
    exchange_client: Box<dyn ExchangeClient>,
}

impl KlineMaintenanceServiceV2 {
    /// 创建新的维护服务
    pub fn new(
        config: MaintenanceConfig,
        repository: KlineRepositoryV2,
        exchange_client: Box<dyn ExchangeClient>,
    ) -> Self {
        Self {
            config,
            repository,
            exchange_client,
        }
    }

    /// 启动维护服务
    pub async fn start(&self) -> Result<()> {
        info!("🚀 启动K线数据维护服务 v2.0");
        info!("📊 配置信息:");
        info!("   - 交易所: {}", self.config.exchange_name);
        info!("   - 交易对数量: {}", self.config.symbols.len());
        info!("   - 更新间隔: {}分钟", self.config.update_interval_minutes);
        info!("   - 完整性检查间隔: {}小时", self.config.integrity_check_interval_hours);
        info!("   - 自动修复: {}", self.config.enable_auto_fix);
        info!("   - 缓存启用: {}", self.config.enable_cache);

        // 1. 初始化数据库结构
        self.repository.initialize_schema().await?;

        // 2. 执行启动时的完整性检查
        self.perform_startup_integrity_check().await?;

        // 3. 启动定时任务
        self.start_scheduled_tasks().await?;

        Ok(())
    }

    /// 执行启动时的完整性检查
    async fn perform_startup_integrity_check(&self) -> Result<()> {
        info!("🔍 执行启动时完整性检查");

        let end_time = Utc::now();
        let start_time = end_time - Duration::hours(24); // 检查最近24小时

        let mut total_issues = 0;
        let mut total_fixed = 0;

        for symbol in &self.config.symbols {
            match self.repository.check_data_integrity(
                symbol,
                &self.config.exchange_name,
                start_time,
                end_time,
            ).await {
                Ok(result) => {
                    info!("📈 {} 完整性: {:.2}% (缺失 {} 分钟)", 
                        symbol, result.completeness_percentage, result.missing_count);

                    if result.missing_count > 0 {
                        total_issues += result.missing_count;

                        if self.config.enable_auto_fix {
                            match self.fix_missing_data(symbol, &result.missing_ranges).await {
                                Ok(fixed) => {
                                    total_fixed += fixed;
                                    info!("✅ {} 修复了 {} 条数据", symbol, fixed);
                                }
                                Err(e) => {
                                    warn!("⚠️ {} 修复失败: {}", symbol, e);
                                }
                            }
                        }
                    }

                    // 检查数据延迟
                    if let Some(lag_minutes) = result.data_lag_minutes {
                        if lag_minutes > 10 {
                            warn!("⏰ {} 数据延迟 {} 分钟", symbol, lag_minutes);
                        }
                    }
                }
                Err(e) => {
                    error!("❌ {} 完整性检查失败: {}", symbol, e);
                }
            }
        }

        info!("📊 启动检查完成: 发现 {} 个问题，修复 {} 条数据", total_issues, total_fixed);
        Ok(())
    }

    /// 启动定时任务
    async fn start_scheduled_tasks(&self) -> Result<()> {
        info!("⏰ 启动定时任务");

        // 增量更新任务
        let update_interval = TokioDuration::from_secs(self.config.update_interval_minutes * 60);
        let mut update_timer = interval(update_interval);

        // 完整性检查任务
        let integrity_interval = TokioDuration::from_secs(self.config.integrity_check_interval_hours * 3600);
        let mut integrity_timer = interval(integrity_interval);

        // 数据清理任务 (每天执行一次)
        let cleanup_interval = TokioDuration::from_secs(24 * 3600);
        let mut cleanup_timer = interval(cleanup_interval);

        loop {
            tokio::select! {
                _ = update_timer.tick() => {
                    if let Err(e) = self.perform_incremental_update().await {
                        error!("增量更新失败: {}", e);
                    }
                }
                _ = integrity_timer.tick() => {
                    if let Err(e) = self.perform_scheduled_integrity_check().await {
                        error!("定时完整性检查失败: {}", e);
                    }
                }
                _ = cleanup_timer.tick() => {
                    if let Err(e) = self.perform_data_cleanup().await {
                        error!("数据清理失败: {}", e);
                    }
                }
            }
        }
    }

    /// 执行增量更新
    async fn perform_incremental_update(&self) -> Result<()> {
        debug!("🔄 执行增量更新");

        let end_time = Utc::now();
        let start_time = end_time - Duration::hours(1); // 获取最近1小时的数据

        let mut total_updated = 0;

        for symbol in &self.config.symbols {
            match self.fetch_and_store_klines(symbol, start_time, end_time).await {
                Ok(count) => {
                    if count > 0 {
                        total_updated += count;
                        debug!("✅ {} 更新了 {} 条数据", symbol, count);
                    }
                }
                Err(e) => {
                    warn!("⚠️ {} 增量更新失败: {}", symbol, e);
                }
            }

            // API限制保护
            tokio::time::sleep(TokioDuration::from_millis(100)).await;
        }

        if total_updated > 0 {
            info!("🔄 增量更新完成，共更新 {} 条数据", total_updated);
        }

        Ok(())
    }

    /// 执行定时完整性检查
    async fn perform_scheduled_integrity_check(&self) -> Result<()> {
        info!("🔍 执行定时完整性检查");

        // 检查所有交易对的完整性
        match self.repository.check_all_symbols_integrity(
            &self.config.exchange_name,
            24, // 检查最近24小时
        ).await {
            Ok(results) => {
                let mut issues_found = 0;
                let mut total_fixed = 0;

                for result in results {
                    if result.completeness_percentage < 95.0 {
                        issues_found += 1;
                        warn!("⚠️ {} 完整性较低: {:.2}%", 
                            result.symbol, result.completeness_percentage);

                        if self.config.enable_auto_fix && result.missing_count > 0 {
                            // 获取详细的缺失范围并修复
                            let end_time = Utc::now();
                            let start_time = end_time - Duration::hours(24);
                            
                            if let Ok(detailed_result) = self.repository.check_data_integrity(
                                &result.symbol,
                                &self.config.exchange_name,
                                start_time,
                                end_time,
                            ).await {
                                match self.fix_missing_data(&result.symbol, &detailed_result.missing_ranges).await {
                                    Ok(fixed) => {
                                        total_fixed += fixed;
                                        info!("✅ {} 修复了 {} 条数据", result.symbol, fixed);
                                    }
                                    Err(e) => {
                                        warn!("⚠️ {} 修复失败: {}", result.symbol, e);
                                    }
                                }
                            }
                        }
                    }

                    // 检查数据延迟
                    if let Some(lag_minutes) = result.data_lag_minutes {
                        if lag_minutes > 15 {
                            warn!("⏰ {} 数据延迟严重: {} 分钟", result.symbol, lag_minutes);
                        }
                    }
                }

                info!("📊 完整性检查完成: 发现 {} 个问题，修复 {} 条数据", issues_found, total_fixed);
            }
            Err(e) => {
                error!("❌ 批量完整性检查失败: {}", e);
            }
        }

        Ok(())
    }

    /// 执行数据清理
    async fn perform_data_cleanup(&self) -> Result<()> {
        info!("🧹 执行数据清理");

        // 清理过期数据
        match self.repository.cleanup_expired_data(self.config.retention_days).await {
            Ok(deleted_count) => {
                if deleted_count > 0 {
                    info!("🗑️ 清理了 {} 条过期数据", deleted_count);
                }
            }
            Err(e) => {
                warn!("⚠️ 数据清理失败: {}", e);
            }
        }

        Ok(())
    }

    /// 从交易所获取并存储K线数据
    async fn fetch_and_store_klines(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<u64> {
        // 1. 从交易所获取数据
        let query = KlineQuery {
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            start_time: Some(start_time.timestamp_millis()),
            end_time: Some(end_time.timestamp_millis()),
            limit: Some(1000),
        };

        let klines = self.exchange_client.get_klines(query).await?;

        if klines.is_empty() {
            return Ok(0);
        }

        // 2. 转换为插入请求
        let requests = self.convert_to_insert_requests(symbol, &klines)?;

        // 3. 批量插入（双写：TimescaleDB + Redis）
        let count = self.repository.batch_insert_klines(&requests).await?;

        Ok(count)
    }

    /// 修复缺失数据
    async fn fix_missing_data(
        &self,
        symbol: &str,
        missing_ranges: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Result<u64> {
        if missing_ranges.is_empty() {
            return Ok(0);
        }

        let mut total_fixed = 0;

        for &(start_time, end_time) in missing_ranges {
            // 限制单次修复的时间范围，避免API限制
            let max_duration = Duration::hours(12);
            let actual_end_time = if end_time - start_time > max_duration {
                start_time + max_duration
            } else {
                end_time
            };

            match self.fetch_and_store_klines(symbol, start_time, actual_end_time).await {
                Ok(count) => {
                    total_fixed += count;
                    debug!("修复 {} 时间段 {} - {}: {} 条数据", 
                        symbol, start_time, actual_end_time, count);
                }
                Err(e) => {
                    warn!("修复 {} 时间段失败: {}", symbol, e);
                }
            }

            // API限制保护
            tokio::time::sleep(TokioDuration::from_millis(200)).await;
        }

        Ok(total_fixed)
    }

    /// 转换UniversalKline为InsertKlineRequest
    fn convert_to_insert_requests(
        &self,
        symbol: &str,
        klines: &[UniversalKline],
    ) -> Result<Vec<InsertKlineRequest>> {
        use rust_decimal::Decimal;
        use std::str::FromStr;

        let mut requests = Vec::new();

        for kline in klines {
            let time = DateTime::from_timestamp_millis(kline.open_time)
                .ok_or_else(|| anyhow::anyhow!("Invalid timestamp: {}", kline.open_time))?;

            let request = InsertKlineRequest {
                time,
                symbol: symbol.to_string(),
                exchange: self.config.exchange_name.clone(),
                open_price: Decimal::from_str(&kline.open_price)?,
                high_price: Decimal::from_str(&kline.high_price)?,
                low_price: Decimal::from_str(&kline.low_price)?,
                close_price: Decimal::from_str(&kline.close_price)?,
                volume: Decimal::from_str(&kline.volume)?,
                quote_volume: Decimal::from_str(&kline.quote_asset_volume)?,
                trades_count: kline.number_of_trades,
                taker_buy_volume: Decimal::from_str(&kline.taker_buy_base_asset_volume)?,
                taker_buy_quote_volume: Decimal::from_str(&kline.taker_buy_quote_asset_volume)?,
            };

            requests.push(request);
        }

        Ok(requests)
    }

    /// 获取服务状态
    pub async fn get_service_status(&self) -> Result<ServiceStatus> {
        // 获取数据延迟状态
        let lag_statuses = self.repository.get_data_lag_status().await?;
        
        let mut healthy_count = 0;
        let mut warning_count = 0;
        let mut critical_count = 0;

        for status in &lag_statuses {
            match status.status {
                LagStatus::Healthy => healthy_count += 1,
                LagStatus::Warning => warning_count += 1,
                LagStatus::Critical => critical_count += 1,
            }
        }

        Ok(ServiceStatus {
            total_symbols: self.config.symbols.len(),
            healthy_symbols: healthy_count,
            warning_symbols: warning_count,
            critical_symbols: critical_count,
            last_update: Utc::now(),
            config: self.config.clone(),
        })
    }
}

/// 服务状态
#[derive(Debug, Clone)]
pub struct ServiceStatus {
    pub total_symbols: usize,
    pub healthy_symbols: usize,
    pub warning_symbols: usize,
    pub critical_symbols: usize,
    pub last_update: DateTime<Utc>,
    pub config: MaintenanceConfig,
}
