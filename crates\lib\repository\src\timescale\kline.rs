// 兼容性文件 - 为了向后兼容而保留
// 实际的实现已被拆分到各个独立的表文件中

// 重新导出新的分离后的类型和函数
pub use super::klines::*;
pub use super::tickers::*;
pub use super::order_books::*;
pub use super::stats_24hr::*;
pub use super::trades::{TradeRepository, TradeEntity, CreateTradeRequest, TradeQuery, TradeStats};

use sqlx::PgPool;
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 综合的市场数据Repository - 组合了各个表的操作
#[derive(Clone)]
pub struct MarketDataRepository {
    pub klines: KlineRepository,
    pub tickers: TickerRepository,
    pub order_books: OrderBookRepository,
    pub stats_24hr: Stats24hrRepository,
    pub trades: TradeRepository,
    pool: PgPool,
}

impl MarketDataRepository {
    pub fn new(pool: PgPool) -> Self {
        Self {
            klines: KlineRepository::new(pool.clone()),
            tickers: TickerRepository::new(pool.clone()),
            order_books: OrderBookRepository::new(pool.clone()),
            stats_24hr: Stats24hrRepository::new(pool.clone()),
            trades: TradeRepository::new(pool.clone()),
            pool,
        }
    }

    /// 初始化所有TimescaleDB表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        // 逐个初始化各个表
        self.klines.initialize_table().await?;
        self.tickers.initialize_table().await?;
        self.order_books.initialize_table().await?;
        self.stats_24hr.initialize_table().await?;
        self.trades.initialize_table().await?;
        
        log::info!("所有TimescaleDB表结构初始化完成");
        Ok(())
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        self.klines.health_check().await
    }
} 