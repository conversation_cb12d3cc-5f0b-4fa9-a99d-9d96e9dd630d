use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;
use sqlx::Row;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 时间桶统计结果
#[derive(Debug, Clone)]
pub struct TimeBucketCount {
    pub time_bucket: DateTime<Utc>,
    pub actual_count: i64,
}

// 导入实体定义
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct DataStatsEntity {
    pub total_records: i64,
    pub earliest_time: Option<DateTime<Utc>>,
    pub latest_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct TimeRangeEntity {
    pub min_time: Option<DateTime<Utc>>,
    pub max_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct TimeBucketEntity {
    pub minute_bucket: DateTime<Utc>,
    pub data_count: Option<i64>,
}

// K线实体 - 兼容旧的KlineEntity
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct KlineEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub interval: String,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub close_price: Decimal,
    pub volume: Decimal,
    pub quote_asset_volume: Decimal,
    pub number_of_trades: i32,
    pub taker_buy_base_asset_volume: Decimal,
    pub taker_buy_quote_asset_volume: Decimal,
    pub close_time: DateTime<Utc>,
}

/// OHLCV市场数据
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct MarketData {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub volume: Decimal,
    pub interval_type: String, // '1m', '5m', '1h', '1d' etc.
}

/// 市场数据插入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateMarketDataRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub volume: Decimal,
    pub interval_type: String,
    pub quote_asset_volume: Decimal,
    pub number_of_trades: i32,
    pub taker_buy_base_asset_volume: Decimal,
    pub taker_buy_quote_asset_volume: Decimal,
    pub close_time: DateTime<Utc>,
}

/// 市场数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketDataQuery {
    pub symbol: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub interval_type: String,
    pub limit: Option<u32>,
}

/// K线数据Repository
#[derive(Clone)]
pub struct KlineRepository {
    pool: PgPool,
}

impl KlineRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化TimescaleDB表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        // 注意：TimescaleDB扩展已在连接管理器中初始化，这里直接创建表和超级表

        // 创建K线数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS klines (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                interval TEXT NOT NULL,
                open_price DECIMAL NOT NULL,
                high_price DECIMAL NOT NULL,
                low_price DECIMAL NOT NULL,
                close_price DECIMAL NOT NULL,
                volume DECIMAL NOT NULL,
                quote_asset_volume DECIMAL NOT NULL,
                number_of_trades INTEGER NOT NULL,
                taker_buy_base_asset_volume DECIMAL NOT NULL,
                taker_buy_quote_asset_volume DECIMAL NOT NULL,
                close_time TIMESTAMPTZ NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将K线表转换为超级表
        log::info!("正在创建klines超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("klines")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建klines超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("klines超级表创建成功");

        // 添加唯一约束（使用DO块处理IF NOT EXISTS）
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'klines_unique' AND conrelid = 'klines'::regclass
                ) THEN
                    ALTER TABLE klines ADD CONSTRAINT klines_unique UNIQUE (time, symbol, interval);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_klines_symbol_interval_time
             ON klines (symbol, interval, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_klines_symbol_time
             ON klines (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建价格数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tickers (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                price DECIMAL NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将价格表转换为超级表
        log::info!("正在创建tickers超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("tickers")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建tickers超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("tickers超级表创建成功");

        // 创建深度数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS order_books (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                last_update_id BIGINT NOT NULL,
                bids JSONB NOT NULL,
                asks JSONB NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将深度表转换为超级表
        log::info!("正在创建order_books超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("order_books")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建order_books超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("order_books超级表创建成功");

        // 添加唯一约束
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'order_books_unique' AND conrelid = 'order_books'::regclass
                ) THEN
                    ALTER TABLE order_books ADD CONSTRAINT order_books_unique UNIQUE (time, symbol);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建24小时统计表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS stats_24hr (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                price_change DECIMAL NOT NULL,
                price_change_percent DECIMAL NOT NULL,
                weighted_avg_price DECIMAL NOT NULL,
                prev_close_price DECIMAL NOT NULL,
                last_price DECIMAL NOT NULL,
                last_qty DECIMAL NOT NULL,
                bid_price DECIMAL NOT NULL,
                ask_price DECIMAL NOT NULL,
                open_price DECIMAL NOT NULL,
                high_price DECIMAL NOT NULL,
                low_price DECIMAL NOT NULL,
                volume DECIMAL NOT NULL,
                quote_volume DECIMAL NOT NULL,
                open_time TIMESTAMPTZ NOT NULL,
                close_time TIMESTAMPTZ NOT NULL,
                first_id BIGINT NOT NULL,
                last_id BIGINT NOT NULL,
                count BIGINT NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将统计表转换为超级表
        log::info!("正在创建stats_24hr超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("stats_24hr")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建stats_24hr超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("stats_24hr超级表创建成功");

        // 创建交易数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS trades (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                trade_id BIGINT NOT NULL,
                price DECIMAL NOT NULL,
                qty DECIMAL NOT NULL,
                quote_qty DECIMAL NOT NULL,
                is_buyer_maker BOOLEAN NOT NULL,
                is_best_match BOOLEAN NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将交易表转换为超级表
        log::info!("正在创建trades超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("trades")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建trades超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("trades超级表创建成功");

        // 添加唯一约束
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'trades_unique' AND conrelid = 'trades'::regclass
                ) THEN
                    ALTER TABLE trades ADD CONSTRAINT trades_unique UNIQUE (time, symbol, trade_id);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("TimescaleDB表结构初始化完成");
        Ok(())
    }

    /// 插入单条市场数据（支持重复数据处理）
    pub async fn insert(&self, request: CreateMarketDataRequest) -> Result<MarketData> {
        let data = sqlx::query_as::<_, MarketData>(
            r#"
            INSERT INTO klines (symbol, time, open_price, high_price, low_price, close_price, volume, interval, 
                               quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, 
                               taker_buy_quote_asset_volume, close_time)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            ON CONFLICT (time, symbol, interval) DO UPDATE SET
                open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                close_price = EXCLUDED.close_price,
                volume = EXCLUDED.volume,
                quote_asset_volume = EXCLUDED.quote_asset_volume,
                number_of_trades = EXCLUDED.number_of_trades,
                taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume,
                close_time = EXCLUDED.close_time
            RETURNING symbol, time as timestamp, open_price as open, high_price as high, 
                     low_price as low, close_price as close, volume, interval as interval_type
            "#,
        )
        .bind(&request.symbol)
        .bind(&request.timestamp)
        .bind(&request.open)
        .bind(&request.high)
        .bind(&request.low)
        .bind(&request.close)
        .bind(&request.volume)
        .bind(&request.interval_type)
        .bind(&request.quote_asset_volume)
        .bind(&request.number_of_trades)
        .bind(&request.taker_buy_base_asset_volume)
        .bind(&request.taker_buy_quote_asset_volume)
        .bind(&request.close_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(data)
    }

    /// 批量插入市场数据（支持重复数据处理）
    pub async fn batch_insert(&self, requests: Vec<CreateMarketDataRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO klines (symbol, time, open_price, high_price, low_price, close_price, volume, interval, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, close_time) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.symbol)
                .push_bind(&request.timestamp)
                .push_bind(&request.open)
                .push_bind(&request.high)
                .push_bind(&request.low)
                .push_bind(&request.close)
                .push_bind(&request.volume)
                .push_bind(&request.interval_type)
                .push_bind(&request.quote_asset_volume)
                .push_bind(&request.number_of_trades)
                .push_bind(&request.taker_buy_base_asset_volume)
                .push_bind(&request.taker_buy_quote_asset_volume)
                .push_bind(&request.close_time);
        });

        // 添加 ON CONFLICT 子句来处理重复数据
        query_builder.push(
            " ON CONFLICT (time, symbol, interval) DO UPDATE SET \
             open_price = EXCLUDED.open_price, \
             high_price = EXCLUDED.high_price, \
             low_price = EXCLUDED.low_price, \
             close_price = EXCLUDED.close_price, \
             volume = EXCLUDED.volume, \
             quote_asset_volume = EXCLUDED.quote_asset_volume, \
             number_of_trades = EXCLUDED.number_of_trades, \
             taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume, \
             taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume, \
             close_time = EXCLUDED.close_time"
        );

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询市场数据
    pub async fn query(&self, params: MarketDataQuery) -> Result<Vec<MarketData>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT symbol, time as timestamp, open_price as open, high_price as high, low_price as low, close_price as close, volume, interval as interval_type FROM klines WHERE symbol = "
        );
        query.push_bind(&params.symbol);
        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        query.push(" AND interval = ").push_bind(&params.interval_type);
        query.push(" ORDER BY time ASC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let data: Vec<MarketData> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(data)
    }

    /// 获取最新的市场数据
    pub async fn get_latest(&self, symbol: &str, interval_type: &str) -> Result<Option<MarketData>> {
        let data = sqlx::query_as::<_, MarketData>(
            "SELECT symbol, time as timestamp, open_price as open, high_price as high, low_price as low, close_price as close, volume, interval as interval_type FROM klines WHERE symbol = $1 AND interval = $2 ORDER BY time DESC LIMIT 1"
        )
        .bind(symbol)
        .bind(interval_type)
        .fetch_optional(&self.pool)
        .await?;

        Ok(data)
    }

    /// 获取指定时间段的数据统计
    pub async fn get_stats(&self, symbol: &str, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> Result<MarketStats> {
        let stats = sqlx::query_as::<_, MarketStats>(
            r#"
            SELECT 
                COUNT(*) as count,
                MIN(low_price) as min_price,
                MAX(high_price) as max_price,
                SUM(volume) as total_volume,
                AVG(close_price) as avg_price
            FROM klines 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 删除旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM klines WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 分析指定时间范围内的数据统计
    pub async fn analyze_data_stats(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<DataStatsEntity> {
        let result = sqlx::query_as::<_, DataStatsEntity>(
            "SELECT 
                COUNT(*) as total_records,
                MIN(time) as earliest_time,
                MAX(time) as latest_time
             FROM klines 
             WHERE symbol = $1 AND interval = $2 
                AND time >= $3 AND time <= $4"
        )
        .bind(symbol)
        .bind(interval)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    /// 获取数据时间范围
    pub async fn get_time_range(&self, symbol: &str, interval: &str) -> Result<TimeRangeEntity> {
        let result = sqlx::query_as::<_, TimeRangeEntity>(
            "SELECT 
                MIN(time) as min_time,
                MAX(time) as max_time
             FROM klines 
             WHERE symbol = $1 AND interval = $2"
        )
        .bind(symbol)
        .bind(interval)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    /// 查找缺失的时间桶
    pub async fn find_missing_time_buckets(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketEntity>> {
        let result = sqlx::query_as::<_, TimeBucketEntity>(
            r#"
            SELECT 
                time_bucket_gapfill('1 minute', time, $3, $4) as minute_bucket,
                COUNT(time) as data_count
            FROM klines 
            WHERE symbol = $1 AND interval = $2 
                AND time >= $3 AND time <= $4
            GROUP BY minute_bucket
            HAVING COUNT(time) = 0
            ORDER BY minute_bucket
            "#,
        )
        .bind(symbol)
        .bind(interval)
        .bind(start_time)
        .bind(end_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(result)
    }

    /// 查询K线数据 - 兼容旧接口
    pub async fn find_klines(
        &self,
        symbol: &str,
        interval: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<i64>,
    ) -> Result<Vec<KlineEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT time, symbol, interval, 
             open_price, high_price, low_price, 
             close_price, volume, quote_asset_volume, 
             number_of_trades, taker_buy_base_asset_volume, 
             taker_buy_quote_asset_volume, close_time 
             FROM klines WHERE symbol = "
        );
        
        query.push_bind(symbol);
        query.push(" AND interval = ");
        query.push_bind(interval);

        if let Some(start) = start_time {
            query.push(" AND time >= ");
            query.push_bind(start);
        }

        if let Some(end) = end_time {
            query.push(" AND time <= ");
            query.push_bind(end);
        }

        query.push(" ORDER BY time ASC");

        if let Some(limit) = limit {
            query.push(" LIMIT ");
            query.push_bind(limit);
        }

        let klines = query
            .build_query_as::<KlineEntity>()
            .fetch_all(&self.pool)
            .await?;

        Ok(klines)
    }

    /// 获取活跃的维护品种
    pub async fn get_active_maintained_symbols(&self) -> Result<Vec<String>> {
        // 这个方法应该在PostgresRepository中，这里暂时返回空
        Ok(vec!["BTCUSDT".to_string(), "ETHUSDT".to_string()])
    }

    /// 执行时间桶聚合查询（用于数据完整性检查）
    pub async fn query_time_bucket_aggregation(
        &self,
        symbol: &str,
        interval: &str,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketCount>> {
        let query = format!(
            r#"
            SELECT 
                time_bucket('{}', time) as time_bucket,
                COUNT(*) as actual_count
            FROM klines 
            WHERE symbol = $1 
                AND interval = $2
                AND time >= $3 
                AND time < $4
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
            bucket_interval
        );

        let rows = sqlx::query(&query)
            .bind(symbol)
            .bind(interval)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        let mut bucket_counts = Vec::new();
        for row in rows {
            let time_bucket: DateTime<Utc> = row.get("time_bucket");
            let actual_count: i64 = row.get("actual_count");
            
            bucket_counts.push(TimeBucketCount {
                time_bucket,
                actual_count,
            });
        }

        Ok(bucket_counts)
    }
}

/// 市场数据统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct MarketStats {
    pub count: i64,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub total_volume: Option<Decimal>,
    pub avg_price: Option<Decimal>,
} 