#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use tokio;
    use chrono::{DateTime, Utc};
    use crate::market_data::ServiceStateManager;

    /// 测试缓存同步服务的基本功能
    #[tokio::test]
    async fn test_cache_sync_service_creation() {
        // 为了简化测试，我们只测试状态管理器的基本功能
        let state_manager = ServiceStateManager::new();

        // 验证状态管理器的基本功能
        let initial_state = state_manager.get_state().await;
        assert!(!initial_state.cache_sync.is_running);
        assert_eq!(initial_state.cache_sync.sync_stats.synced_records, 0);

        // 测试状态更新
        state_manager.set_cache_sync_running(true).await;
        let updated_state = state_manager.get_state().await;
        assert!(updated_state.cache_sync.is_running);

        // 测试同步统计更新
        state_manager.update_sync_stats(100, 5, 1000).await;
        let stats_state = state_manager.get_state().await;
        assert_eq!(stats_state.cache_sync.sync_stats.synced_records, 100);
        assert_eq!(stats_state.cache_sync.sync_stats.synced_symbols, 5);
        assert_eq!(stats_state.cache_sync.sync_stats.sync_duration_ms, 1000);
    }

    /// 测试维护进度跟踪
    #[tokio::test]
    async fn test_maintenance_progress_tracking() {
        let state_manager = ServiceStateManager::new();
        
        // 测试初始状态
        let initial_state = state_manager.get_state().await;
        assert!(!initial_state.maintenance.is_running);
        assert_eq!(initial_state.maintenance.progress.total_symbols, 0);
        
        // 测试设置运行状态
        state_manager.set_maintenance_running(true).await;
        let running_state = state_manager.get_state().await;
        assert!(running_state.maintenance.is_running);
        assert!(running_state.maintenance.started_at.is_some());
        
        // 测试进度更新
        state_manager.update_maintenance_progress(
            100, 
            25, 
            "数据扫描", 
            Some("BTCUSDT".to_string())
        ).await;
        
        let progress_state = state_manager.get_state().await;
        assert_eq!(progress_state.maintenance.progress.total_symbols, 100);
        assert_eq!(progress_state.maintenance.progress.processed_symbols, 25);
        assert_eq!(progress_state.maintenance.progress.current_phase, "数据扫描");
        assert_eq!(progress_state.maintenance.current_symbol, Some("BTCUSDT".to_string()));
        
        // 验证预计完成时间被设置
        assert!(progress_state.maintenance.progress.estimated_completion.is_some());
    }

    /// 测试错误处理
    #[tokio::test]
    async fn test_error_handling() {
        let state_manager = ServiceStateManager::new();
        
        // 测试维护错误记录
        let error_msg = "测试错误信息".to_string();
        state_manager.set_maintenance_error(Some(error_msg.clone())).await;
        
        let error_state = state_manager.get_state().await;
        assert_eq!(error_state.maintenance.last_error, Some(error_msg));
        
        // 测试缓存同步错误记录
        let cache_error_msg = "缓存同步错误".to_string();
        state_manager.set_cache_sync_error(Some(cache_error_msg.clone())).await;
        
        let cache_error_state = state_manager.get_state().await;
        assert_eq!(cache_error_state.cache_sync.last_error, Some(cache_error_msg));
        
        // 测试错误清除
        state_manager.set_maintenance_error(None).await;
        state_manager.set_cache_sync_error(None).await;
        
        let cleared_state = state_manager.get_state().await;
        assert_eq!(cleared_state.maintenance.last_error, None);
        assert_eq!(cleared_state.cache_sync.last_error, None);
    }

    /// 测试状态管理器的克隆功能
    #[tokio::test]
    async fn test_state_manager_cloning() {
        let state_manager1 = ServiceStateManager::new();
        let state_manager2 = state_manager1.clone();
        
        // 在第一个管理器中设置状态
        state_manager1.set_maintenance_running(true).await;
        state_manager1.update_maintenance_progress(50, 10, "测试阶段", None).await;
        
        // 验证第二个管理器能看到相同的状态
        let state1 = state_manager1.get_state().await;
        let state2 = state_manager2.get_state().await;
        
        assert_eq!(state1.maintenance.is_running, state2.maintenance.is_running);
        assert_eq!(state1.maintenance.progress.total_symbols, state2.maintenance.progress.total_symbols);
        assert_eq!(state1.maintenance.progress.processed_symbols, state2.maintenance.progress.processed_symbols);
        assert_eq!(state1.maintenance.progress.current_phase, state2.maintenance.progress.current_phase);
    }

    /// 测试系统统计信息更新
    #[tokio::test]
    async fn test_system_stats_update() {
        let state_manager = ServiceStateManager::new();
        
        // 测试系统统计更新
        state_manager.update_system_stats(|stats| {
            stats.active_symbols = 150;
            stats.total_kline_records = 1000000;
            stats.cache_hit_rate = 0.85;
            stats.exchange_symbol_counts.insert("binance".to_string(), 100);
            stats.exchange_symbol_counts.insert("okx".to_string(), 50);
        }).await;
        
        let stats_state = state_manager.get_state().await;
        assert_eq!(stats_state.stats.active_symbols, 150);
        assert_eq!(stats_state.stats.total_kline_records, 1000000);
        assert_eq!(stats_state.stats.cache_hit_rate, 0.85);
        assert_eq!(stats_state.stats.exchange_symbol_counts.get("binance"), Some(&100));
        assert_eq!(stats_state.stats.exchange_symbol_counts.get("okx"), Some(&50));
    }

    /// 集成测试：模拟完整的维护周期
    #[tokio::test]
    async fn test_maintenance_cycle_simulation() {
        let state_manager = ServiceStateManager::new();
        
        // 1. 启动维护服务
        state_manager.set_maintenance_running(true).await;
        
        // 2. 开始全量扫描
        state_manager.update_maintenance_progress(100, 0, "全量扫描", None).await;
        
        // 3. 模拟处理进度
        for i in 1..=100 {
            let symbol = format!("SYMBOL{}USDT", i);
            state_manager.update_maintenance_progress(
                100, 
                i, 
                "全量扫描", 
                Some(symbol)
            ).await;
            
            // 每10个符号检查一次状态
            if i % 10 == 0 {
                let current_state = state_manager.get_state().await;
                assert_eq!(current_state.maintenance.progress.processed_symbols, i);
                assert!(current_state.maintenance.progress.estimated_completion.is_some());
            }
        }
        
        // 4. 完成全量扫描，开始增量扫描
        state_manager.update_maintenance_progress(100, 100, "全量扫描完成", None).await;
        state_manager.update_maintenance_progress(100, 0, "增量扫描", None).await;
        
        // 5. 启动缓存同步
        state_manager.set_cache_sync_running(true).await;
        state_manager.update_sync_stats(500, 10, 2000).await;
        
        // 6. 验证最终状态
        let final_state = state_manager.get_state().await;
        assert!(final_state.maintenance.is_running);
        assert!(final_state.cache_sync.is_running);
        assert_eq!(final_state.cache_sync.sync_stats.synced_records, 500);
        assert_eq!(final_state.cache_sync.sync_stats.synced_symbols, 10);
        
        // 7. 停止服务
        state_manager.set_maintenance_running(false).await;
        state_manager.set_cache_sync_running(false).await;
        
        let stopped_state = state_manager.get_state().await;
        assert!(!stopped_state.maintenance.is_running);
        assert!(!stopped_state.cache_sync.is_running);
    }
}
