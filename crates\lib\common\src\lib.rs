#![allow(warnings)]

use std::sync::OnceLock;
use crate::config::kafka_config::Kafka;
use crate::config::pgsql_config::DatabaseConfig;
use crate::config::redis_config::RedisConfig;
use crate::config::topic_config::Topic;

pub mod opt;
pub mod utils;
pub mod validate;
pub mod domain;
pub mod config;
pub mod log;
mod r#const;

pub static KAFKA_CONFIG: OnceLock<Kafka> = OnceLock::new();
pub static TOPIC_CONFIG: OnceLock<Topic> = OnceLock::new();
pub static REDIS_CONFIG: OnceLock<RedisConfig> = OnceLock::new();
pub static USERDATABASE_CONFIG: OnceLock<DatabaseConfig> = OnceLock::new();

pub static TRADERDATABASE_CONFIG: OnceLock<DatabaseConfig> = OnceLock::new();
