use anyhow::Result;
use std::env;

#[derive(Debug, <PERSON><PERSON>)]
pub struct AppConfig {
    pub listen_addr: String,
    pub enable_data_maintenance: bool,
    pub maintenance_interval_seconds: u64,
    pub symbols: Vec<String>,
    pub intervals: Vec<String>,
}

impl AppConfig {
    pub fn from_env() -> Self {
        Self {
            listen_addr: env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0:8091".to_string()),
            enable_data_maintenance: env::var("ENABLE_DATA_MAINTENANCE").unwrap_or_else(|_| "false".to_string()).parse().unwrap_or(false),
            maintenance_interval_seconds: env::var("MAINTENANCE_INTERVAL_SECONDS")
                .unwrap_or_else(|_| "3600".to_string()) // 默认1小时
                .parse()
                .unwrap_or(3600),
            symbols: env::var("SYMBOLS")
                .unwrap_or_else(|_| "BTCUSDT,ETHUSDT".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
            intervals: env::var("INTERVALS")
                .unwrap_or_else(|_| "1m,5m,15m,1h,4h,1d".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
        }
    }
}
