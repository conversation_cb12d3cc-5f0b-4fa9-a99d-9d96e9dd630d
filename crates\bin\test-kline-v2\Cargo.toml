[package]
name = "test-kline-v2"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "test-kline-v2"
path = "src/main.rs"

[dependencies]
# 核心依赖
tokio = { workspace = true, features = ["full"] }
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
clap = { workspace = true, features = ["derive"] }
chrono = { workspace = true, features = ["serde"] }
sqlx = { workspace = true, features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "rust_decimal"] }

# 本地依赖
repository = { path = "../../lib/repository" }
service = { path = "../../lib/service" }
