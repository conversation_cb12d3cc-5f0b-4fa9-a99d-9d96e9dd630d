use axum::{
    extract::Extension,
    http::StatusCode,
    response::{IntoResponse, Response, Json},
    Router,
    routing::{get, post},
    middleware,
};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use crate::router::AppState;
use crate::router::auth_jwt::auth_middleware;
use crate::router::auth_extractor::{PermissionCheck, check_permission, check_role};
use common::domain::dto::{
    Claims, ApiResponse, HealthData, JwtUserInfo, 
    ProtectedResourceData, OperationResult, SystemStats
};
use chrono::Utc;

/// 健康检查接口（无需认证）
#[utoipa::path(
    get,
    path = "/api/v1/health/health",
    responses(
        (status = 200, description = "服务健康", body = ApiResponse<HealthData>)
    ),
    tag = "health"
)]
pub async fn health_check() -> impl IntoResponse {
    let health_data = HealthData::default();
    let response = ApiResponse::success(health_data);
    Json(response)
}

/// 获取当前用户信息（需要JWT认证）
#[utoipa::path(
    get,
    path = "/api/v1/health/user-info",
    responses(
        (status = 200, description = "用户信息获取成功", body = ApiResponse<JwtUserInfo>),
        (status = 401, description = "未授权访问")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn get_user_info(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    let user_info = JwtUserInfo {
        user_id: claims.sub.clone(),
        email: claims.email.clone(),
        nickname: claims.nickname.clone(),
        roles: claims.roles.clone(),
        permissions: claims.permissions.clone(),
        issued_at: claims.iat as i64,
        expires_at: claims.exp as i64,
    };

    let response = ApiResponse::success(user_info);
    Json(response)
}

/// 访问受保护资源（需要JWT认证 + read权限）
/// 
/// 使用权限检查工具函数进行权限验证
#[utoipa::path(
    get,
    path = "/api/v1/health/protected",
    responses(
        (status = 200, description = "受保护资源访问成功", body = ApiResponse<ProtectedResourceData>),
        (status = 401, description = "未授权访问"),
        (status = 403, description = "权限不足")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn access_protected_resource(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    // 使用权限检查工具函数
    match PermissionCheck::require_permission(&claims, "read") {
        Ok(_) => {
            // 权限验证通过，执行业务逻辑
            let protected_data = ProtectedResourceData {
                accessed_by: claims.sub.clone(),
                access_time: Utc::now().timestamp(),
                resource_info: "这是一个需要认证和'read'权限才能访问的秘密信息".to_string(),
                operation: "read_protected_resource".to_string(),
            };

            let response = ApiResponse::success(protected_data);
            Json(response).into_response()
        }
        Err(error_response) => error_response,
    }
}

/// 管理员专用接口（需要JWT认证 + admin角色）
/// 
/// 使用角色检查工具函数进行角色验证
#[utoipa::path(
    get,
    path = "/api/v1/health/admin-only",
    responses(
        (status = 200, description = "管理员操作成功", body = ApiResponse<SystemStats>),
        (status = 401, description = "未授权访问"),
        (status = 403, description = "需要管理员权限")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn admin_only_endpoint(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    // 使用角色检查工具函数
    match PermissionCheck::require_role(&claims, "admin") {
        Ok(_) => {
            // 角色验证通过，执行管理员业务逻辑
            let system_stats = SystemStats::default();
            let response = ApiResponse::success(system_stats);
            Json(response).into_response()
        }
        Err(error_response) => error_response,
    }
}

/// 需要写权限的接口示例
/// 
/// 展示如何使用权限检查进行写操作权限验证
#[utoipa::path(
    post,
    path = "/api/v1/health/write-operation",
    responses(
        (status = 200, description = "写操作成功", body = ApiResponse<OperationResult>),
        (status = 401, description = "未授权访问"),
        (status = 403, description = "权限不足")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn write_operation_endpoint(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    // 使用权限检查工具函数
    match PermissionCheck::require_permission(&claims, "write") {
        Ok(_) => {
            // 权限验证通过，执行写操作业务逻辑
            let operation_result = OperationResult {
                operation: "write_data".to_string(),
                user_id: claims.sub.clone(),
                username: claims.nickname.clone(),
                permissions: claims.permissions.clone(),
                timestamp: Utc::now().timestamp(),
                status: "success".to_string(),
            };

            let response = ApiResponse::success(operation_result);
            Json(response).into_response()
        }
        Err(error_response) => error_response,
    }
}

/// 多权限检查示例接口
/// 
/// 展示如何检查用户是否同时拥有多个权限
#[utoipa::path(
    post,
    path = "/api/v1/health/multi-permission",
    responses(
        (status = 200, description = "多权限操作成功", body = ApiResponse<OperationResult>),
        (status = 401, description = "未授权访问"),
        (status = 403, description = "权限不足")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn multi_permission_endpoint(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    // 检查是否同时拥有read和write权限
    match PermissionCheck::require_multiple_permissions(&claims, &["read", "write"]) {
        Ok(_) => {
            let operation_result = OperationResult {
                operation: "read_and_write".to_string(),
                user_id: claims.sub.clone(),
                username: claims.nickname.clone(),
                permissions: claims.permissions.clone(),
                timestamp: Utc::now().timestamp(),
                status: "success".to_string(),
            };

            let response = ApiResponse::success(operation_result);
            Json(response).into_response()
        }
        Err(error_response) => error_response,
    }
}

/// 任意权限检查示例接口
/// 
/// 展示如何检查用户是否拥有任意一个指定权限
#[utoipa::path(
    get,
    path = "/api/v1/health/any-permission",
    responses(
        (status = 200, description = "任意权限操作成功", body = ApiResponse<OperationResult>),
        (status = 401, description = "未授权访问"),
        (status = 403, description = "权限不足")
    ),
    tag = "health",
    security(
        ("Bearer" = [])
    )
)]
pub async fn any_permission_endpoint(Extension(claims): Extension<Claims>) -> impl IntoResponse {
    // 检查是否拥有read或admin权限之一
    match PermissionCheck::require_any_permission(&claims, &["read", "admin"]) {
        Ok(_) => {
            let operation_result = OperationResult {
                operation: "flexible_access".to_string(),
                user_id: claims.sub.clone(),
                username: claims.nickname.clone(),
                permissions: claims.permissions.clone(),
                timestamp: Utc::now().timestamp(),
                status: "success".to_string(),
            };

            let response = ApiResponse::success(operation_result);
            Json(response).into_response()
        }
        Err(error_response) => error_response,
    }
}

/// 权限认证最佳实践路由配置
pub fn health_routes() -> Router<AppState> {
    Router::new()
        // 无需认证的公开接口
        .route("/health", get(health_check))
        
        // 需要JWT认证的受保护路由组
        .route("/user-info", get(get_user_info))
        .route("/protected", get(access_protected_resource))
        .route("/admin-only", get(admin_only_endpoint))
        .route("/write-operation", post(write_operation_endpoint))
        .route("/multi-permission", post(multi_permission_endpoint))
        .route("/any-permission", get(any_permission_endpoint))
        // 为受保护的路由组应用JWT认证中间件
        .layer(middleware::from_fn(auth_middleware))
}

/// 便利函数：检查用户是否拥有指定权限
pub fn has_permission(claims: &Claims, permission: &str) -> bool {
    check_permission(claims, permission)
}

/// 便利函数：检查用户是否拥有指定角色
pub fn has_role(claims: &Claims, role: &str) -> bool {
    check_role(claims, role)
}
