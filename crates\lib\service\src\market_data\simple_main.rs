use anyhow::Result;
use tracing::{info, error};
use std::sync::Arc;

use repository::{
    connection::{timescale::TimescalePoolManager, DatabaseConfig},
    timescale::KlineRepository,
    RedisCacheRepository,
};
use exchange::binance::BinanceClient;
use crate::market_data::simple_maintenance::{SimpleMaintenanceService, SimpleConfig};

/// 简化的市场数据维护服务启动器
pub struct SimpleServiceLauncher;

impl SimpleServiceLauncher {
    /// 启动简化的维护服务
    pub async fn start() -> Result<()> {
        // 初始化日志
        Self::init_logging();

        info!("🚀 启动简化市场数据维护服务");

        // 加载配置
        let config = SimpleConfig::from_env();
        info!("📋 配置加载完成");

        // 初始化数据库连接
        let db_pool = Self::init_database(&config.database_url).await?;
        info!("💾 数据库连接初始化完成");

        // 初始化缓存连接
        let cache_repo = Self::init_cache(&config.redis_url).await?;
        info!("📦 缓存连接初始化完成");

        // 初始化交易所客户端
        let exchange_client = Self::init_exchange(&config.exchange_name)?;
        info!("🏪 交易所客户端初始化完成");

        // 创建维护服务
        let kline_repo = KlineRepository::new(db_pool);
        let maintenance_service = SimpleMaintenanceService::new(
            kline_repo,
            cache_repo,
            exchange_client,
        );

        info!("✅ 服务初始化完成，开始运行");

        // 启动服务（这会一直运行）
        maintenance_service.start().await?;

        Ok(())
    }

    /// 初始化日志系统
    fn init_logging() {
        let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
        
        tracing_subscriber::fmt()
            .with_env_filter(log_level)
            .with_target(false)
            .with_thread_ids(false)
            .with_file(false)
            .with_line_number(false)
            .init();
    }

    /// 初始化数据库连接
    async fn init_database(database_url: &str) -> Result<sqlx::PgPool> {
        let db_config = Self::parse_database_url(database_url)?;
        let pool_manager = TimescalePoolManager::new(db_config).await?;
        Ok(pool_manager.pool().clone())
    }

    /// 解析数据库URL
    fn parse_database_url(url: &str) -> Result<DatabaseConfig> {
        // 简化的URL解析
        // 格式: postgresql://user:password@host:port/database
        let url = url.strip_prefix("postgresql://").unwrap_or(url);
        let parts: Vec<&str> = url.split('@').collect();
        
        if parts.len() != 2 {
            return Ok(default_database_config());
        }

        let auth_parts: Vec<&str> = parts[0].split(':').collect();
        let host_parts: Vec<&str> = parts[1].split('/').collect();
        let host_port: Vec<&str> = host_parts[0].split(':').collect();

        Ok(DatabaseConfig {
            host: host_port.get(0).unwrap_or(&"localhost").to_string(),
            port: host_port.get(1).unwrap_or(&"5432").parse().unwrap_or(5432),
            username: auth_parts.get(0).unwrap_or(&"postgres").to_string(),
            password: auth_parts.get(1).unwrap_or(&"password").to_string(),
            database: host_parts.get(1).unwrap_or(&"market_data").to_string(),
            max_connections: 10,
            min_connections: 2,
            connect_timeout: 30,
            idle_timeout: 600,
        })
    }

    /// 初始化缓存连接
    async fn init_cache(redis_url: &str) -> Result<Box<dyn repository::CacheRepository>> {
        let cache_repo = RedisCacheRepository::new(redis_url).await?;
        Ok(Box::new(cache_repo))
    }

    /// 初始化交易所客户端
    fn init_exchange(exchange_name: &str) -> Result<Box<dyn exchange::ExchangeClient>> {
        match exchange_name.to_lowercase().as_str() {
            "binance" => {
                let client = BinanceClient::new();
                Ok(Box::new(client))
            }
            _ => {
                error!("不支持的交易所: {}", exchange_name);
                Err(anyhow::anyhow!("不支持的交易所: {}", exchange_name))
            }
        }
    }

    /// 运行健康检查
    pub async fn health_check() -> Result<()> {
        let config = SimpleConfig::from_env();

        // 检查数据库连接
        match Self::init_database(&config.database_url).await {
            Ok(_) => info!("✅ 数据库连接正常"),
            Err(e) => {
                error!("❌ 数据库连接失败: {}", e);
                return Err(e);
            }
        }

        // 检查缓存连接
        match Self::init_cache(&config.redis_url).await {
            Ok(cache) => {
                match cache.ping().await {
                    Ok(_) => info!("✅ 缓存连接正常"),
                    Err(e) => {
                        error!("❌ 缓存连接失败: {}", e);
                        return Err(e);
                    }
                }
            }
            Err(e) => {
                error!("❌ 缓存初始化失败: {}", e);
                return Err(e);
            }
        }

        info!("✅ 健康检查通过");
        Ok(())
    }
}

/// 创建默认数据库配置
fn default_database_config() -> DatabaseConfig {
    DatabaseConfig {
        host: "localhost".to_string(),
        port: 5432,
        username: "postgres".to_string(),
        password: "password".to_string(),
        database: "market_data".to_string(),
        max_connections: 10,
        min_connections: 2,
        connect_timeout: 30,
        idle_timeout: 600,
    }
}
