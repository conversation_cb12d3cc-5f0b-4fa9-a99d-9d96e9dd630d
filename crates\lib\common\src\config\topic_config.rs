use serde::Deserialize;
use crate::config::tdengine_config::Tdengine;

#[derive(Debug, Deserialize, Clone)]
pub struct TopicConfig {
    pub topic: Topic,
}
#[derive(Debug, Deserialize,Clone)]
pub struct Topic {
    /// 订单消息主题
    pub order_command_topic: String,
    /// 撮合消息主题
    pub order_match_topic: String,
    /// 开闭市消息主题
    pub market_status_topic: String,
    /// 深度行情主题
    pub depth_topic: String,
    /// Kline消息主题
    pub kline_topic: String,
    /// 消费批次大小
    pub batch_size: i32,

}
