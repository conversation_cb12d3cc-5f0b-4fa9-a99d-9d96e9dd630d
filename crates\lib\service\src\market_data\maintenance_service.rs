use anyhow::Result;
use chrono::{DateTime, Utc, TimeZone, Timelike};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::time::{sleep, Duration, Instant};
use tracing::{info, warn, debug, error};

// 使用repository模块
use repository::{
    timescale::{KlineRepository, Symbol, TimeBucketCount},
    connection::timescale::TimescalePoolManager,
};

// 导入交易所客户端
use exchange::{ExchangeClient, ExchangeClientFactory, KlineQuery, UniversalKline};

// 导入配置和存储管理器
use super::config::MaintenanceConfig;
use super::enhanced_config::{EnhancedMaintenanceConfig, ConfigManager};
use super::storage::TimescaleStorageManager;
use super::shared_state::ServiceStateManager;
use super::cache_sync_service::CacheSyncService;
use super::gap_detector::{GapDetector, GapDetectionConfig};

/// 数据缺口信息
#[derive(Debug, Clone)]
pub struct DataGap {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

/// 时间桶缺口检测结果
#[derive(Debug, Clone)]
pub struct TimeBucketGap {
    pub interval: String,
    pub time_bucket: DateTime<Utc>,
    pub expected_count: i64,
    pub actual_count: i64,
}

/// 精确缺口检测报告
#[derive(Debug, Clone)]
pub struct GapDetectionReport {
    pub symbol: String,
    pub interval: String,
    pub total_buckets_checked: i64,
    pub gaps_found: Vec<TimeBucketGap>,
    pub missing_data_ranges: Vec<DataGap>,
}

/// 维护状态信息
#[derive(Debug, Clone)]
pub struct MaintenanceStatus {
    pub is_running: bool,
    pub last_full_scan: Option<DateTime<Utc>>,
    pub last_incremental_scan: Option<DateTime<Utc>>,
}

/// 数据维护服务
/// 
/// 专门负责数据完整性检查、历史数据维护和增量同步
pub struct MaintenanceService {
    // Repository层
    kline_repo: KlineRepository,

    // 维护相关
    config: MaintenanceConfig,
    enhanced_config: Arc<tokio::sync::RwLock<EnhancedMaintenanceConfig>>,
    config_manager: Option<ConfigManager>,
    storage: Arc<TimescaleStorageManager>,
    exchange_clients: HashMap<String, Box<dyn ExchangeClient>>,
    shutdown_signal: Arc<AtomicBool>,

    // 状态管理
    state_manager: ServiceStateManager,

    // 缓存同步服务
    cache_sync_service: Option<Arc<CacheSyncService>>,

    // 优化的缺口检测器
    gap_detector: GapDetector,
    
    // 时间记录
    last_full_scan: Option<DateTime<Utc>>,
    last_incremental_scan: Option<DateTime<Utc>>,
}

impl MaintenanceService {
    /// 创建维护服务实例
    pub async fn new(
        config: MaintenanceConfig,
        storage: Arc<TimescaleStorageManager>,
        timescale_pool: TimescalePoolManager,
        state_manager: ServiceStateManager,
        cache_sync_service: Option<Arc<CacheSyncService>>,
    ) -> Result<Self> {
        // 验证配置
        config.validate().map_err(|e| anyhow::anyhow!("配置验证失败: {}", e))?;

        let pool = timescale_pool.pool().clone();

        // 初始化交易所客户端
        let mut exchange_clients = HashMap::new();
        
        // 创建Binance客户端
        let binance_client = ExchangeClientFactory::create_binance_client();
        exchange_clients.insert("binance".to_string(), binance_client);

        // 创建优化的缺口检测器
        let gap_detector = GapDetector::new(KlineRepository::new(pool.clone()));

        // 创建默认的增强配置
        let enhanced_config = Arc::new(tokio::sync::RwLock::new(EnhancedMaintenanceConfig::default()));

        Ok(Self {
            kline_repo: KlineRepository::new(pool),
            config,
            enhanced_config,
            config_manager: None,
            storage,
            exchange_clients,
            shutdown_signal: Arc::new(AtomicBool::new(false)),
            state_manager,
            cache_sync_service,
            gap_detector,
            last_full_scan: None,
            last_incremental_scan: None,
        })
    }

    /// 使用增强配置创建维护服务实例
    pub async fn new_with_enhanced_config(
        config_file: Option<&str>,
        storage: Arc<TimescaleStorageManager>,
        timescale_pool: TimescalePoolManager,
        state_manager: ServiceStateManager,
        cache_sync_service: Option<Arc<CacheSyncService>>,
    ) -> Result<Self> {
        // 加载增强配置
        let config_manager = ConfigManager::new(config_file)?;
        let enhanced_config = config_manager.get_config().clone();

        info!("🔧 使用增强配置创建维护服务: {}", enhanced_config.get_config_summary());

        // 验证配置
        enhanced_config.validate().map_err(|e| anyhow::anyhow!("增强配置验证失败: {}", e))?;

        let pool = timescale_pool.pool().clone();

        // 初始化交易所客户端（基于配置）
        let mut exchange_clients = HashMap::new();

        for (exchange_name, exchange_config) in &enhanced_config.exchanges {
            if exchange_config.enabled {
                match exchange_name.as_str() {
                    "binance" => {
                        let binance_client = ExchangeClientFactory::create_binance_client();
                        exchange_clients.insert(exchange_name.clone(), binance_client);
                        info!("✅ 启用交易所客户端: {}", exchange_name);
                    }
                    _ => {
                        warn!("⚠️  不支持的交易所: {}", exchange_name);
                    }
                }
            } else {
                info!("⏸️  跳过禁用的交易所: {}", exchange_name);
            }
        }

        // 创建优化的缺口检测器
        let gap_detector = GapDetector::new(KlineRepository::new(pool.clone()));

        // 转换为旧的MaintenanceConfig以保持兼容性
        let legacy_config = MaintenanceConfig {
            full_scan_interval_hours: enhanced_config.maintenance.full_scan_interval_hours,
            incremental_scan_interval_minutes: enhanced_config.maintenance.incremental_scan_interval_minutes,
            binance_base_url: enhanced_config.exchanges.get("binance")
                .map(|c| c.base_url.clone())
                .unwrap_or_else(|| "https://api.binance.com".to_string()),
            request_timeout_seconds: enhanced_config.exchanges.get("binance")
                .map(|c| c.timeout_seconds)
                .unwrap_or(30),
            batch_insert_size: enhanced_config.maintenance.batch_size,
            cleanup_threshold_days: enhanced_config.maintenance.data_retention_days,
        };

        Ok(Self {
            kline_repo: KlineRepository::new(pool),
            config: legacy_config,
            enhanced_config: Arc::new(tokio::sync::RwLock::new(enhanced_config)),
            config_manager: Some(config_manager),
            storage,
            exchange_clients,
            shutdown_signal: Arc::new(AtomicBool::new(false)),
            state_manager,
            cache_sync_service,
            gap_detector,
            last_full_scan: None,
            last_incremental_scan: None,
        })
    }

    /// 启动维护服务（在单独线程中运行）
    pub async fn start_maintenance(&mut self) -> Result<()> {
        info!("🚀 启动数据维护服务...");

        // 设置运行状态
        self.state_manager.set_maintenance_running(true).await;

        // 初始化数据库
        info!("正在初始化数据库表结构...");
        if let Err(e) = self.storage.initialize_database().await {
            error!("数据库初始化失败: {}", e);
            self.state_manager.set_maintenance_error(Some(e.to_string())).await;
            return Err(e);
        }
        info!("✅ 数据库表结构初始化成功");

        // 检查是否为首次启动
        let is_first_startup = self.last_full_scan.is_none();
        if is_first_startup {
            info!("🚀 检测到首次启动，将立即执行全量历史数据扫描...");
            self.state_manager.update_maintenance_progress(0, 0, "首次全量扫描", None).await;
            
            if let Err(e) = self.run_full_scan().await {
                error!("首次全量扫描失败: {}", e);
                self.state_manager.set_maintenance_error(Some(e.to_string())).await;
                return Err(e);
            }
            info!("✅ 首次全量扫描完成，开始正常维护循环");
        }

        // 启动缓存同步服务（如果提供）
        if let Some(cache_sync) = &self.cache_sync_service {
            let symbols = self.get_maintained_symbols().await?;
            let cache_sync_clone = cache_sync.clone();
            
            tokio::spawn(async move {
                if let Err(e) = cache_sync_clone.start(symbols).await {
                    error!("缓存同步服务启动失败: {}", e);
                }
            });
        }

        // 主循环
        let mut loop_count = 0;
        while !self.shutdown_signal.load(Ordering::Relaxed) {
            let loop_start = Instant::now();
            loop_count += 1;

            debug!("维护循环 #{} 开始", loop_count);
            self.state_manager.update_maintenance_progress(0, 0, &format!("维护循环 #{}", loop_count), None).await;

            if self.should_run_full_scan() {
                info!("触发定期全量扫描...");
                if let Err(e) = self.run_full_scan().await {
                    error!("定期全量扫描失败: {}", e);
                    self.state_manager.set_maintenance_error(Some(e.to_string())).await;
                }
            }

            if self.should_run_incremental_scan() {
                debug!("触发增量扫描...");
                if let Err(e) = self.run_incremental_scan().await {
                    error!("增量扫描失败: {}", e);
                    self.state_manager.set_maintenance_error(Some(e.to_string())).await;
                }
            }

            let elapsed = loop_start.elapsed();
            let sleep_duration = if elapsed < Duration::from_secs(60) {
                Duration::from_secs(60) - elapsed
            } else {
                Duration::from_secs(1)
            };

            debug!("维护循环 #{} 完成，耗时: {:?}，休眠: {:?}", loop_count, elapsed, sleep_duration);
            sleep(sleep_duration).await;
        }

        // 停止缓存同步服务
        if let Some(cache_sync) = &self.cache_sync_service {
            cache_sync.stop();
        }

        self.state_manager.set_maintenance_running(false).await;
        info!("数据维护服务已停止");
        Ok(())
    }

    /// 停止维护服务
    pub fn stop_maintenance(&self) {
        info!("正在停止维护服务...");
        self.shutdown_signal.store(true, Ordering::Relaxed);
    }

    /// 判断是否应该运行全量扫描
    fn should_run_full_scan(&self) -> bool {
        match self.last_full_scan {
            None => true,
            Some(last) => {
                let now = Utc::now();
                let elapsed = now - last;
                elapsed.num_seconds() >= self.config.full_scan_interval().as_secs() as i64
            }
        }
    }

    /// 判断是否应该运行增量扫描
    fn should_run_incremental_scan(&self) -> bool {
        match self.last_incremental_scan {
            None => true,
            Some(last) => {
                let now = Utc::now();
                let elapsed = now - last;
                elapsed.num_seconds() >= self.config.incremental_scan_interval().as_secs() as i64
            }
        }
    }

    /// 执行全量扫描
    async fn run_full_scan(&mut self) -> Result<()> {
        info!("🔍 开始执行全量扫描...");
        let start_time = Instant::now();

        let symbols = self.get_maintained_symbols().await?;
        if symbols.is_empty() {
            warn!("symbols表中没有需要维护的币对，跳过全量扫描");
            return Ok(());
        }

        info!("📊 从symbols表获取到 {} 个需要维护的币对", symbols.len());
        self.state_manager.update_maintenance_progress(symbols.len() as u32, 0, "全量扫描", None).await;

        // 显示将要处理的交易对
        for (i, symbol) in symbols.iter().enumerate().take(10) {
            info!("  {}. {} ({})", i + 1, symbol.symbol, symbol.exchange);
        }
        if symbols.len() > 10 {
            info!("  ... 还有 {} 个交易对", symbols.len() - 10);
        }

        info!("🔧 开始数据一致性检查...");
        self.check_data_consistency(&symbols).await?;

        info!("📈 开始维护历史数据（从2024-06-01至今）...");
        self.maintain_historical_data(&symbols).await?;

        if let Err(e) = self.storage.perform_maintenance().await {
            warn!("数据库维护操作失败: {}", e);
        }

        self.last_full_scan = Some(Utc::now());
        self.state_manager.update_maintenance_progress(symbols.len() as u32, symbols.len() as u32, "全量扫描完成", None).await;
        
        info!("✅ 全量扫描完成，总耗时: {:?}", start_time.elapsed());
        Ok(())
    }

    /// 执行增量扫描
    async fn run_incremental_scan(&mut self) -> Result<()> {
        debug!("开始执行增量扫描...");
        let start_time = Instant::now();

        let symbols = self.get_maintained_symbols().await?;
        if symbols.is_empty() {
            warn!("symbols表中没有活跃的币对，跳过增量扫描");
            return Ok(());
        }

        debug!("找到 {} 个活跃交易符号", symbols.len());
        self.state_manager.update_maintenance_progress(symbols.len() as u32, 0, "增量扫描", None).await;

        let mut updated_symbols = Vec::new();

        for (i, symbol) in symbols.iter().enumerate() {
            self.state_manager.update_maintenance_progress(
                symbols.len() as u32,
                i as u32,
                "增量扫描",
                Some(symbol.symbol.clone())
            ).await;

            match self.fetch_latest_klines(symbol).await {
                Ok(has_new_data) => {
                    if has_new_data {
                        updated_symbols.push(symbol.clone());
                        debug!("✅ {} 获取到新数据", symbol.symbol);
                    }
                }
                Err(e) => {
                    error!("获取{}的最新K线数据失败: {}", symbol.symbol, e);
                }
            }
        }

        // 如果有新数据，触发缓存同步
        if !updated_symbols.is_empty() {
            info!("🔄 增量扫描发现 {} 个符号有新数据，触发缓存同步", updated_symbols.len());
            self.trigger_cache_sync_for_symbols(&updated_symbols).await?;
        }

        self.last_incremental_scan = Some(Utc::now());
        self.state_manager.update_maintenance_progress(symbols.len() as u32, symbols.len() as u32, "增量扫描完成", None).await;

        debug!("增量扫描完成，耗时: {:?}", start_time.elapsed());
        Ok(())
    }

    /// 从symbols表获取需要维护的币对
    async fn get_maintained_symbols(&self) -> Result<Vec<Symbol>> {
        debug!("从symbols表获取需要维护的币对列表...");
        
        let symbols = self.storage.symbol_manager().get_active_symbol_objects().await?;
        
        let mut exchange_counts = HashMap::new();
        for symbol in &symbols {
            *exchange_counts.entry(symbol.exchange.clone()).or_insert(0) += 1;
        }
        
        for (exchange, count) in exchange_counts {
            debug!("交易所 {} 有 {} 个活跃币对", exchange, count);
        }
        
        Ok(symbols)
    }

    /// 检查数据一致性
    async fn check_data_consistency(&self, symbols: &[Symbol]) -> Result<()> {
        info!("检查数据一致性...");

        let mut missing_data_count = 0;
        for symbol in symbols {
            if let Err(e) = self.check_symbol_data_integrity(&symbol.symbol).await {
                warn!("符号{}数据完整性检查失败: {}", symbol.symbol, e);
                missing_data_count += 1;
            }
        }

        if missing_data_count > 0 {
            warn!("发现{}个符号存在数据完整性问题", missing_data_count);
        }

        Ok(())
    }

    /// 检查单个符号的数据完整性（使用优化的缺口检测）
    async fn check_symbol_data_integrity(&self, symbol: &str) -> Result<()> {
        info!("🔍 检查符号 {} 的数据完整性", symbol);

        // 配置缺口检测参数
        let config = GapDetectionConfig {
            intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
            batch_window_days: 7,
            max_concurrent_symbols: 1, // 单个符号检测
            enable_smart_sampling: true,
            sampling_rate: 0.2, // 20%采样，平衡速度和准确性
        };

        // 检查最近30天的数据
        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::days(30);

        for interval in &config.intervals {
            match self.gap_detector.detect_gaps_optimized(
                symbol,
                interval,
                start_time,
                end_time,
                &config
            ).await {
                Ok(result) => {
                    if !result.gaps.is_empty() {
                        warn!("⚠️  符号 {} {} 发现 {} 个数据缺口",
                              symbol, interval, result.gaps.len());

                        // 记录严重缺口
                        let severe_gaps = result.gaps.iter()
                            .filter(|g| g.gap_severity == super::gap_detector::GapSeverity::Severe)
                            .count();

                        if severe_gaps > 0 {
                            error!("❌ 符号 {} {} 发现 {} 个严重数据缺口",
                                   symbol, interval, severe_gaps);
                        }
                    } else {
                        debug!("✅ 符号 {} {} 数据完整性良好", symbol, interval);
                    }
                }
                Err(e) => {
                    error!("❌ 检查符号 {} {} 数据完整性失败: {}", symbol, interval, e);
                }
            }
        }

        Ok(())
    }

    /// 获取最新K线数据
    async fn fetch_latest_klines(&self, symbol: &Symbol) -> Result<bool> {
        let client = self.exchange_clients.get(&symbol.exchange)
            .ok_or_else(|| anyhow::anyhow!("不支持的交易所: {}", symbol.exchange))?;

        let query = KlineQuery {
            symbol: symbol.symbol.clone(),
            interval: "1m".to_string(),
            start_time: None,
            end_time: None,
            limit: Some(100),
        };

        let klines = client.get_klines(query).await?;

        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests(&symbol.symbol, &klines);
            match self.storage.kline_manager().batch_insert(requests).await {
                Ok(inserted_count) => {
                    if inserted_count > 0 {
                        debug!("为符号 {} 插入了 {} 条K线数据", symbol.symbol, inserted_count);
                        return Ok(true); // 有新数据插入
                    } else {
                        debug!("符号 {} 没有新数据需要插入", symbol.symbol);
                        return Ok(false); // 没有新数据
                    }
                }
                Err(e) => {
                    warn!("插入K线数据失败: {}", e);
                    return Err(e.into());
                }
            }
        }

        Ok(false) // 没有获取到数据
    }

    /// 为指定符号触发缓存同步
    async fn trigger_cache_sync_for_symbols(&self, symbols: &[Symbol]) -> Result<()> {
        if let Some(cache_sync) = &self.cache_sync_service {
            let mut total_synced = 0u64;

            // 计算同步时间范围（最近10分钟的数据，确保覆盖新插入的数据）
            let end_time = Utc::now();
            let start_time = end_time - chrono::Duration::minutes(10);

            for symbol in symbols {
                match cache_sync.manual_sync_symbol(&symbol.symbol, Some(start_time), Some(end_time)).await {
                    Ok(synced_count) => {
                        total_synced += synced_count;
                        debug!("✅ 缓存同步 {} 完成，同步了 {} 条记录", symbol.symbol, synced_count);
                    }
                    Err(e) => {
                        warn!("⚠️  缓存同步 {} 失败: {}", symbol.symbol, e);
                    }
                }
            }

            if total_synced > 0 {
                info!("🔄 增量缓存同步完成，总共同步了 {} 条1分钟级别记录", total_synced);
            }
        } else {
            warn!("缓存同步服务未配置，跳过缓存同步");
        }

        Ok(())
    }

    /// 维护历史数据
    async fn maintain_historical_data(&self, symbols: &[Symbol]) -> Result<()> {
        info!("🔧 开始维护历史数据（2024-06-01 至今）...");
        let start_time = Instant::now();

        let maintenance_start = Utc.with_ymd_and_hms(2024, 6, 1, 0, 0, 0)
            .single()
            .ok_or_else(|| anyhow::anyhow!("无法创建维护起始时间"))?;
        
        let maintenance_end = Utc::now();
        
        info!("📅 历史数据维护时间范围: {} 到 {}", 
              maintenance_start.format("%Y-%m-%d %H:%M:%S UTC"),
              maintenance_end.format("%Y-%m-%d %H:%M:%S UTC"));

        if symbols.is_empty() {
            warn!("⚠️  没有找到需要维护的交易符号，跳过历史数据维护");
            return Ok(());
        }

        let intervals = vec![
            "1m".to_string(),
            "5m".to_string(), 
            "15m".to_string(),
            "1h".to_string(),
            "4h".to_string(),
            "1d".to_string(),
        ];

        info!("🚀 开始为 {} 个交易符号维护 {} 个时间级别的历史数据", symbols.len(), intervals.len());

        let mut processed_symbols = 0;
        let mut total_records_fixed = 0;

        for symbol in symbols {
            info!("🔧 维护交易对: {} (多时间级别精确检测)", symbol.symbol);
            
            // 这里可以添加具体的数据完整性检查和修复逻辑
            processed_symbols += 1;
            
            // 每处理10个符号休息一下，避免过载
            if processed_symbols % 10 == 0 {
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }

        info!("🎉 历史数据维护完成，处理了 {} 个符号，总共修复了 {} 条记录，耗时: {:?}", 
              processed_symbols, total_records_fixed, start_time.elapsed());
        Ok(())
    }

    /// 将通用K线数据转换为数据库请求
    fn convert_klines_to_requests(&self, symbol: &str, klines: &[UniversalKline]) -> Vec<repository::timescale::CreateMarketDataRequest> {
        klines.iter().map(|kline| {
            repository::timescale::CreateMarketDataRequest {
                symbol: symbol.to_string(),
                timestamp: DateTime::from_timestamp_millis(kline.open_time)
                    .unwrap_or_else(Utc::now),
                open: kline.open_price.parse().unwrap_or_default(),
                high: kline.high_price.parse().unwrap_or_default(),
                low: kline.low_price.parse().unwrap_or_default(),
                close: kline.close_price.parse().unwrap_or_default(),
                volume: kline.volume.parse().unwrap_or_default(),
                interval_type: "1m".to_string(),
                quote_asset_volume: kline.quote_asset_volume.parse().unwrap_or_default(),
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse().unwrap_or_default(),
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse().unwrap_or_default(),
                close_time: DateTime::from_timestamp_millis(kline.close_time)
                    .unwrap_or_else(Utc::now),
            }
        }).collect()
    }

    /// 获取服务状态
    pub fn get_maintenance_status(&self) -> MaintenanceStatus {
        MaintenanceStatus {
            is_running: !self.shutdown_signal.load(Ordering::Relaxed),
            last_full_scan: self.last_full_scan,
            last_incremental_scan: self.last_incremental_scan,
        }
    }

    /// 手动触发数据同步
    pub async fn sync_symbol_data(&self, symbol: &str, start_time: Option<DateTime<Utc>>, end_time: Option<DateTime<Utc>>) -> Result<u64> {
        let exchange = "binance"; // 默认使用binance
        let client = self.exchange_clients.get(exchange)
            .ok_or_else(|| anyhow::anyhow!("不支持的交易所: {}", exchange))?;

        let query = KlineQuery {
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            start_time: start_time.map(|t| t.timestamp_millis()),
            end_time: end_time.map(|t| t.timestamp_millis()),
            limit: Some(1000),
        };

        let klines = client.get_klines(query).await?;
        
        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests(symbol, &klines);
            let inserted = self.storage.kline_manager().batch_insert(requests).await?;
            info!("手动同步符号 {} 数据，插入了 {} 条记录", symbol, inserted);
            Ok(inserted)
        } else {
            Ok(0)
        }
    }

    /// 批量检测多个符号的数据缺口（优化版本）
    pub async fn batch_detect_gaps_optimized(
        &self,
        symbols: &[String],
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<HashMap<String, Vec<super::gap_detector::GapDetectionResult>>> {
        info!("🔍 开始批量优化缺口检测: {}个符号", symbols.len());

        let config = GapDetectionConfig {
            intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
            batch_window_days: 7,
            max_concurrent_symbols: 5, // 控制并发数
            enable_smart_sampling: true,
            sampling_rate: 0.15, // 15%采样，适合批量处理
        };

        let results = self.gap_detector.batch_detect_gaps(
            symbols,
            &config,
            start_time,
            end_time,
        ).await?;

        // 统计结果
        let mut total_gaps = 0;
        let mut symbols_with_gaps = 0;

        for (symbol, symbol_results) in &results {
            let symbol_gap_count: usize = symbol_results.iter().map(|r| r.gaps.len()).sum();
            if symbol_gap_count > 0 {
                symbols_with_gaps += 1;
                total_gaps += symbol_gap_count;
                info!("📊 符号 {} 发现 {} 个缺口", symbol, symbol_gap_count);
            }
        }

        info!("✅ 批量缺口检测完成: {}个符号中有{}个存在缺口，总计{}个缺口",
              symbols.len(), symbols_with_gaps, total_gaps);

        Ok(results)
    }

    /// 获取缺口检测统计信息
    pub async fn get_gap_detection_stats(
        &self,
        symbols: &[String],
    ) -> Result<HashMap<String, GapDetectionStats>> {
        let mut stats = HashMap::new();

        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::days(7); // 检查最近7天

        let results = self.batch_detect_gaps_optimized(symbols, start_time, end_time).await?;

        for (symbol, symbol_results) in results {
            let mut symbol_stats = GapDetectionStats::default();

            for result in symbol_results {
                symbol_stats.total_buckets_checked += result.total_buckets_expected;
                symbol_stats.total_gaps_found += result.gaps.len() as u64;
                symbol_stats.total_missing_ranges += result.missing_ranges.len() as u64;

                // 统计不同严重程度的缺口
                for gap in &result.gaps {
                    match gap.gap_severity {
                        super::gap_detector::GapSeverity::Minor => symbol_stats.minor_gaps += 1,
                        super::gap_detector::GapSeverity::Moderate => symbol_stats.moderate_gaps += 1,
                        super::gap_detector::GapSeverity::Severe => symbol_stats.severe_gaps += 1,
                    }
                }
            }

            stats.insert(symbol, symbol_stats);
        }

        Ok(stats)
    }

    /// 检查并重载配置
    pub async fn check_and_reload_config(&mut self) -> Result<bool> {
        if let Some(ref mut config_manager) = self.config_manager {
            match config_manager.check_and_reload() {
                Ok(true) => {
                    info!("🔄 配置已重载，更新服务配置");

                    // 更新增强配置
                    let new_config = config_manager.get_config().clone();
                    *self.enhanced_config.write().await = new_config.clone();

                    // 更新旧配置以保持兼容性
                    self.config.full_scan_interval_hours = new_config.maintenance.full_scan_interval_hours;
                    self.config.incremental_scan_interval_minutes = new_config.maintenance.incremental_scan_interval_minutes;
                    self.config.batch_insert_size = new_config.maintenance.batch_size;

                    info!("✅ 配置重载完成: {}", new_config.get_config_summary());
                    return Ok(true);
                }
                Ok(false) => {
                    // 配置无变化
                    return Ok(false);
                }
                Err(e) => {
                    error!("❌ 配置重载失败: {}", e);
                    return Err(e);
                }
            }
        }
        Ok(false)
    }

    /// 获取当前增强配置
    pub async fn get_enhanced_config(&self) -> EnhancedMaintenanceConfig {
        self.enhanced_config.read().await.clone()
    }

    /// 导出当前配置
    pub async fn export_config(&self, file_path: &str) -> Result<()> {
        let config = self.enhanced_config.read().await;
        config.export_to_file(file_path)?;
        info!("配置已导出到: {}", file_path);
        Ok(())
    }

    /// 获取配置摘要
    pub async fn get_config_summary(&self) -> String {
        let config = self.enhanced_config.read().await;
        config.get_config_summary()
    }
}

/// 缺口检测统计信息
#[derive(Debug, Clone, Default)]
pub struct GapDetectionStats {
    pub total_buckets_checked: i64,
    pub total_gaps_found: u64,
    pub total_missing_ranges: u64,
    pub minor_gaps: u64,
    pub moderate_gaps: u64,
    pub severe_gaps: u64,
}
