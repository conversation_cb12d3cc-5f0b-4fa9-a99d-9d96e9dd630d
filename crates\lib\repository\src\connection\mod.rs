pub mod postgres;
pub mod timescale;
pub mod redis;

use crate::Result;
pub use common::config::pgsql_config::DatabaseConfig;
use serde::{Deserialize, Serialize};

/// 连接池健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStatus {
    pub active_connections: u32,
    pub idle_connections: u32,
    pub total_connections: u32,
    pub is_healthy: bool,
}

impl PoolStatus {
    /// 检查连接池是否健康
    pub fn is_healthy(&self) -> bool {
        self.is_healthy && self.total_connections > 0
    }
} 