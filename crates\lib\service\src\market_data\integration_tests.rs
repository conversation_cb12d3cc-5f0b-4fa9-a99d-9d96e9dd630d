#[cfg(test)]
mod integration_tests {
    use super::*;
    use crate::market_data::{
        enhanced_config::{EnhancedMaintenanceConfig, Environment},
        maintenance_service::MaintenanceService,
        shared_state::ServiceStateManager,
        gap_detector::{GapDetector, GapDetectionConfig},
    };
    use repository::{
        connection::{timescale::TimescalePoolManager, DatabaseConfig},
        timescale::KlineRepository,
        CacheRepository, RedisCacheRepository,
    };
    use std::sync::Arc;
    use tokio;
    use tempfile::tempdir;

    /// 集成测试环境设置
    struct TestEnvironment {
        pub config: EnhancedMaintenanceConfig,
        pub state_manager: ServiceStateManager,
        pub gap_detector: GapDetector,
        pub temp_dir: tempfile::TempDir,
    }

    impl TestEnvironment {
        async fn new() -> Self {
            let temp_dir = tempdir().unwrap();
            
            // 创建测试配置
            let mut config = EnhancedMaintenanceConfig::default();
            config.environment = Environment::Testing;
            config.maintenance.full_scan_interval_hours = 1;
            config.maintenance.incremental_scan_interval_minutes = 1;
            config.maintenance.batch_size = 10;
            config.maintenance.max_concurrent_symbols = 2;
            config.maintenance.enable_auto_repair = false;
            
            // 创建状态管理器
            let state_manager = ServiceStateManager::new();
            
            // 创建模拟的KlineRepository用于测试
            let db_config = DatabaseConfig {
                host: "localhost".to_string(),
                port: 5432,
                username: "test".to_string(),
                password: "test".to_string(),
                database: "test_db".to_string(),
                max_connections: 5,
                min_connections: 1,
                connect_timeout: 30,
                idle_timeout: 600,
            };
            
            // 注意：在实际测试中，这里需要真实的数据库连接
            // 为了演示，我们创建一个模拟的KlineRepository
            let kline_repo = create_mock_kline_repository();
            let gap_detector = GapDetector::new(kline_repo);
            
            Self {
                config,
                state_manager,
                gap_detector,
                temp_dir,
            }
        }
    }

    /// 创建模拟的KlineRepository
    fn create_mock_kline_repository() -> KlineRepository {
        // 在实际测试中，这里应该连接到测试数据库
        // 为了演示，我们返回一个占位符
        // 注意：这需要实际的数据库连接才能工作
        todo!("需要实际的测试数据库连接")
    }

    #[tokio::test]
    #[ignore] // 需要数据库连接，在CI中忽略
    async fn test_full_integration_workflow() {
        let env = TestEnvironment::new().await;
        
        // 测试配置加载和验证
        assert!(env.config.validate().is_ok());
        assert_eq!(env.config.environment, Environment::Testing);
        
        // 测试状态管理
        let initial_state = env.state_manager.get_state().await;
        assert!(!initial_state.maintenance.is_running);
        assert!(!initial_state.cache_sync.is_running);
        
        // 模拟启动维护任务
        env.state_manager.set_maintenance_running(true).await;
        let running_state = env.state_manager.get_state().await;
        assert!(running_state.maintenance.is_running);
        
        // 测试缺口检测配置
        let gap_config = GapDetectionConfig {
            intervals: vec!["1m".to_string(), "5m".to_string()],
            batch_window_days: 1,
            max_concurrent_symbols: 2,
            enable_smart_sampling: false,
            sampling_rate: 1.0, // 100%采样用于测试
        };
        
        // 验证配置
        assert_eq!(gap_config.intervals.len(), 2);
        assert_eq!(gap_config.max_concurrent_symbols, 2);
    }

    #[tokio::test]
    async fn test_config_environment_adaptation() {
        let mut config = EnhancedMaintenanceConfig::default();
        
        // 测试开发环境适配
        config.environment = Environment::Development;
        config.adjust_for_environment();
        
        assert_eq!(config.maintenance.full_scan_interval_hours, 1);
        assert_eq!(config.gap_detection.sampling_rate, 0.5);
        assert_eq!(config.monitoring.logging.level, "debug");
        assert!(!config.maintenance.enable_auto_repair); // 开发环境不自动修复
        
        // 测试生产环境适配
        config = EnhancedMaintenanceConfig::default();
        config.environment = Environment::Production;
        config.adjust_for_environment();
        
        assert_eq!(config.gap_detection.sampling_rate, 0.05);
        assert!(config.performance.enable_compression);
        assert_eq!(config.monitoring.logging.level, "info");
    }

    #[tokio::test]
    async fn test_state_manager_concurrent_access() {
        let state_manager = ServiceStateManager::new();
        
        // 测试并发状态更新
        let handles: Vec<_> = (0..10).map(|i| {
            let sm = state_manager.clone();
            tokio::spawn(async move {
                sm.update_maintenance_progress(100, i * 10, "test", None).await;
                sm.update_sync_stats((i * 100) as u64, i, (i * 1000) as u64).await;
            })
        }).collect();
        
        // 等待所有任务完成
        for handle in handles {
            handle.await.unwrap();
        }
        
        // 验证最终状态
        let final_state = state_manager.get_state().await;
        assert!(final_state.maintenance.progress.processed_symbols >= 0);
        assert!(final_state.cache_sync.sync_stats.synced_records >= 0);
    }

    #[tokio::test]
    async fn test_config_serialization_roundtrip() {
        let original_config = EnhancedMaintenanceConfig::default();
        
        // 测试TOML序列化往返
        let toml_str = toml::to_string_pretty(&original_config).unwrap();
        let deserialized_toml: EnhancedMaintenanceConfig = toml::from_str(&toml_str).unwrap();
        
        assert_eq!(original_config.environment, deserialized_toml.environment);
        assert_eq!(original_config.service.name, deserialized_toml.service.name);
        assert_eq!(original_config.maintenance.full_scan_interval_hours, 
                   deserialized_toml.maintenance.full_scan_interval_hours);
        
        // 测试JSON序列化往返
        let json_str = serde_json::to_string_pretty(&original_config).unwrap();
        let deserialized_json: EnhancedMaintenanceConfig = serde_json::from_str(&json_str).unwrap();
        
        assert_eq!(original_config.environment, deserialized_json.environment);
        assert_eq!(original_config.service.name, deserialized_json.service.name);
    }

    #[tokio::test]
    async fn test_gap_detection_config_validation() {
        // 测试有效配置
        let valid_config = GapDetectionConfig {
            intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
            batch_window_days: 7,
            max_concurrent_symbols: 10,
            enable_smart_sampling: true,
            sampling_rate: 0.1,
        };
        
        assert!(!valid_config.intervals.is_empty());
        assert!(valid_config.batch_window_days > 0);
        assert!(valid_config.max_concurrent_symbols > 0);
        assert!(valid_config.sampling_rate > 0.0 && valid_config.sampling_rate <= 1.0);
        
        // 测试边界值
        let boundary_config = GapDetectionConfig {
            intervals: vec!["1m".to_string()],
            batch_window_days: 1,
            max_concurrent_symbols: 1,
            enable_smart_sampling: false,
            sampling_rate: 1.0,
        };
        
        assert_eq!(boundary_config.intervals.len(), 1);
        assert_eq!(boundary_config.batch_window_days, 1);
        assert_eq!(boundary_config.max_concurrent_symbols, 1);
        assert_eq!(boundary_config.sampling_rate, 1.0);
    }

    #[tokio::test]
    async fn test_performance_metrics() {
        let state_manager = ServiceStateManager::new();
        
        // 模拟性能指标更新
        let start_time = std::time::Instant::now();
        
        // 模拟一些工作
        for i in 0..100 {
            state_manager.update_maintenance_progress(100, i, "performance_test", None).await;
            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        }
        
        let duration = start_time.elapsed();
        
        // 验证性能指标
        let state = state_manager.get_state().await;
        assert_eq!(state.maintenance.progress.processed_symbols, 99);
        assert_eq!(state.maintenance.progress.total_symbols, 100);
        
        // 验证操作在合理时间内完成
        assert!(duration.as_millis() < 1000); // 应该在1秒内完成
    }

    #[tokio::test]
    async fn test_error_handling_and_recovery() {
        let state_manager = ServiceStateManager::new();
        
        // 测试错误状态设置
        state_manager.set_maintenance_error(Some("Test error".to_string())).await;
        
        let error_state = state_manager.get_state().await;
        assert!(error_state.maintenance.last_error.is_some());
        assert_eq!(error_state.maintenance.last_error.unwrap(), "Test error");
        
        // 测试错误恢复
        state_manager.set_maintenance_error(None).await;
        
        let recovered_state = state_manager.get_state().await;
        assert!(recovered_state.maintenance.last_error.is_none());
    }

    #[tokio::test]
    async fn test_config_hot_reload_simulation() {
        let temp_dir = tempdir().unwrap();
        let config_file = temp_dir.path().join("test_config.toml");
        
        // 创建初始配置
        let initial_config = EnhancedMaintenanceConfig::default();
        let toml_content = toml::to_string_pretty(&initial_config).unwrap();
        std::fs::write(&config_file, &toml_content).unwrap();
        
        // 创建配置管理器
        let mut manager = crate::market_data::enhanced_config::ConfigManager::new(
            Some(config_file.to_str().unwrap())
        ).unwrap();
        
        // 验证初始配置
        assert_eq!(manager.get_config().environment, Environment::Development);
        
        // 模拟配置文件更新
        let mut modified_config = initial_config.clone();
        modified_config.environment = Environment::Testing;
        modified_config.maintenance.full_scan_interval_hours = 12;
        
        let modified_toml = toml::to_string_pretty(&modified_config).unwrap();
        
        // 等待确保文件时间戳不同
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        std::fs::write(&config_file, &modified_toml).unwrap();
        
        // 检查热重载
        let reloaded = manager.check_and_reload().unwrap();
        assert!(reloaded);
        assert_eq!(manager.get_config().environment, Environment::Testing);
        assert_eq!(manager.get_config().maintenance.full_scan_interval_hours, 12);
    }

    #[tokio::test]
    async fn test_memory_usage_and_cleanup() {
        let state_manager = ServiceStateManager::new();
        
        // 模拟大量状态更新
        for i in 0..1000 {
            state_manager.update_maintenance_progress(1000, i, "memory_test", None).await;
            state_manager.update_sync_stats((i * 10) as u64, (i / 10) as u32, (i * 100) as u64).await;
        }
        
        // 验证状态正确性
        let final_state = state_manager.get_state().await;
        assert_eq!(final_state.maintenance.progress.processed_symbols, 999);
        assert_eq!(final_state.maintenance.progress.total_symbols, 1000);
        
        // 在实际测试中，这里可以检查内存使用情况
        // 例如使用系统工具或内存分析器
    }

    #[test]
    fn test_configuration_validation_edge_cases() {
        let mut config = EnhancedMaintenanceConfig::default();
        
        // 测试边界值
        config.maintenance.full_scan_interval_hours = 0;
        assert!(config.validate().is_err());
        
        config.maintenance.full_scan_interval_hours = 1;
        config.maintenance.incremental_scan_interval_minutes = 0;
        assert!(config.validate().is_err());
        
        config.maintenance.incremental_scan_interval_minutes = 1;
        config.gap_detection.sampling_rate = 0.0;
        assert!(config.validate().is_err());
        
        config.gap_detection.sampling_rate = 1.1;
        assert!(config.validate().is_err());
        
        config.gap_detection.sampling_rate = 0.5;
        config.maintenance.max_concurrent_symbols = 0;
        assert!(config.validate().is_err());
        
        // 恢复有效配置
        config.maintenance.max_concurrent_symbols = 1;
        assert!(config.validate().is_ok());
    }
}
