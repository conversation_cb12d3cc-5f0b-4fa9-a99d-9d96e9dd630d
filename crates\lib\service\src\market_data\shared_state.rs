use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 服务状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceState {
    /// 维护服务状态
    pub maintenance: MaintenanceState,
    /// 缓存同步服务状态
    pub cache_sync: CacheSyncState,
    /// 系统统计信息
    pub stats: SystemStats,
}

/// 维护服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceState {
    /// 服务是否运行中
    pub is_running: bool,
    /// 服务启动时间
    pub started_at: Option<DateTime<Utc>>,
    /// 最后全量扫描时间
    pub last_full_scan: Option<DateTime<Utc>>,
    /// 最后增量扫描时间
    pub last_incremental_scan: Option<DateTime<Utc>>,
    /// 当前正在处理的符号
    pub current_symbol: Option<String>,
    /// 处理进度
    pub progress: MaintenanceProgress,
    /// 错误信息
    pub last_error: Option<String>,
}

/// 缓存同步服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheSyncState {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后同步时间
    pub last_sync: Option<DateTime<Utc>>,
    /// 同步统计
    pub sync_stats: SyncStats,
    /// 错误信息
    pub last_error: Option<String>,
}

/// 维护进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceProgress {
    /// 总符号数
    pub total_symbols: u32,
    /// 已处理符号数
    pub processed_symbols: u32,
    /// 当前阶段
    pub current_phase: String,
    /// 预计完成时间
    pub estimated_completion: Option<DateTime<Utc>>,
}

/// 同步统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStats {
    /// 同步的记录数
    pub synced_records: u64,
    /// 同步的符号数
    pub synced_symbols: u32,
    /// 同步耗时（毫秒）
    pub sync_duration_ms: u64,
}

/// 系统统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStats {
    /// 活跃符号数
    pub active_symbols: u32,
    /// 总K线记录数
    pub total_kline_records: u64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 数据时间范围
    pub data_time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    /// 各交易所符号数量
    pub exchange_symbol_counts: HashMap<String, u32>,
}

impl Default for ServiceState {
    fn default() -> Self {
        Self {
            maintenance: MaintenanceState::default(),
            cache_sync: CacheSyncState::default(),
            stats: SystemStats::default(),
        }
    }
}

impl Default for MaintenanceState {
    fn default() -> Self {
        Self {
            is_running: false,
            started_at: None,
            last_full_scan: None,
            last_incremental_scan: None,
            current_symbol: None,
            progress: MaintenanceProgress::default(),
            last_error: None,
        }
    }
}

impl Default for CacheSyncState {
    fn default() -> Self {
        Self {
            is_running: false,
            last_sync: None,
            sync_stats: SyncStats::default(),
            last_error: None,
        }
    }
}

impl Default for MaintenanceProgress {
    fn default() -> Self {
        Self {
            total_symbols: 0,
            processed_symbols: 0,
            current_phase: "idle".to_string(),
            estimated_completion: None,
        }
    }
}

impl Default for SyncStats {
    fn default() -> Self {
        Self {
            synced_records: 0,
            synced_symbols: 0,
            sync_duration_ms: 0,
        }
    }
}

impl Default for SystemStats {
    fn default() -> Self {
        Self {
            active_symbols: 0,
            total_kline_records: 0,
            cache_hit_rate: 0.0,
            data_time_range: None,
            exchange_symbol_counts: HashMap::new(),
        }
    }
}

/// 服务状态管理器
/// 
/// 提供线程安全的状态共享机制，支持多个服务间的状态同步
pub struct ServiceStateManager {
    state: Arc<RwLock<ServiceState>>,
}

impl ServiceStateManager {
    /// 创建新的状态管理器
    pub fn new() -> Self {
        Self {
            state: Arc::new(RwLock::new(ServiceState::default())),
        }
    }

    /// 获取当前状态的只读副本
    pub async fn get_state(&self) -> ServiceState {
        self.state.read().await.clone()
    }

    /// 更新维护服务状态
    pub async fn update_maintenance_state<F>(&self, updater: F)
    where
        F: FnOnce(&mut MaintenanceState),
    {
        let mut state = self.state.write().await;
        updater(&mut state.maintenance);
    }

    /// 更新缓存同步服务状态
    pub async fn update_cache_sync_state<F>(&self, updater: F)
    where
        F: FnOnce(&mut CacheSyncState),
    {
        let mut state = self.state.write().await;
        updater(&mut state.cache_sync);
    }

    /// 更新系统统计信息
    pub async fn update_system_stats<F>(&self, updater: F)
    where
        F: FnOnce(&mut SystemStats),
    {
        let mut state = self.state.write().await;
        updater(&mut state.stats);
    }

    /// 设置维护服务运行状态
    pub async fn set_maintenance_running(&self, running: bool) {
        self.update_maintenance_state(|state| {
            state.is_running = running;
            if running && state.started_at.is_none() {
                state.started_at = Some(Utc::now());
            }
        }).await;
    }

    /// 设置缓存同步服务运行状态
    pub async fn set_cache_sync_running(&self, running: bool) {
        self.update_cache_sync_state(|state| {
            state.is_running = running;
        }).await;
    }

    /// 记录维护进度
    pub async fn update_maintenance_progress(&self, total: u32, processed: u32, phase: &str, current_symbol: Option<String>) {
        self.update_maintenance_state(|state| {
            state.progress.total_symbols = total;
            state.progress.processed_symbols = processed;
            state.progress.current_phase = phase.to_string();
            state.current_symbol = current_symbol;
            
            // 估算完成时间
            if processed > 0 && processed < total {
                let progress_rate = processed as f64 / total as f64;
                let elapsed = state.started_at.map(|start| Utc::now() - start);
                if let Some(elapsed) = elapsed {
                    let estimated_total_duration = elapsed.num_milliseconds() as f64 / progress_rate;
                    let remaining_duration = estimated_total_duration - elapsed.num_milliseconds() as f64;
                    state.progress.estimated_completion = Some(Utc::now() + chrono::Duration::milliseconds(remaining_duration as i64));
                }
            }
        }).await;
    }

    /// 记录同步统计
    pub async fn update_sync_stats(&self, records: u64, symbols: u32, duration_ms: u64) {
        self.update_cache_sync_state(|state| {
            state.last_sync = Some(Utc::now());
            state.sync_stats.synced_records += records;
            state.sync_stats.synced_symbols += symbols;
            state.sync_stats.sync_duration_ms = duration_ms;
        }).await;
    }

    /// 记录错误信息
    pub async fn set_maintenance_error(&self, error: Option<String>) {
        self.update_maintenance_state(|state| {
            state.last_error = error;
        }).await;
    }

    /// 记录缓存同步错误
    pub async fn set_cache_sync_error(&self, error: Option<String>) {
        self.update_cache_sync_state(|state| {
            state.last_error = error;
        }).await;
    }

    /// 克隆状态管理器（共享同一个状态）
    pub fn clone(&self) -> Self {
        Self {
            state: self.state.clone(),
        }
    }
}

impl Clone for ServiceStateManager {
    fn clone(&self) -> Self {
        Self {
            state: self.state.clone(),
        }
    }
}
