[package]
name = "nacos"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
common.workspace = true

serde.workspace = true
thiserror.workspace = true
toml.workspace = true
nacos-sdk.workspace = true
anyhow.workspace = true
log.workspace = true
tokio.workspace = true
tracing.workspace = true
serde_yaml.workspace = true

