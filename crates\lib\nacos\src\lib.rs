#![allow(warnings)]
use anyhow::Result;
use std::sync::{Arc, OnceLock};
use tokio::sync::RwLock;
use common::config::common_config::Http;
use common::config::kafka_config::Kafka;
use common::config::pgsql_config::DatabaseConfig;
use common::config::redis_config::RedisConfig;
use common::config::tdengine_config::Tdengine;
use common::config::topic_config::Topic;
use common::domain::r#enum::error::AppError;
use common::{REDIS_CONFIG, TRADERDATABASE_CONFIG, USERDATABASE_CONFIG};
use crate::nacos_config::{NacosClient, NacosConfig};

pub mod client;
pub mod nacos_config;

pub static NACOS_CONFIG: OnceLock<NacosConfig> = OnceLock::new();


pub type SharedNacosClient = Arc<RwLock<NacosClient>>;

pub async fn init_nacos(app_name: &str) -> Result<SharedNacosClient, AppError> {
    let nacos_config = NacosConfig::new(app_name);
    NACOS_CONFIG.set(nacos_config.clone()).ok();
    log::warn!(
        "Start…………………………………app_name:{:?},nacos:{:?},use_name{:?},password{:?}",
        app_name,
        nacos_config.server_addr,
        nacos_config.username,
        nacos_config.password
    );
    let mut client = NacosClient::new(&nacos_config).await?;
    client.register_service().await.expect("Register db error");
    Ok(Arc::new(RwLock::new(client)))
}

pub async fn init_all_nacos(app_name: &str) -> Result<SharedNacosClient, AppError> {
    let nacos_config = NacosConfig::new(app_name);
    NACOS_CONFIG.set(nacos_config.clone()).ok();
    log::warn!(
        "Start…………………………………app_name:{:?},nacos:{:?},use_name{:?},password{:?}",
        app_name,
        nacos_config.server_addr,
        nacos_config.username,
        nacos_config.password
    );
    let mut client = NacosClient::new(&nacos_config).await?;

    // match client.get_yaml_config::<TopicConfig>("exchange-kafka-dev.yml", &nacos_config.group).await {
    //     Ok(config) => {
    //         log::warn!("成功从 Nacos 加载并解析 Kafka 配置:{:?}", config);
    //         // 使用 OnceLock::set() 设置值，它只允许成功设置一次
    //         if let Err(e) = TOPIC_CONFIG.set(config.topic) {
    //             log::error!("TOPIC_CONFIG already set! This should only happen once during startup.");
    //         }
    //     }
    //     Err(e) => {
    //         log::error!("从 Nacos 加载或解析 Topic 配置失败: {:?}", e);
    //     }
    // }

    // match client.get_yaml_config::<KafkaSpring>("kafka-dev.yml", &nacos_config.group).await {
    //     Ok(config) => {
    //         log::warn!("成功从 Nacos 加载并解析 Kafka 配置:{:?}", config);
    //         // 使用 OnceLock::set() 设置值，它只允许成功设置一次
    //         if let Err(e) = KAFKA_CONFIG.set(config.spring.kafka) {
    //             log::error!("KAFKA_CONFIG already set! This should only happen once during startup.");
    //         }
    //     }
    //     Err(e) => {
    //         log::error!("从 Nacos 加载或解析 Kafka 配置失败: {:?}", e);
    //     }
    // }

    match client.get_toml_config::<DatabaseConfig>("pgsql", &nacos_config.group).await {
        Ok(config) => {
            log::warn!("成功从 Nacos 加载并解析 USERDATABASE_CONFIG 配置:{:?}", config);
            if let Err(e) = USERDATABASE_CONFIG.set(config) {
                log::error!("USERDATABASE_CONFIG already set! This should only happen once during startup.");
            }
        }
        Err(e) => {
            log::error!("从 Nacos 加载或解析 USERDATABASE_CONFIG 配置失败: {:?}", e);
        }
    }

    match client.get_toml_config::<DatabaseConfig>("tsdb", &nacos_config.group).await {
        Ok(config) => {
            log::warn!("成功从 Nacos 加载并解析 TRADERDATABASE_CONFIG 配置:{:?}", config);
            if let Err(e) = TRADERDATABASE_CONFIG.set(config) {
                log::error!("TRADERDATABASE_CONFIG already set! This should only happen once during startup.");
            }
        }
        Err(e) => {
            log::error!("从 Nacos 加载或解析 TRADERDATABASE_CONFIG 配置失败: {:?}", e);
        }
    }

    match client.get_toml_config::<RedisConfig>("redis", &nacos_config.group).await {
        Ok(config) => {
            log::warn!("成功从 Nacos 加载并解析 Redis 配置:{:?}", config);
            if let Err(e) = REDIS_CONFIG.set(config) {
                log::error!("REDIS_CONFIG already set! This should only happen once during startup.");
            }
        }
        Err(e) => {
            log::error!("从 Nacos 加载或解析 Redis 配置失败: {:?}", e);
        }
    }
    client.register_service().await.expect("Register db error");
    Ok(Arc::new(RwLock::new(client)))
}
