pub mod kline_cache;

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use common::domain::dto::KlineDto;
use std::collections::HashMap;

/// 缓存操作相关的错误类型
#[derive(Debug, thiserror::Error)]
pub enum CacheError {
    #[error("Redis connection error: {0}")]
    ConnectionError(String),
    #[error("Serialization error: {0}")]
    SerializationError(String),
    #[error("Key not found: {0}")]
    KeyNotFound(String),
    #[error("Invalid data format: {0}")]
    InvalidDataFormat(String),
    #[error("Cache operation failed: {0}")]
    OperationFailed(String),
}

/// K线数据缓存仓库抽象接口
/// 
/// 提供对K线数据的缓存操作，包括存储、查询、过期时间管理等功能
#[async_trait]
pub trait CacheRepository: Send + Sync {
    /// 类型转换方法，用于downcast到具体实现类型
    fn as_any(&self) -> &dyn std::any::Any;

    /// 根据时间范围获取K线数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号 (如 "BTCUSDT")
    /// * `interval` - 时间间隔 (如 "1m", "5m", "1h")
    /// * `start_time` - 开始时间
    /// * `end_time` - 结束时间
    /// * `limit` - 最大返回数量限制
    /// 
    /// # Returns
    /// 返回指定时间范围内的K线数据列表，按时间升序排列
    async fn get_klines_by_range(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<usize>,
    ) -> Result<Vec<KlineDto>, CacheError>;

    /// 为指定日期添加K线数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `interval` - 时间间隔
    /// * `date` - 日期字符串 (YYYYMMDD格式)
    /// * `klines` - K线数据列表
    /// 
    /// # Returns
    /// 返回成功添加的K线数据数量
    async fn add_klines_for_day(
        &self,
        symbol: &str,
        interval: &str,
        date: &str,
        klines: &[KlineDto],
    ) -> Result<usize, CacheError>;

    /// 为指定日期的缓存键设置过期时间
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `date` - 日期字符串 (YYYYMMDD格式)
    /// * `ttl_seconds` - 过期时间（秒）
    async fn set_expiry_for_day(
        &self,
        symbol: &str,
        date: &str,
        ttl_seconds: u64,
    ) -> Result<(), CacheError>;

    /// 检查指定日期的缓存是否存在
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `date` - 日期字符串 (YYYYMMDD格式)
    /// 
    /// # Returns
    /// 返回缓存是否存在
    async fn exists_for_day(
        &self,
        symbol: &str,
        date: &str,
    ) -> Result<bool, CacheError>;

    /// 获取指定日期缓存的剩余TTL
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `date` - 日期字符串 (YYYYMMDD格式)
    /// 
    /// # Returns
    /// 返回剩余TTL秒数，-1表示永不过期，-2表示键不存在
    async fn get_ttl_for_day(
        &self,
        symbol: &str,
        date: &str,
    ) -> Result<i64, CacheError>;

    /// 批量获取多个日期的K线数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `interval` - 时间间隔
    /// * `dates` - 日期字符串列表
    /// 
    /// # Returns
    /// 返回日期到K线数据列表的映射
    async fn get_klines_for_dates(
        &self,
        symbol: &str,
        interval: &str,
        dates: &[String],
    ) -> Result<HashMap<String, Vec<KlineDto>>, CacheError>;

    /// 删除指定日期的缓存数据
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// * `date` - 日期字符串 (YYYYMMDD格式)
    async fn delete_day_cache(
        &self,
        symbol: &str,
        date: &str,
    ) -> Result<(), CacheError>;

    /// 获取指定交易对的缓存统计信息
    /// 
    /// # Arguments
    /// * `symbol` - 交易对符号
    /// 
    /// # Returns
    /// 返回该交易对的缓存统计信息
    async fn get_stats(&self, symbol: &str) -> Result<CacheStats, CacheError>;

    /// 获取缓存统计信息
    /// 
    /// # Returns
    /// 返回缓存的统计信息，如键数量、内存使用等
    async fn get_cache_stats(&self) -> Result<CacheStats, CacheError>;

    /// 健康检查
    /// 
    /// # Returns
    /// 返回缓存服务是否健康
    async fn health_check(&self) -> Result<bool, CacheError>;
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// 总键数量
    pub total_keys: u64,
    /// 内存使用量（字节）
    pub memory_usage: u64,
    /// 连接数
    pub connections: u32,
    /// 命中率
    pub hit_rate: f64,
    /// 其他统计信息
    pub additional_info: HashMap<String, String>,
}

/// TTL计算工具函数
pub struct TtlCalculator;

impl TtlCalculator {
    /// 标准缓存窗口大小（天）
    pub const CACHE_WINDOW_DAYS: i64 = 366;

    /// 计算历史数据的动态TTL
    /// 
    /// 公式: (键的日期 + 366天) - 当前日期
    /// 
    /// # Arguments
    /// * `key_date` - 键对应的日期 (YYYYMMDD格式)
    /// * `current_date` - 当前日期 (YYYYMMDD格式)
    /// 
    /// # Returns
    /// 返回TTL秒数，如果计算结果为负数则返回1秒（立即过期）
    pub fn calculate_dynamic_ttl(key_date: &str, current_date: &str) -> Result<u64, CacheError> {
        use chrono::NaiveDate;

        let key_date = NaiveDate::parse_from_str(key_date, "%Y%m%d")
            .map_err(|e| CacheError::InvalidDataFormat(format!("Invalid key date: {}", e)))?;
        
        let current_date = NaiveDate::parse_from_str(current_date, "%Y%m%d")
            .map_err(|e| CacheError::InvalidDataFormat(format!("Invalid current date: {}", e)))?;

        // 计算过期日期：键日期 + 366天
        let expiry_date = key_date + chrono::Duration::days(Self::CACHE_WINDOW_DAYS);
        
        // 计算剩余天数
        let remaining_days = (expiry_date - current_date).num_days();
        
        // 如果剩余天数小于等于0，返回1秒（立即过期）
        if remaining_days <= 0 {
            Ok(1)
        } else {
            // 转换为秒数
            Ok((remaining_days * 24 * 60 * 60) as u64)
        }
    }

    /// 获取固定TTL（用于新创建的当天键）
    /// 
    /// # Returns
    /// 返回固定的TTL秒数（366天）
    pub fn get_fixed_ttl() -> u64 {
        (Self::CACHE_WINDOW_DAYS * 24 * 60 * 60) as u64
    }

    /// 获取当前日期字符串 (YYYYMMDD格式)
    pub fn get_current_date_string() -> String {
        Utc::now().format("%Y%m%d").to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dynamic_ttl_calculation() {
        // 测试365天前的键，应该剩余1天TTL
        let ttl = TtlCalculator::calculate_dynamic_ttl("20230615", "20240615").unwrap();
        assert_eq!(ttl, 24 * 60 * 60); // 1天的秒数

        // 测试昨天的键，应该剩余365天TTL
        let ttl = TtlCalculator::calculate_dynamic_ttl("20240614", "20240615").unwrap();
        assert_eq!(ttl, 365 * 24 * 60 * 60); // 365天的秒数

        // 测试过期的键，应该返回1秒
        let ttl = TtlCalculator::calculate_dynamic_ttl("20220615", "20240615").unwrap();
        assert_eq!(ttl, 1);
    }

    #[test]
    fn test_fixed_ttl() {
        let ttl = TtlCalculator::get_fixed_ttl();
        assert_eq!(ttl, 366 * 24 * 60 * 60);
    }
}

pub use kline_cache::RedisCacheRepository;

#[cfg(test)]
pub use kline_cache::MockCacheRepository; 