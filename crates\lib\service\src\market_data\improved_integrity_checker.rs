// 改进的数据完整性检查器
// 简化逻辑，提高效率，减少数据库查询次数

use chrono::{DateTime, Utc, Duration};
use repository::timescale::KlineRepository;
use exchange::ExchangeClient;
use anyhow::Result;
use tracing::{info, warn, debug};
use std::collections::HashMap;

/// 简化的完整性检查结果
#[derive(Debug, Clone)]
pub struct SimpleIntegrityResult {
    pub symbol: String,
    pub interval: String,
    pub missing_ranges: Vec<(DateTime<Utc>, DateTime<Utc>)>,
    pub total_missing_minutes: i64,
    pub completeness_percentage: f64,
}

/// 改进的数据完整性检查器
pub struct ImprovedIntegrityChecker {
    kline_repo: KlineRepository,
    exchange_client: Box<dyn ExchangeClient>,
}

impl ImprovedIntegrityChecker {
    pub fn new(
        kline_repo: KlineRepository,
        exchange_client: Box<dyn ExchangeClient>,
    ) -> Self {
        Self {
            kline_repo,
            exchange_client,
        }
    }

    /// 简化的完整性检查 - 只检查1分钟数据
    pub async fn check_integrity(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<SimpleIntegrityResult> {
        let interval = "1m";
        
        // 1. 使用单个SQL查询获取所有缺失的时间段
        let missing_ranges = self.find_missing_ranges_efficient(
            symbol, 
            interval, 
            start_time, 
            end_time
        ).await?;

        // 2. 计算统计信息
        let total_missing_minutes: i64 = missing_ranges.iter()
            .map(|(start, end)| end.signed_duration_since(*start).num_minutes())
            .sum();

        let total_expected_minutes = end_time.signed_duration_since(start_time).num_minutes();
        let completeness_percentage = if total_expected_minutes > 0 {
            ((total_expected_minutes - total_missing_minutes) as f64 / total_expected_minutes as f64) * 100.0
        } else {
            100.0
        };

        Ok(SimpleIntegrityResult {
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            missing_ranges,
            total_missing_minutes,
            completeness_percentage,
        })
    }

    /// 高效的缺失范围查找 - 使用单个SQL查询
    async fn find_missing_ranges_efficient(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<(DateTime<Utc>, DateTime<Utc>)>> {
        // 使用TimescaleDB的time_bucket_gapfill功能一次性找出所有缺失时间段
        let query = r#"
            WITH expected_times AS (
                SELECT generate_series($3::timestamptz, $4::timestamptz, '1 minute'::interval) AS expected_time
            ),
            actual_times AS (
                SELECT time FROM klines 
                WHERE symbol = $1 AND interval = $2 
                AND time >= $3 AND time <= $4
            ),
            missing_times AS (
                SELECT expected_time FROM expected_times
                LEFT JOIN actual_times ON expected_times.expected_time = actual_times.time
                WHERE actual_times.time IS NULL
                ORDER BY expected_time
            )
            SELECT expected_time FROM missing_times
        "#;

        let missing_times: Vec<DateTime<Utc>> = sqlx::query_scalar(query)
            .bind(symbol)
            .bind(interval)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(self.kline_repo.pool())
            .await?;

        // 将连续的缺失时间点合并为时间段
        Ok(self.merge_consecutive_times(missing_times))
    }

    /// 将连续的时间点合并为时间段
    fn merge_consecutive_times(&self, times: Vec<DateTime<Utc>>) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        if times.is_empty() {
            return Vec::new();
        }

        let mut ranges = Vec::new();
        let mut range_start = times[0];
        let mut range_end = times[0];

        for &time in times.iter().skip(1) {
            if time == range_end + Duration::minutes(1) {
                // 连续时间，扩展当前范围
                range_end = time;
            } else {
                // 不连续，保存当前范围并开始新范围
                ranges.push((range_start, range_end + Duration::minutes(1)));
                range_start = time;
                range_end = time;
            }
        }

        // 添加最后一个范围
        ranges.push((range_start, range_end + Duration::minutes(1)));

        ranges
    }

    /// 智能批量修复缺失数据
    pub async fn fix_missing_data_batch(
        &self,
        symbol: &str,
        missing_ranges: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Result<u64> {
        if missing_ranges.is_empty() {
            return Ok(0);
        }

        info!("开始批量修复 {} 的缺失数据，共 {} 个时间段", symbol, missing_ranges.len());

        // 1. 智能合并小间隔的时间段
        let optimized_ranges = self.optimize_ranges_for_api(missing_ranges);
        info!("优化后需要 {} 次API调用", optimized_ranges.len());

        let mut total_fixed = 0u64;

        // 2. 批量处理，每次API调用获取更多数据
        for (i, &(start_time, end_time)) in optimized_ranges.iter().enumerate() {
            debug!("处理时间段 {}/{}: {} 到 {}", 
                i + 1, optimized_ranges.len(), start_time, end_time);

            match self.fetch_and_save_klines(symbol, start_time, end_time).await {
                Ok(count) => {
                    total_fixed += count;
                    debug!("成功修复 {} 条数据", count);
                }
                Err(e) => {
                    warn!("修复时间段失败: {}", e);
                }
            }

            // API限制保护
            if i < optimized_ranges.len() - 1 {
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }

        info!("批量修复完成，共修复 {} 条数据", total_fixed);
        Ok(total_fixed)
    }

    /// 优化时间段以减少API调用次数
    fn optimize_ranges_for_api(
        &self,
        ranges: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        if ranges.is_empty() {
            return Vec::new();
        }

        let mut optimized = Vec::new();
        let max_gap = Duration::hours(1); // 小于1小时的间隔进行合并
        let max_range_duration = Duration::hours(12); // 单次API调用最大12小时

        let mut current_start = ranges[0].0;
        let mut current_end = ranges[0].1;

        for &(start, end) in ranges.iter().skip(1) {
            let gap = start.signed_duration_since(current_end);
            let potential_duration = end.signed_duration_since(current_start);

            // 如果间隔小且合并后不超过最大时长，则合并
            if gap <= max_gap && potential_duration <= max_range_duration {
                current_end = end;
            } else {
                optimized.push((current_start, current_end));
                current_start = start;
                current_end = end;
            }
        }

        optimized.push((current_start, current_end));
        optimized
    }

    /// 获取并保存K线数据
    async fn fetch_and_save_klines(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<u64> {
        // 1. 从交易所获取数据
        let query = exchange::KlineQuery {
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            start_time: Some(start_time.timestamp_millis()),
            end_time: Some(end_time.timestamp_millis()),
            limit: Some(1000),
        };

        let klines = self.exchange_client.get_klines(query).await?;

        if klines.is_empty() {
            return Ok(0);
        }

        // 2. 转换并批量保存
        let kline_entities = self.convert_to_entities(symbol, &klines)?;
        let count = self.kline_repo.batch_insert_klines(&kline_entities).await?;

        Ok(count)
    }

    /// 转换API数据为实体
    fn convert_to_entities(
        &self,
        symbol: &str,
        klines: &[exchange::UniversalKline],
    ) -> Result<Vec<repository::timescale::KlineEntity>> {
        use rust_decimal::Decimal;
        use std::str::FromStr;

        let mut entities = Vec::new();

        for kline in klines {
            let entity = repository::timescale::KlineEntity {
                time: DateTime::from_timestamp_millis(kline.open_time)
                    .unwrap_or_else(Utc::now),
                symbol: symbol.to_string(),
                interval: "1m".to_string(),
                open_price: Decimal::from_str(&kline.open_price).unwrap_or_default(),
                high_price: Decimal::from_str(&kline.high_price).unwrap_or_default(),
                low_price: Decimal::from_str(&kline.low_price).unwrap_or_default(),
                close_price: Decimal::from_str(&kline.close_price).unwrap_or_default(),
                volume: Decimal::from_str(&kline.volume).unwrap_or_default(),
                quote_asset_volume: Decimal::from_str(&kline.quote_asset_volume).unwrap_or_default(),
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: Decimal::from_str(&kline.taker_buy_base_asset_volume).unwrap_or_default(),
                taker_buy_quote_asset_volume: Decimal::from_str(&kline.taker_buy_quote_asset_volume).unwrap_or_default(),
                close_time: DateTime::from_timestamp_millis(kline.close_time)
                    .unwrap_or_else(Utc::now),
            };
            entities.push(entity);
        }

        Ok(entities)
    }
}
