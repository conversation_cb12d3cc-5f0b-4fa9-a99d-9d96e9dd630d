use chrono::Utc;
use jsonwebtoken::{encode, Encoding<PERSON><PERSON>, <PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::env;
use crate::domain::r#enum::error::AppError;

/// JWT Claims 结构体
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    /// 用户ID
    pub sub: String,
    /// 过期时间
    pub exp: usize,
    /// 签发时间
    pub iat: usize,
    /// 签发者
    pub iss: String,
}

/// 生成 JWT token
pub fn generate_token(user_id: &str) -> Result<String, AppError> {
    let secret = env::var("JWT_SECRET")
        .map_err(|_| AppError::Internal("JWT_SECRET environment variable not set".into()))?;

    let now = Utc::now();
    let exp = (now + chrono::Duration::hours(24)).timestamp() as usize;
    let iat = now.timestamp() as usize;

    let claims = Claims {
        sub: user_id.to_string(),
        exp,
        iat,
        iss: "your-app-name".to_string(),
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_bytes()),
    )
    .map_err(|e| AppError::Internal(format!("Token generation failed: {}", e)))
} 