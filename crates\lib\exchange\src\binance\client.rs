use anyhow::Result;
use async_trait::async_trait;
use reqwest::Client;
use log::{debug, error, info};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

use crate::{ExchangeClient, UniversalKline, KlineQuery, ExchangeInfo, SymbolInfo};
use super::types::*;

/// Binance期货API客户端
#[derive(Clone)]
pub struct BinanceClient {
    client: Client,
    base_url: String,
    exchange_info: ExchangeInfo,
    api_type: BinanceApiType,
}

/// Binance API类型
#[derive(Clone, Debug)]
pub enum BinanceApiType {
    /// 现货API
    Spot,
    /// USDT-M期货API
    UsdtFutures,
    /// COIN-M期货API
    CoinFutures,
}

impl BinanceClient {
    /// 创建新的Binance期货客户端（默认USDT-M期货）
    pub fn new() -> Self {
        Self::new_futures()
    }

    /// 创建USDT-M期货客户端
    pub fn new_futures() -> Self {
        let base_url = "https://fapi.binance.com".to_string();
        info!("创建Binance USDT-M期货客户端: {}", base_url);
        
        Self {
            client: Client::new(),
            base_url: base_url.clone(),
            exchange_info: ExchangeInfo {
                name: "Binance USDT-M Futures".to_string(),
                base_url,
                rate_limit: 2400, // 期货API限制更高，每分钟2400次请求
            },
            api_type: BinanceApiType::UsdtFutures,
        }
    }

    /// 创建COIN-M期货客户端
    pub fn new_coin_futures() -> Self {
        let base_url = "https://dapi.binance.com".to_string();
        info!("创建Binance COIN-M期货客户端: {}", base_url);
        
        Self {
            client: Client::new(),
            base_url: base_url.clone(),
            exchange_info: ExchangeInfo {
                name: "Binance COIN-M Futures".to_string(),
                base_url,
                rate_limit: 2400,
            },
            api_type: BinanceApiType::CoinFutures,
        }
    }

    /// 创建现货客户端（向后兼容）
    pub fn new_spot() -> Self {
        let base_url = "https://api.binance.com".to_string();
        info!("创建Binance现货客户端: {}", base_url);
        
        Self {
            client: Client::new(),
            base_url: base_url.clone(),
            exchange_info: ExchangeInfo {
                name: "Binance Spot".to_string(),
                base_url,
                rate_limit: 1200,
            },
            api_type: BinanceApiType::Spot,
        }
    }

    /// 创建带自定义基础URL的客户端
    pub fn with_base_url(base_url: String) -> Self {
        let api_type = if base_url.contains("fapi") {
            BinanceApiType::UsdtFutures
        } else if base_url.contains("dapi") {
            BinanceApiType::CoinFutures
        } else {
            BinanceApiType::Spot
        };

        let (name, rate_limit) = match api_type {
            BinanceApiType::UsdtFutures => ("Binance USDT-M Futures", 2400),
            BinanceApiType::CoinFutures => ("Binance COIN-M Futures", 2400),
            BinanceApiType::Spot => ("Binance Spot", 1200),
        };

        Self {
            client: Client::new(),
            base_url: base_url.clone(),
            exchange_info: ExchangeInfo {
                name: name.to_string(),
                base_url,
                rate_limit,
            },
            api_type,
        }
    }

    /// 获取API端点路径
    fn get_klines_endpoint(&self) -> &str {
        match self.api_type {
            BinanceApiType::Spot => "/api/v3/klines",
            BinanceApiType::UsdtFutures => "/fapi/v1/klines",
            BinanceApiType::CoinFutures => "/dapi/v1/klines",
        }
    }

    /// 获取ping端点路径
    fn get_ping_endpoint(&self) -> &str {
        match self.api_type {
            BinanceApiType::Spot => "/api/v3/ping",
            BinanceApiType::UsdtFutures => "/fapi/v1/ping",
            BinanceApiType::CoinFutures => "/dapi/v1/ping",
        }
    }

    /// 获取服务器时间端点路径
    fn get_time_endpoint(&self) -> &str {
        match self.api_type {
            BinanceApiType::Spot => "/api/v3/time",
            BinanceApiType::UsdtFutures => "/fapi/v1/time",
            BinanceApiType::CoinFutures => "/dapi/v1/time",
        }
    }

    /// 获取交易所信息端点路径
    fn get_exchange_info_endpoint(&self) -> &str {
        match self.api_type {
            BinanceApiType::Spot => "/api/v3/exchangeInfo",
            BinanceApiType::UsdtFutures => "/fapi/v1/exchangeInfo",
            BinanceApiType::CoinFutures => "/dapi/v1/exchangeInfo",
        }
    }

    /// 将BinanceKline转换为UniversalKline
    fn convert_to_universal_kline(binance_kline: BinanceKline) -> UniversalKline {
        UniversalKline {
            open_time: binance_kline.open_time,
            open_price: binance_kline.open_price,
            high_price: binance_kline.high_price,
            low_price: binance_kline.low_price,
            close_price: binance_kline.close_price,
            volume: binance_kline.volume,
            close_time: binance_kline.close_time,
            quote_asset_volume: binance_kline.quote_asset_volume,
            number_of_trades: binance_kline.number_of_trades,
            taker_buy_base_asset_volume: binance_kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: binance_kline.taker_buy_quote_asset_volume,
        }
    }

    /// 获取K线数据（内部方法）
    async fn get_klines_internal(
        &self,
        symbol: &str,
        interval: &str,
        start_time: Option<i64>,
        end_time: Option<i64>,
        limit: Option<i32>,
    ) -> Result<Vec<BinanceKline>> {
        let mut params = HashMap::new();
        params.insert("symbol", symbol.to_string());
        params.insert("interval", interval.to_string());
        
        if let Some(start) = start_time {
            params.insert("startTime", start.to_string());
        }
        if let Some(end) = end_time {
            params.insert("endTime", end.to_string());
        }
        if let Some(limit) = limit {
            // 期货API的limit限制可能不同，这里统一处理
            let max_limit = match self.api_type {
                BinanceApiType::Spot => 1000,
                BinanceApiType::UsdtFutures => 1500,
                BinanceApiType::CoinFutures => 1500,
            };
            params.insert("limit", limit.min(max_limit).to_string());
        }

        let url = format!("{}{}", self.base_url, self.get_klines_endpoint());
        debug!("请求{}K线数据: {} (symbol: {}, interval: {})", 
               match self.api_type {
                   BinanceApiType::Spot => "现货",
                   BinanceApiType::UsdtFutures => "USDT-M期货",
                   BinanceApiType::CoinFutures => "COIN-M期货",
               }, url, symbol, interval);

        let response = self.client
            .get(&url)
            .query(&params)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("获取K线数据失败: {}", error_text);
            return Err(anyhow::anyhow!("API请求失败: {}", error_text));
        }

        let klines: Vec<Vec<serde_json::Value>> = response.json().await?;
        let mut result = Vec::new();

        for kline_data in klines {
            if kline_data.len() >= 12 {
                let kline = BinanceKline {
                    open_time: kline_data[0].as_i64().unwrap_or(0),
                    open_price: kline_data[1].as_str().unwrap_or("0").to_string(),
                    high_price: kline_data[2].as_str().unwrap_or("0").to_string(),
                    low_price: kline_data[3].as_str().unwrap_or("0").to_string(),
                    close_price: kline_data[4].as_str().unwrap_or("0").to_string(),
                    volume: kline_data[5].as_str().unwrap_or("0").to_string(),
                    close_time: kline_data[6].as_i64().unwrap_or(0),
                    quote_asset_volume: kline_data[7].as_str().unwrap_or("0").to_string(),
                    number_of_trades: kline_data[8].as_i64().unwrap_or(0) as i32,
                    taker_buy_base_asset_volume: kline_data[9].as_str().unwrap_or("0").to_string(),
                    taker_buy_quote_asset_volume: kline_data[10].as_str().unwrap_or("0").to_string(),
                    ignore: kline_data[11].as_str().unwrap_or("0").to_string(),
                };
                result.push(kline);
            }
        }

        debug!("成功获取{}条K线数据", result.len());
        Ok(result)
    }
}

/// 实现统一的交易所客户端接口
#[async_trait]
impl ExchangeClient for BinanceClient {
    fn get_exchange_info(&self) -> &ExchangeInfo {
        &self.exchange_info
    }

    async fn get_klines(&self, query: KlineQuery) -> Result<Vec<UniversalKline>> {
        let binance_klines = self.get_klines_internal(
            &query.symbol,
            &query.interval,
            query.start_time,
            query.end_time,
            query.limit,
        ).await?;

        let universal_klines: Vec<UniversalKline> = binance_klines
            .into_iter()
            .map(Self::convert_to_universal_kline)
            .collect();

        debug!("转换完成，返回{}条UniversalKline数据", universal_klines.len());
        Ok(universal_klines)
    }

    async fn test_connection(&self) -> Result<()> {
        let url = format!("{}{}", self.base_url, self.get_ping_endpoint());
        debug!("测试{}连接: {}", 
               match self.api_type {
                   BinanceApiType::Spot => "现货",
                   BinanceApiType::UsdtFutures => "USDT-M期货",
                   BinanceApiType::CoinFutures => "COIN-M期货",
               }, url);
        
        let response = self.client.get(&url).send().await?;
        
        if response.status().is_success() {
            info!("{}连接测试成功", self.exchange_info.name);
            Ok(())
        } else {
            error!("{}连接测试失败", self.exchange_info.name);
            Err(anyhow::anyhow!("连接测试失败"))
        }
    }

    async fn get_server_time(&self) -> Result<DateTime<Utc>> {
        let url = format!("{}{}", self.base_url, self.get_time_endpoint());
        let response = self.client.get(&url).send().await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("获取{}服务器时间失败", self.exchange_info.name));
        }

        let time_data: serde_json::Value = response.json().await?;
        let server_time = time_data["serverTime"].as_i64()
            .ok_or_else(|| anyhow::anyhow!("服务器时间格式错误"))?;

        let datetime = DateTime::from_timestamp_millis(server_time)
            .unwrap_or_else(|| {
                error!("无效的服务器时间戳: {}", server_time);
                Utc::now()
            });

        debug!("获取{}服务器时间: {}", self.exchange_info.name, datetime);
        Ok(datetime)
    }

    async fn get_symbol_info(&self, symbol: &str) -> Result<SymbolInfo> {
        let url = format!("{}{}", self.base_url, self.get_exchange_info_endpoint());
        debug!("获取{}交易对信息: {} (symbol: {})", self.exchange_info.name, url, symbol);
        
        let response = self.client.get(&url).send().await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("获取{}交易对信息失败", self.exchange_info.name));
        }

        let exchange_info: serde_json::Value = response.json().await?;
        let symbols = exchange_info["symbols"].as_array()
            .ok_or_else(|| anyhow::anyhow!("交易对信息格式错误"))?;

        for symbol_data in symbols {
            if symbol_data["symbol"].as_str() == Some(symbol) {
                let symbol_info = SymbolInfo {
                    symbol: symbol_data["symbol"].as_str().unwrap_or("").to_string(),
                    base_asset: symbol_data["baseAsset"].as_str().unwrap_or("").to_string(),
                    quote_asset: symbol_data["quoteAsset"].as_str().unwrap_or("").to_string(),
                    status: symbol_data["status"].as_str().unwrap_or("").to_string(),
                    min_qty: self.extract_filter_value(symbol_data, "LOT_SIZE", "minQty"),
                    max_qty: self.extract_filter_value(symbol_data, "LOT_SIZE", "maxQty"),
                    step_size: self.extract_filter_value(symbol_data, "LOT_SIZE", "stepSize"),
                };
                
                debug!("找到交易对信息: {} ({}/{})", symbol_info.symbol, 
                       symbol_info.base_asset, symbol_info.quote_asset);
                return Ok(symbol_info);
            }
        }

        Err(anyhow::anyhow!("未找到交易对: {}", symbol))
    }

    fn clone_box(&self) -> Box<dyn ExchangeClient> {
        Box::new(self.clone())
    }
}

impl BinanceClient {
    /// 提取过滤器值的辅助方法
    fn extract_filter_value(&self, symbol_data: &serde_json::Value, filter_type: &str, field: &str) -> String {
        symbol_data["filters"]
            .as_array()
            .and_then(|filters| {
                filters.iter().find(|f| f["filterType"] == filter_type)
            })
            .and_then(|f| f[field].as_str())
            .unwrap_or("0")
            .to_string()
    }
} 