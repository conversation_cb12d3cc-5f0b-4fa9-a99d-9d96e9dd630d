use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// K线数据实体 - 数据库映射
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct KlineEntity {
    /// 时间戳（主键时间）
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 时间间隔 (可选，用于兼容性)
    pub interval: Option<String>,
    /// 开盘价
    pub open_price: Decimal,
    /// 最高价
    pub high_price: Decimal,
    /// 最低价
    pub low_price: Decimal,
    /// 收盘价
    pub close_price: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 计价资产成交量
    pub quote_asset_volume: Decimal,
    /// 成交笔数
    pub number_of_trades: i32,
    /// 主动买入成交量
    pub taker_buy_base_asset_volume: Decimal,
    /// 主动买入计价资产成交量
    pub taker_buy_quote_asset_volume: Decimal,
    /// 收盘时间
    pub close_time: DateTime<Utc>,
}

impl KlineEntity {
    /// 获取interval值，提供默认值以保持向后兼容性
    pub fn get_interval(&self) -> &str {
        self.interval.as_deref().unwrap_or("1m")
    }
    
    /// 从旧格式创建新实体（兼容性方法）
    pub fn from_legacy(
        time: DateTime<Utc>,
        symbol: String,
        interval: String,
        open_price: Decimal,
        high_price: Decimal,
        low_price: Decimal,
        close_price: Decimal,
        volume: Decimal,
        quote_asset_volume: Decimal,
        number_of_trades: i32,
        taker_buy_base_asset_volume: Decimal,
        taker_buy_quote_asset_volume: Decimal,
        close_time: DateTime<Utc>,
    ) -> Self {
        Self {
            time,
            symbol,
            interval: if interval.is_empty() { None } else { Some(interval) },
            open_price,
            high_price,
            low_price,
            close_price,
            volume,
            quote_asset_volume,
            number_of_trades,
            taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume,
            close_time,
        }
    }
}