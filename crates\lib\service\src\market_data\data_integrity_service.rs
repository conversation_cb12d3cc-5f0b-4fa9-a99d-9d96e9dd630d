// 数据完整性检查服务
// 负责检查K线数据的完整性，识别缺失数据并进行补充

use chrono::{DateTime, Utc, Duration, Datelike, Timelike};
use repository::{
    timescale::{MarketDataRepository, SymbolRepository, CreateMarketDataRequest},
    connection::timescale::TimescalePoolManager,
};
use exchange::{binance::BinanceClient, ExchangeClient, KlineQuery};
use common::domain::{dto::KlineDto, entity::KlineEntity};
use crate::market_data::{KlineInterval, IntegrityCheckResult, Result, MarketDataServiceError};
use std::collections::HashMap;

/// 数据完整性检查服务
pub struct DataIntegrityService {
    /// TimescaleDB数据仓库
    market_data_repo: MarketDataRepository,
    /// 交易对仓库
    symbol_repo: SymbolRepository,
    /// Binance客户端
    binance_client: BinanceClient,
}

impl DataIntegrityService {
    /// 创建新的数据完整性检查服务
    pub async fn new(
        timescale_pool: &TimescalePoolManager,
        binance_client: BinanceClient,
    ) -> Result<Self> {
        let pool = timescale_pool.pool().clone();
        let market_data_repo = MarketDataRepository::new(pool.clone());
        let symbol_repo = SymbolRepository::new(pool);

        Ok(Self {
            market_data_repo,
            symbol_repo,
            binance_client,
        })
    }

    /// 获取市场数据仓库的引用
    pub fn get_repository(&self) -> &MarketDataRepository {
        &self.market_data_repo
    }

    /// 执行全量数据完整性检查
    /// 
    /// # Arguments
    /// * `start_date` - 检查的开始日期
    /// * `end_date` - 检查的结束日期（可选，默认为今天）
    pub async fn perform_full_integrity_check(
        &self,
        start_date: DateTime<Utc>,
        end_date: Option<DateTime<Utc>>,
    ) -> Result<Vec<IntegrityCheckResult>> {
        let end_date = end_date.unwrap_or_else(|| Utc::now());
        
        log::info!("开始执行全量数据完整性检查，时间范围: {} 到 {}", start_date, end_date);

        // 1. 获取所有需要维护的交易对
        let symbols = self.get_active_symbols().await?;
        log::info!("找到 {} 个需要检查的交易对", symbols.len());

        let mut all_results = Vec::new();

        // 2. 对每个交易对进行完整性检查
        for symbol in symbols {
            log::info!("检查交易对: {}", symbol);
            
            // 检查所有时间间隔
            for interval in KlineInterval::all() {
                log::debug!("检查 {} 的 {} 时间间隔", symbol, interval);
                
                match self.check_symbol_interval_integrity(
                    &symbol, 
                    &interval, 
                    start_date, 
                    end_date
                ).await {
                    Ok(result) => {
                        log::info!(
                            "{} {} 完整性: {:.2}% ({}/{})",
                            symbol,
                            interval,
                            result.completeness_percentage,
                            result.actual_count,
                            result.expected_count
                        );
                        
                        // 如果有缺失数据，进行补充
                        if !result.missing_periods.is_empty() {
                            log::warn!("{} {} 发现 {} 个缺失时间段", 
                                symbol, interval, result.missing_periods.len());
                            
                            if let Err(e) = self.fill_missing_data(
                                &symbol, 
                                &interval, 
                                &result.missing_periods
                            ).await {
                                log::error!("补充缺失数据失败: {}", e);
                            }
                        }
                        
                        all_results.push(result);
                    }
                    Err(e) => {
                        log::error!("检查 {} {} 失败: {}", symbol, interval, e);
                    }
                }
            }
        }

        log::info!("全量数据完整性检查完成，共检查了 {} 个结果", all_results.len());
        Ok(all_results)
    }

    /// 检查单个交易对和时间间隔的数据完整性
    async fn check_symbol_interval_integrity(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<IntegrityCheckResult> {
        let interval_str = interval.to_binance_string();
        
        // 使用TimescaleDB的时间桶功能检查数据完整性
        let bucket_interval = self.get_bucket_interval_for_kline_interval(interval);
        
        // 查询时间桶聚合数据
        let bucket_counts = self.market_data_repo.klines
            .query_time_bucket_aggregation(
                symbol,
                interval_str,
                &bucket_interval,
                start_time,
                end_time,
            )
            .await?;

        // 计算预期的数据点数量
        let expected_count = self.calculate_expected_data_points(interval, start_time, end_time);
        
        // 计算实际数据点数量
        let actual_count: i64 = bucket_counts.iter().map(|bc| bc.actual_count).sum();
        
        // 识别缺失的时间段 - 使用精确的时间桶分析
        let missing_periods = self.identify_missing_periods_precise(
            symbol,
            interval,
            start_time,
            end_time,
        ).await?;

        // 计算完整性百分比
        let completeness_percentage = if expected_count > 0 {
            (actual_count as f64 / expected_count as f64) * 100.0
        } else {
            100.0
        };

        Ok(IntegrityCheckResult {
            symbol: symbol.to_string(),
            interval: interval.clone(),
            start_time,
            end_time,
            missing_periods,
            expected_count,
            actual_count,
            completeness_percentage,
        })
    }

    /// 获取活跃的交易对列表
    async fn get_active_symbols(&self) -> Result<Vec<String>> {
        // 从symbols表获取需要维护的交易对
        let symbols = self.symbol_repo.get_active_symbols().await
            .map_err(|e| MarketDataServiceError::Database(e))?;
        
        if symbols.is_empty() {
            log::warn!("未找到活跃的交易对，将使用默认列表");
            // 如果没有配置，使用一些常见的交易对
            Ok(vec![
                "BTCUSDT".to_string(),
                "ETHUSDT".to_string(),
                "BNBUSDT".to_string(),
                "ADAUSDT".to_string(),
                "SOLUSDT".to_string(),
            ])
        } else {
            // 转换Symbol到String
            Ok(symbols.into_iter().map(|s| s.symbol).collect())
        }
    }

    /// 根据K线间隔获取相应的时间桶间隔
    /// 优化：使用更精确的时间桶粒度，特别是对于分钟级数据
    fn get_bucket_interval_for_kline_interval(&self, interval: &KlineInterval) -> String {
        match interval {
            // 对于分钟级数据，使用分钟级时间桶以获得精确定位
            KlineInterval::OneMinute => "1 minute".to_string(),
            KlineInterval::ThreeMinutes => "3 minutes".to_string(),
            KlineInterval::FiveMinutes => "5 minutes".to_string(),
            KlineInterval::FifteenMinutes => "15 minutes".to_string(),
            KlineInterval::ThirtyMinutes => "30 minutes".to_string(),
            // 对于小时级数据，使用小时级时间桶
            KlineInterval::OneHour => "1 hour".to_string(),
            KlineInterval::TwoHours => "2 hours".to_string(),
            KlineInterval::FourHours => "4 hours".to_string(),
            KlineInterval::SixHours | KlineInterval::EightHours | KlineInterval::TwelveHours => {
                "1 day".to_string()
            }
            // 对于日级数据，使用日级时间桶
            KlineInterval::OneDay | KlineInterval::ThreeDays => {
                "1 day".to_string()
            }
            KlineInterval::OneWeek | KlineInterval::OneMonth => {
                "1 week".to_string()
            }
        }
    }

    /// 计算预期的数据点数量
    fn calculate_expected_data_points(
        &self,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> i64 {
        let duration = end_time - start_time;
        let interval_minutes = interval.to_minutes();
        let total_minutes = duration.num_minutes();
        
        // 考虑市场休市时间（加密货币市场24/7运行，所以直接计算）
        total_minutes / interval_minutes
    }

    /// 识别缺失的时间段
    fn identify_missing_periods(
        &self,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        bucket_counts: &[repository::timescale::TimeBucketCount],
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        let interval_duration = Duration::minutes(interval.to_minutes());
        
        // 创建时间桶映射
        let bucket_map: HashMap<DateTime<Utc>, i64> = bucket_counts
            .iter()
            .map(|bc| (bc.time_bucket, bc.actual_count))
            .collect();

        // 首先收集所有缺失的时间点
        let mut missing_points = Vec::new();
        let mut current_time = start_time;
        
        while current_time < end_time {
            let bucket_time = self.align_to_bucket(current_time, interval);
            
            // 检查这个时间桶是否有数据
            if !bucket_map.contains_key(&bucket_time) || bucket_map[&bucket_time] == 0 {
                missing_points.push(current_time);
            }
            current_time += interval_duration;
        }

        if missing_points.is_empty() {
            return Vec::new();
        }

        log::debug!("发现 {} 个缺失时间点，开始合并相邻时间段", missing_points.len());

        // 合并相邻的缺失时间点为时间段
        let missing_periods = self.merge_adjacent_missing_points(&missing_points, interval_duration);
        
        log::debug!("合并后得到 {} 个缺失时间段", missing_periods.len());

        // 进一步优化：限制单个时间段的最大长度和最小合并间隔
        let optimized_periods = self.optimize_missing_periods(&missing_periods, interval);
        
        log::debug!("优化后得到 {} 个最终缺失时间段", optimized_periods.len());

        optimized_periods
    }

    /// 合并相邻的缺失时间点为时间段
    fn merge_adjacent_missing_points(
        &self,
        missing_points: &[DateTime<Utc>],
        interval_duration: Duration,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        if missing_points.is_empty() {
            return Vec::new();
        }

        let mut periods = Vec::new();
        let mut current_start = missing_points[0];
        let mut current_end = missing_points[0] + interval_duration;

        for &point in missing_points.iter().skip(1) {
            // 如果当前点与前一个时间段相邻或重叠，则扩展时间段
            if point <= current_end || point - current_end <= interval_duration {
                current_end = point + interval_duration;
            } else {
                // 否则，保存当前时间段并开始新的时间段
                periods.push((current_start, current_end));
                current_start = point;
                current_end = point + interval_duration;
            }
        }

        // 添加最后一个时间段
        periods.push((current_start, current_end));

        periods
    }

    /// 优化缺失时间段：合并小间隔、分割超大时间段
    fn optimize_missing_periods(
        &self,
        periods: &[(DateTime<Utc>, DateTime<Utc>)],
        interval: &KlineInterval,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        let mut optimized = Vec::new();
        let max_period_duration = Duration::hours(24); // 最大24小时
        let min_gap_duration = Duration::hours(1); // 小于1小时的间隔进行合并
        
        let mut i = 0;
        while i < periods.len() {
            let mut current_start = periods[i].0;
            let mut current_end = periods[i].1;
            
            // 尝试合并相近的时间段
            while i + 1 < periods.len() {
                let next_start = periods[i + 1].0;
                let gap = next_start - current_end;
                
                // 如果间隔很小，则合并
                if gap <= min_gap_duration {
                    current_end = periods[i + 1].1;
                    i += 1;
                } else {
                    break;
                }
            }
            
            // 检查时间段是否过大，需要分割
            let period_duration = current_end - current_start;
            if period_duration > max_period_duration {
                let sub_periods = self.split_large_period(current_start, current_end, max_period_duration);
                let sub_periods_count = sub_periods.len();
                optimized.extend(sub_periods);
                log::debug!("分割超大时间段: {} 到 {} → {} 个子时间段", 
                    current_start, current_end, sub_periods_count);
            } else {
                optimized.push((current_start, current_end));
            }
            
            i += 1;
        }

        // 进一步优化：如果有很多小时间段，考虑批量合并
        if optimized.len() > 100 {
            log::warn!("检测到 {} 个缺失时间段，可能是分散缺失，尝试批量合并", optimized.len());
            optimized = self.batch_merge_small_periods(&optimized, interval);
        }

        optimized
    }

    /// 精确识别缺失的时间段 - 参考旧版本的实现
    /// 使用 time_bucket_gapfill 进行精确的缺失数据检测
    async fn identify_missing_periods_precise(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<(DateTime<Utc>, DateTime<Utc>)>> {
        let interval_str = interval.to_binance_string();
        
        // 使用精确的时间桶间隔
        let bucket_interval = match interval {
            KlineInterval::OneMinute => "1 minute",
            KlineInterval::ThreeMinutes => "3 minutes", 
            KlineInterval::FiveMinutes => "5 minutes",
            KlineInterval::FifteenMinutes => "15 minutes",
            KlineInterval::ThirtyMinutes => "30 minutes",
            KlineInterval::OneHour => "1 hour",
            KlineInterval::TwoHours => "2 hours",
            KlineInterval::FourHours => "4 hours",
            _ => "1 hour", // 默认值
        };

        log::debug!("使用精确时间桶 '{}' 检测 {} {} 的缺失数据，时间范围: {} 到 {}", 
            bucket_interval, symbol, interval_str,
            start_time.format("%Y-%m-%d %H:%M:%S"), 
            end_time.format("%Y-%m-%d %H:%M:%S"));

        // 查询精确的时间桶数据，包括缺失的时间点
        let time_buckets = self.market_data_repo.klines
            .find_missing_time_buckets(
                symbol,
                interval_str,
                start_time,
                end_time,
            )
            .await
            .map_err(|e| MarketDataServiceError::Database(e))?;

        log::debug!("精确时间桶查询返回 {} 个时间桶", time_buckets.len());

        // 分析时间桶，识别缺失的连续时间段
        let mut missing_ranges = Vec::new();
        let mut current_missing_start: Option<DateTime<Utc>> = None;
        let mut last_missing_time: Option<DateTime<Utc>> = None;

        for (i, bucket) in time_buckets.iter().enumerate() {
            let bucket_time = bucket.minute_bucket;
            let data_count = bucket.data_count.unwrap_or(0);

            // 记录前几个和后几个桶的详细信息用于调试
            if i < 3 || i >= time_buckets.len() - 3 {
                log::debug!("  时间桶 {}/{}: {} -> {} 条数据", 
                    i + 1, time_buckets.len(), bucket_time.format("%Y-%m-%d %H:%M:%S"), data_count);
            }

            if data_count == 0 {
                // 当前时间桶缺失数据
                if current_missing_start.is_none() {
                    current_missing_start = Some(bucket_time);
                    log::debug!("发现缺失数据开始于: {}", bucket_time.format("%Y-%m-%d %H:%M:%S"));
                }
                last_missing_time = Some(bucket_time);
                
                // 如果这是最后一个桶且仍然缺失，需要结束当前缺失范围
                if i == time_buckets.len() - 1 {
                    if let Some(start_time) = current_missing_start {
                        let end_time = last_missing_time.unwrap();
                        log::debug!("在结尾处结束缺失范围: {} 到 {}", 
                            start_time.format("%Y-%m-%d %H:%M:%S"), 
                            end_time.format("%Y-%m-%d %H:%M:%S"));
                        missing_ranges.push((start_time, end_time));
                        current_missing_start = None;
                    }
                }
            } else {
                // 当前时间桶有数据
                if let Some(start_time) = current_missing_start {
                    // 结束当前缺失范围，结束时间是最后一个缺失的时间桶
                    let end_time = last_missing_time.unwrap();
                    log::debug!("结束缺失范围: {} 到 {}", 
                        start_time.format("%Y-%m-%d %H:%M:%S"), 
                        end_time.format("%Y-%m-%d %H:%M:%S"));
                    missing_ranges.push((start_time, end_time));
                    current_missing_start = None;
                    last_missing_time = None;
                }
            }
        }

        log::info!("精确检测发现 {} 个缺失时间段", missing_ranges.len());
        for (i, (start, end)) in missing_ranges.iter().enumerate() {
            // 修正时间间隔计算逻辑
            let interval_minutes = interval.to_minutes();
            let total_minutes = end.signed_duration_since(*start).num_minutes();
            
            // 对于时间桶，如果start == end，说明只有一个时间桶缺失
            let interval_count = if start == end {
                1
            } else {
                // 计算包含的完整间隔数量
                ((total_minutes + interval_minutes) / interval_minutes).max(1)
            };
            
            log::info!("  缺失段 {}: {} 到 {} ({} 个{}间隔, 共{}分钟)", 
                i + 1, 
                start.format("%Y-%m-%d %H:%M:%S"), 
                end.format("%Y-%m-%d %H:%M:%S"),
                interval_count,
                interval_str,
                total_minutes + interval_minutes); // 包含结束时间的总时长
        }

        // 应用旧版本的优化逻辑：合并相邻时间段、分割超大时间段
        let optimized_periods = self.optimize_missing_periods(&missing_ranges, interval);
        
        log::info!("优化后最终得到 {} 个缺失时间段", optimized_periods.len());

        Ok(optimized_periods)
    }

    /// 批量合并小时间段
    fn batch_merge_small_periods(
        &self,
        periods: &[(DateTime<Utc>, DateTime<Utc>)],
        interval: &KlineInterval,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        if periods.is_empty() {
            return Vec::new();
        }

        let mut merged = Vec::new();
        let max_merge_duration = Duration::hours(6); // 最大合并为6小时段
        let interval_duration = Duration::minutes(interval.to_minutes());
        
        let mut current_start = periods[0].0;
        let mut current_end = periods[0].1;
        let mut periods_in_current_batch = 1;
        
        for &(start, end) in periods.iter().skip(1) {
            let potential_end = end;
            let potential_duration = potential_end - current_start;
            
            // 如果合并后的时间段不超过最大限制，继续合并
            if potential_duration <= max_merge_duration {
                current_end = potential_end;
                periods_in_current_batch += 1;
            } else {
                // 保存当前批次
                merged.push((current_start, current_end));
                log::debug!("批量合并: {} 个小时间段合并为 {} 到 {}", 
                    periods_in_current_batch, current_start, current_end);
                
                // 开始新批次
                current_start = start;
                current_end = end;
                periods_in_current_batch = 1;
            }
        }
        
        // 添加最后一个批次
        merged.push((current_start, current_end));
        if periods_in_current_batch > 1 {
            log::debug!("批量合并: {} 个小时间段合并为 {} 到 {}", 
                periods_in_current_batch, current_start, current_end);
        }
        
        log::info!("批量合并完成: {} 个原始时间段 → {} 个合并时间段", 
            periods.len(), merged.len());
        
        merged
    }

    /// 分割超大时间段
    fn split_large_period(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        max_duration: Duration,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        let mut periods = Vec::new();
        let mut current_start = start_time;
        
        while current_start < end_time {
            let current_end = std::cmp::min(current_start + max_duration, end_time);
            periods.push((current_start, current_end));
            current_start = current_end;
        }
        
        log::debug!("分割时间段 {} 到 {} 为 {} 个子时间段", 
            start_time, end_time, periods.len());
        
        periods
    }

    /// 将时间对齐到时间桶
    fn align_to_bucket(&self, time: DateTime<Utc>, interval: &KlineInterval) -> DateTime<Utc> {
        match interval {
            KlineInterval::OneMinute => time.with_second(0).unwrap().with_nanosecond(0).unwrap(),
            KlineInterval::ThreeMinutes => {
                let minute = (time.minute() / 3) * 3;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::FiveMinutes => {
                let minute = (time.minute() / 5) * 5;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::FifteenMinutes => {
                let minute = (time.minute() / 15) * 15;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::ThirtyMinutes => {
                let minute = (time.minute() / 30) * 30;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::OneHour => {
                time.with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::TwoHours => {
                let hour = (time.hour() / 2) * 2;
                time.with_hour(hour).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::FourHours => {
                let hour = (time.hour() / 4) * 4;
                time.with_hour(hour).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            KlineInterval::OneDay => {
                time.with_hour(0).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            }
            _ => time, // 其他间隔的简化处理
        }
    }

    /// 补充缺失的数据
    async fn fill_missing_data(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Result<()> {
        let total_periods = missing_periods.len();
        log::info!("开始补充 {} {} 的缺失数据，共 {} 个时间段", 
            symbol, interval, total_periods);

        if total_periods == 0 {
            return Ok(());
        }

        // 计算每个时间段的预估数据量并进行智能分批
        let batches = self.create_intelligent_batches(interval, missing_periods);
        
        // 预处理时间段以获取实际要处理的时间段列表
        const BINANCE_LIMIT: i64 = 2400;
        const SAFETY_MARGIN: f64 = 0.8;
        let effective_limit = (BINANCE_LIMIT as f64 * SAFETY_MARGIN) as i64;
        let processed_periods = self.preprocess_large_periods(interval, missing_periods, effective_limit);
        
        log::info!("📊 智能分批结果: {} 个原始时间段预处理为 {} 个时间段，分为 {} 批", 
            total_periods, 
            processed_periods.len(),
            batches.len()
        );

        let mut successful_periods = 0;
        let mut failed_periods = 0;
        let mut total_klines_saved = 0;

        for (batch_index, batch) in batches.iter().enumerate() {
            let batch_start = batch.iter().map(|&i| i).min().unwrap_or(0) + 1;
            let batch_end = batch.iter().map(|&i| i).max().unwrap_or(0) + 1;
            let estimated_klines = batch.iter()
                .map(|&index| {
                    let (start_time, end_time) = processed_periods[index];
                    self.estimate_period_klines(interval, start_time, end_time)
                })
                .sum::<i64>();
            
            log::info!("处理第 {}/{} 批次 (时间段 {}-{}, 预估{}条K线)", 
                batch_index + 1, 
                batches.len(),
                batch_start,
                batch_end,
                estimated_klines
            );

            for &period_index in batch {
                let (start_time, end_time) = processed_periods[period_index];
                let global_index = period_index + 1;
                
                log::debug!("处理时间段 {}/{}: {} 到 {} (预估{}条)", 
                    global_index, processed_periods.len(), start_time, end_time,
                    self.estimate_period_klines(interval, start_time, end_time));
                
                // 添加超时控制
                let fetch_result = tokio::time::timeout(
                    tokio::time::Duration::from_secs(30), // 30秒超时
                    self.fetch_klines_from_binance(symbol, interval, start_time, end_time)
                ).await;

                match fetch_result {
                    Ok(Ok(klines)) => {
                        if !klines.is_empty() {
                            // 转换为数据库格式并保存
                            let requests: Vec<CreateMarketDataRequest> = klines
                                .into_iter()
                                .map(|kline| self.kline_dto_to_request(kline, interval))
                                .collect();

                            let klines_count = requests.len();
                            
                            match self.market_data_repo.klines.batch_insert(requests).await {
                                Ok(_) => {
                                    successful_periods += 1;
                                    total_klines_saved += klines_count;
                                    log::debug!("✅ 时间段 {}/{}: 成功保存 {} 条K线数据", 
                                        global_index, processed_periods.len(), klines_count);
                                }
                                Err(e) => {
                                    failed_periods += 1;
                                    log::error!("❌ 时间段 {}/{}: 保存K线数据失败: {}", 
                                        global_index, processed_periods.len(), e);
                                }
                            }
                        } else {
                            log::warn!("⚠️  时间段 {}/{}: API返回空数据", global_index, processed_periods.len());
                            successful_periods += 1; // 算作成功，因为可能这个时间段确实没有数据
                        }
                    }
                    Ok(Err(e)) => {
                        failed_periods += 1;
                        log::error!("❌ 时间段 {}/{}: 从Binance获取数据失败: {}", 
                            global_index, processed_periods.len(), e);
                    }
                    Err(_) => {
                        failed_periods += 1;
                        log::error!("❌ 时间段 {}/{}: API调用超时", global_index, processed_periods.len());
                    }
                }

                // 每个时间段之间添加延迟避免API限制
                tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
            }

            // 每批次之间的进度报告
            let current_progress = ((batch_end as f64 / processed_periods.len() as f64) * 100.0) as u32;
            log::info!("📊 进度更新: {}% ({}/{}) - 成功: {}, 失败: {}, 已保存K线: {}", 
                current_progress, batch_end, processed_periods.len(), 
                successful_periods, failed_periods, total_klines_saved);

            // 批次之间稍长的延迟，给API服务器缓冲时间
            if batch_index < batches.len() - 1 {
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
            }
        }

        // 最终统计
        log::info!("✅ {} {} 数据补充完成: 原始时间段: {}, 处理时间段: {}, 成功: {}, 失败: {}, 保存K线: {}", 
            symbol, interval, total_periods, processed_periods.len(), successful_periods, failed_periods, total_klines_saved);

        if failed_periods > 0 {
            log::warn!("⚠️  有 {} 个时间段补充失败，可能需要稍后重试", failed_periods);
        }

        Ok(())
    }

    /// 创建智能分批策略
    /// 根据时间间隔和缺失时间段的实际数据量进行分批，确保每批不超过Binance API限制
    fn create_intelligent_batches(
        &self,
        interval: &KlineInterval,
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> Vec<Vec<usize>> {
        const BINANCE_LIMIT: i64 = 1000; // Binance现货API限制
        const SAFETY_MARGIN: f64 = 0.8; // 安全边际，使用80%的限制
        let effective_limit = (BINANCE_LIMIT as f64 * SAFETY_MARGIN) as i64;
        
        // 预处理：分割超大时间段
        let processed_periods = self.preprocess_large_periods(interval, missing_periods, effective_limit);
        
        // 输出每个时间段的详细信息用于调试
        log::info!("📊 智能分批分析 (有效限制: {} 条):", effective_limit);
        for (i, &(start_time, end_time)) in processed_periods.iter().enumerate() {
            let period_estimate = self.estimate_period_klines(interval, start_time, end_time);
            let duration = end_time - start_time;
            log::info!("   时间段 {}: {} 到 {} (时长: {}分钟, 预估: {}条)", 
                i + 1, start_time.format("%Y-%m-%d %H:%M"), end_time.format("%Y-%m-%d %H:%M"), 
                duration.num_minutes(), period_estimate);
        }
        
        let mut batches = Vec::new();
        let mut current_batch = Vec::new();
        let mut current_batch_estimate = 0i64;

        for (index, &(start_time, end_time)) in processed_periods.iter().enumerate() {
            let period_estimate = self.estimate_period_klines(interval, start_time, end_time);
            
            // 如果单个时间段仍然超过限制，记录警告但继续处理
            if period_estimate > effective_limit {
                // 先保存当前批次（如果不为空）
                if !current_batch.is_empty() {
                    batches.push(current_batch.clone());
                    current_batch.clear();
                    current_batch_estimate = 0;
                }
                
                // 将大时间段单独作为一批，并记录警告
                log::warn!("⚠️  时间段 {} ({} 到 {}) 预估 {} 条数据，仍超过API限制，但将尝试处理", 
                    index + 1, start_time, end_time, period_estimate);
                batches.push(vec![index]);
                continue;
            }
            
            // 检查加入当前时间段后是否会超过限制
            if current_batch_estimate + period_estimate > effective_limit && !current_batch.is_empty() {
                // 保存当前批次，开始新批次
                batches.push(current_batch.clone());
                current_batch.clear();
                current_batch_estimate = 0;
            }
            
            // 将时间段加入当前批次
            current_batch.push(index);
            current_batch_estimate += period_estimate;
        }
        
        // 保存最后一个批次
        if !current_batch.is_empty() {
            batches.push(current_batch);
        }
        
        // 输出分批统计信息
        log::info!("📊 最终分批结果:");
        for (i, batch) in batches.iter().enumerate() {
            let batch_estimate = batch.iter()
                .map(|&index| {
                    let (start_time, end_time) = processed_periods[index];
                    self.estimate_period_klines(interval, start_time, end_time)
                })
                .sum::<i64>();
            let period_ranges: Vec<String> = batch.iter()
                .map(|&index| format!("{}", index + 1))
                .collect();
            log::info!("   批次 {}: 时间段[{}], 预估 {} 条K线", 
                i + 1, period_ranges.join(","), batch_estimate);
        }
        
        batches
    }

    /// 预处理超大时间段，将其分割为更小的可管理片段
    fn preprocess_large_periods(
        &self,
        interval: &KlineInterval,
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
        limit: i64,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        let mut processed_periods = Vec::new();
        
        for &(start_time, end_time) in missing_periods {
            let period_estimate = self.estimate_period_klines(interval, start_time, end_time);
            
            if period_estimate > limit {
                // 计算需要分割成多少段
                let segments_needed = ((period_estimate as f64 / limit as f64).ceil() as usize).max(1);
                let total_duration = end_time - start_time;
                let segment_duration = Duration::milliseconds(total_duration.num_milliseconds() / segments_needed as i64);
                
                log::debug!("分割超大时间段: {} 到 {} (预估{}条) → {} 段", 
                    start_time, end_time, period_estimate, segments_needed);
                
                let mut current_start = start_time;
                for i in 0..segments_needed {
                    let current_end = if i == segments_needed - 1 {
                        end_time // 最后一段使用原始结束时间
                    } else {
                        current_start + segment_duration
                    };
                    
                    processed_periods.push((current_start, current_end));
                    current_start = current_end;
                }
            } else {
                processed_periods.push((start_time, end_time));
            }
        }
        
        log::debug!("预处理完成: {} 个原始时间段 → {} 个处理后时间段", 
            missing_periods.len(), processed_periods.len());
        
        processed_periods
    }

    /// 估算单个时间段的K线数据量
    fn estimate_period_klines(
        &self,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> i64 {
        let duration = end_time - start_time;
        let interval_minutes = interval.to_minutes();
        let total_minutes = duration.num_minutes();
        
        // 计算理论上的K线数量
        let estimated_klines = total_minutes / interval_minutes;
        
        // 考虑实际情况，加上一些容错
        std::cmp::max(1, estimated_klines)
    }

    /// 估算批次的总K线数据量
    fn estimate_batch_klines(
        &self,
        interval: &KlineInterval,
        batch: &[usize],
        missing_periods: &[(DateTime<Utc>, DateTime<Utc>)],
    ) -> i64 {
        batch.iter()
            .map(|&index| {
                let (start_time, end_time) = missing_periods[index];
                self.estimate_period_klines(interval, start_time, end_time)
            })
            .sum()
    }

    /// 从Binance API获取K线数据（增强版本，带重试机制）
    async fn fetch_klines_from_binance(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<KlineDto>> {
        let interval_str = interval.to_binance_string();
        
        // 计算时间范围，确保不超过API限制
        let duration_hours = (end_time - start_time).num_hours();
        if duration_hours > 24 {
            log::warn!("时间范围过大 ({} 小时)，可能导致API调用失败", duration_hours);
        }
        
        // 调用Binance API
        let query = KlineQuery {
            symbol: symbol.to_string(),
            interval: interval_str.to_string(),
            start_time: Some(start_time.timestamp_millis()),
            end_time: Some(end_time.timestamp_millis()),
            limit: Some(1000), // Binance API限制
        };
        
        // 重试机制
        let mut retries = 0;
        const MAX_RETRIES: u32 = 3;
        
        loop {
            match self.binance_client.get_klines(query.clone()).await {
                Ok(klines) => {
                    log::debug!("成功获取 {} 条K线数据 ({})", klines.len(), symbol);
                    
                    // 转换为KlineDto
                    let kline_dtos: Vec<KlineDto> = klines
                        .into_iter()
                        .filter_map(|kline| {
                            // 增强的错误处理，避免解析失败导致panic
                            match self.convert_universal_kline_to_dto(kline, symbol, &interval_str) {
                                Ok(dto) => Some(dto),
                                Err(e) => {
                                    log::warn!("转换K线数据失败: {}", e);
                                    None
                                }
                            }
                        })
                        .collect();

                    return Ok(kline_dtos);
                }
                Err(e) => {
                    retries += 1;
                    if retries <= MAX_RETRIES {
                        log::warn!("Binance API调用失败 (重试 {}/{}): {}", retries, MAX_RETRIES, e);
                        tokio::time::sleep(tokio::time::Duration::from_millis(1000 * retries as u64)).await;
                    } else {
                        return Err(MarketDataServiceError::Api(format!("Binance API错误 (重试{}次后失败): {}", MAX_RETRIES, e)));
                    }
                }
            }
        }
    }

    /// 转换Universal K线数据到DTO（增强错误处理）
    fn convert_universal_kline_to_dto(
        &self,
        kline: exchange::UniversalKline,
        symbol: &str,
        interval_str: &str,
    ) -> Result<KlineDto> {
        let open_time = DateTime::from_timestamp_millis(kline.open_time)
            .ok_or_else(|| MarketDataServiceError::DataConversion("无效的开盘时间".to_string()))?;
        let close_time = DateTime::from_timestamp_millis(kline.close_time)
            .ok_or_else(|| MarketDataServiceError::DataConversion("无效的收盘时间".to_string()))?;

        Ok(KlineDto {
            time: open_time,
            symbol: symbol.to_string(),
            interval: interval_str.to_string(),
            open_time,
            close_time,
            open_price: kline.open_price.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("开盘价解析失败: {}", e)))?,
            high_price: kline.high_price.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("最高价解析失败: {}", e)))?,
            low_price: kline.low_price.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("最低价解析失败: {}", e)))?,
            close_price: kline.close_price.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("收盘价解析失败: {}", e)))?,
            volume: kline.volume.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("成交量解析失败: {}", e)))?,
            quote_asset_volume: kline.quote_asset_volume.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("报价资产成交量解析失败: {}", e)))?,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("主动买入基础资产成交量解析失败: {}", e)))?,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse()
                .map_err(|e| MarketDataServiceError::DataConversion(format!("主动买入报价资产成交量解析失败: {}", e)))?,
        })
    }

    /// 将KlineDto转换为CreateMarketDataRequest
    fn kline_dto_to_request(&self, kline: KlineDto, interval: &KlineInterval) -> CreateMarketDataRequest {
        CreateMarketDataRequest {
            symbol: kline.symbol,
            timestamp: kline.time,
            open: kline.open_price,
            high: kline.high_price,
            low: kline.low_price,
            close: kline.close_price,
            volume: kline.volume,
            interval_type: interval.to_binance_string().to_string(),
            quote_asset_volume: kline.quote_asset_volume,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            close_time: kline.close_time,
        }
    }

    /// 获取完整性检查统计信息
    pub async fn get_integrity_statistics(&self) -> Result<HashMap<String, f64>> {
        let mut stats = HashMap::new();
        
        // 这里可以添加更多统计信息的计算
        // 例如：总体完整性、各交易对完整性等
        
        stats.insert("total_symbols_checked".to_string(), 0.0);
        stats.insert("average_completeness".to_string(), 0.0);
        
        Ok(stats)
    }
} 