use serde::{Deserialize, Serialize};
use std::time::Duration;
use std::collections::HashMap;
use anyhow::Result;
use tracing::{info, warn, error};

/// 增强的市场数据维护服务配置
/// 
/// 支持多环境配置、动态配置更新、配置验证等功能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedMaintenanceConfig {
    /// 环境标识
    pub environment: Environment,
    
    /// 服务配置
    pub service: ServiceConfig,
    
    /// 数据维护配置
    pub maintenance: MaintenanceConfig,
    
    /// 缓存同步配置
    pub cache_sync: CacheSyncConfig,
    
    /// 缺口检测配置
    pub gap_detection: GapDetectionConfig,
    
    /// 交易所配置
    pub exchanges: HashMap<String, ExchangeConfig>,
    
    /// 监控和告警配置
    pub monitoring: MonitoringConfig,
    
    /// 性能调优配置
    pub performance: PerformanceConfig,
}

/// 环境类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Environment {
    Development,
    Testing,
    Staging,
    Production,
}

/// 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// 服务名称
    pub name: String,
    /// 服务版本
    pub version: String,
    /// 启动模式
    pub startup_mode: StartupMode,
    /// 优雅关闭超时时间（秒）
    pub graceful_shutdown_timeout_seconds: u64,
    /// 健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
}

/// 启动模式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StartupMode {
    /// 快速启动（跳过历史数据检查）
    Fast,
    /// 标准启动（检查最近数据）
    Standard,
    /// 完整启动（全面数据检查）
    Full,
}

/// 维护配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceConfig {
    /// 全量扫描间隔（小时）
    pub full_scan_interval_hours: u64,
    /// 增量扫描间隔（分钟）
    pub incremental_scan_interval_minutes: u64,
    /// 批量处理大小
    pub batch_size: usize,
    /// 并发处理数
    pub max_concurrent_symbols: usize,
    /// 数据保留天数
    pub data_retention_days: i64,
    /// 是否启用自动修复
    pub enable_auto_repair: bool,
    /// 修复重试次数
    pub repair_retry_count: u32,
}

/// 缓存同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheSyncConfig {
    /// 同步间隔（秒）
    pub sync_interval_seconds: u64,
    /// 预热缓存天数
    pub warmup_days: u32,
    /// 是否启用智能预热
    pub enable_smart_warmup: bool,
    /// 缓存TTL策略
    pub ttl_strategy: TtlStrategy,
    /// 最大缓存大小（MB）
    pub max_cache_size_mb: u64,
}

/// TTL策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TtlStrategy {
    /// 固定TTL
    Fixed { hours: u64 },
    /// 动态TTL（基于数据新旧程度）
    Dynamic { base_hours: u64, decay_factor: f64 },
    /// 智能TTL（基于访问频率）
    Smart { min_hours: u64, max_hours: u64 },
}

/// 缺口检测配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GapDetectionConfig {
    /// 检测的时间间隔
    pub intervals: Vec<String>,
    /// 批量检测窗口大小（天）
    pub batch_window_days: i64,
    /// 是否启用智能采样
    pub enable_smart_sampling: bool,
    /// 采样率
    pub sampling_rate: f64,
    /// 严重缺口阈值
    pub severe_gap_threshold: f64,
}

/// 交易所配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExchangeConfig {
    /// 交易所名称
    pub name: String,
    /// API基础URL
    pub base_url: String,
    /// 请求超时时间（秒）
    pub timeout_seconds: u64,
    /// 速率限制（请求/分钟）
    pub rate_limit_per_minute: u32,
    /// 重试配置
    pub retry: RetryConfig,
    /// 是否启用
    pub enabled: bool,
}

/// 重试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// 最大重试次数
    pub max_attempts: u32,
    /// 基础延迟（毫秒）
    pub base_delay_ms: u64,
    /// 最大延迟（毫秒）
    pub max_delay_ms: u64,
    /// 退避策略
    pub backoff_strategy: BackoffStrategy,
}

/// 退避策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BackoffStrategy {
    /// 固定延迟
    Fixed,
    /// 线性退避
    Linear,
    /// 指数退避
    Exponential,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用指标收集
    pub enable_metrics: bool,
    /// 指标收集间隔（秒）
    pub metrics_interval_seconds: u64,
    /// 告警配置
    pub alerts: AlertConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 告警配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    /// 是否启用告警
    pub enabled: bool,
    /// 缺口检测告警阈值
    pub gap_detection_threshold: u32,
    /// 同步延迟告警阈值（分钟）
    pub sync_delay_threshold_minutes: u64,
    /// 错误率告警阈值
    pub error_rate_threshold: f64,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 是否启用结构化日志
    pub structured: bool,
    /// 日志轮转大小（MB）
    pub rotation_size_mb: u64,
    /// 日志保留天数
    pub retention_days: u32,
}

/// 性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 数据库连接池大小
    pub db_pool_size: u32,
    /// 缓存连接池大小
    pub cache_pool_size: u32,
    /// 工作线程数
    pub worker_threads: Option<usize>,
    /// 内存限制（MB）
    pub memory_limit_mb: Option<u64>,
    /// 是否启用压缩
    pub enable_compression: bool,
}

impl Default for EnhancedMaintenanceConfig {
    fn default() -> Self {
        Self {
            environment: Environment::Development,
            service: ServiceConfig::default(),
            maintenance: MaintenanceConfig::default(),
            cache_sync: CacheSyncConfig::default(),
            gap_detection: GapDetectionConfig::default(),
            exchanges: Self::default_exchanges(),
            monitoring: MonitoringConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            name: "market-data-maintenance".to_string(),
            version: "1.0.0".to_string(),
            startup_mode: StartupMode::Standard,
            graceful_shutdown_timeout_seconds: 30,
            health_check_interval_seconds: 60,
        }
    }
}

impl Default for MaintenanceConfig {
    fn default() -> Self {
        Self {
            full_scan_interval_hours: 24,
            incremental_scan_interval_minutes: 5,
            batch_size: 1000,
            max_concurrent_symbols: 10,
            data_retention_days: 365,
            enable_auto_repair: true,
            repair_retry_count: 3,
        }
    }
}

impl Default for CacheSyncConfig {
    fn default() -> Self {
        Self {
            sync_interval_seconds: 60,
            warmup_days: 7,
            enable_smart_warmup: true,
            ttl_strategy: TtlStrategy::Dynamic { 
                base_hours: 24, 
                decay_factor: 0.1 
            },
            max_cache_size_mb: 1024,
        }
    }
}

impl Default for GapDetectionConfig {
    fn default() -> Self {
        Self {
            intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
            batch_window_days: 7,
            enable_smart_sampling: true,
            sampling_rate: 0.1,
            severe_gap_threshold: 0.8,
        }
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_metrics: true,
            metrics_interval_seconds: 60,
            alerts: AlertConfig::default(),
            logging: LoggingConfig::default(),
        }
    }
}

impl Default for AlertConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            gap_detection_threshold: 10,
            sync_delay_threshold_minutes: 10,
            error_rate_threshold: 0.05,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            structured: true,
            rotation_size_mb: 100,
            retention_days: 30,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            db_pool_size: 20,
            cache_pool_size: 10,
            worker_threads: None,
            memory_limit_mb: None,
            enable_compression: true,
        }
    }
}

impl EnhancedMaintenanceConfig {
    /// 从环境变量和配置文件加载配置
    pub fn from_env_and_file(config_file: Option<&str>) -> Result<Self> {
        let mut config = Self::default();
        
        // 1. 首先从环境变量加载基础配置
        config.load_from_env()?;
        
        // 2. 如果提供了配置文件，从文件加载配置（覆盖环境变量）
        if let Some(file_path) = config_file {
            config.load_from_file(file_path)?;
        }
        
        // 3. 根据环境调整配置
        config.adjust_for_environment();
        
        // 4. 验证配置
        config.validate()?;
        
        info!("✅ 配置加载完成: 环境={:?}, 启动模式={:?}", 
              config.environment, config.service.startup_mode);
        
        Ok(config)
    }

    /// 从环境变量加载配置
    fn load_from_env(&mut self) -> Result<()> {
        // 环境配置
        if let Ok(env) = std::env::var("ENVIRONMENT") {
            self.environment = match env.to_lowercase().as_str() {
                "dev" | "development" => Environment::Development,
                "test" | "testing" => Environment::Testing,
                "staging" => Environment::Staging,
                "prod" | "production" => Environment::Production,
                _ => Environment::Development,
            };
        }

        // 服务配置
        if let Ok(name) = std::env::var("SERVICE_NAME") {
            self.service.name = name;
        }
        
        if let Ok(mode) = std::env::var("STARTUP_MODE") {
            self.service.startup_mode = match mode.to_lowercase().as_str() {
                "fast" => StartupMode::Fast,
                "standard" => StartupMode::Standard,
                "full" => StartupMode::Full,
                _ => StartupMode::Standard,
            };
        }

        // 维护配置
        if let Ok(interval) = std::env::var("FULL_SCAN_INTERVAL_HOURS") {
            self.maintenance.full_scan_interval_hours = interval.parse().unwrap_or(24);
        }
        
        if let Ok(interval) = std::env::var("INCREMENTAL_SCAN_INTERVAL_MINUTES") {
            self.maintenance.incremental_scan_interval_minutes = interval.parse().unwrap_or(5);
        }

        // 缓存同步配置
        if let Ok(interval) = std::env::var("CACHE_SYNC_INTERVAL_SECONDS") {
            self.cache_sync.sync_interval_seconds = interval.parse().unwrap_or(60);
        }

        Ok(())
    }

    /// 从配置文件加载配置
    fn load_from_file(&mut self, file_path: &str) -> Result<()> {
        let content = std::fs::read_to_string(file_path)?;
        
        let file_config: EnhancedMaintenanceConfig = if file_path.ends_with(".toml") {
            toml::from_str(&content)?
        } else if file_path.ends_with(".yaml") || file_path.ends_with(".yml") {
            serde_yaml::from_str(&content)?
        } else {
            serde_json::from_str(&content)?
        };
        
        // 合并配置（文件配置覆盖环境变量配置）
        *self = file_config;
        
        Ok(())
    }

    /// 根据环境调整配置
    pub fn adjust_for_environment(&mut self) {
        match self.environment {
            Environment::Development => {
                self.maintenance.full_scan_interval_hours = 1; // 开发环境更频繁的扫描
                self.gap_detection.sampling_rate = 0.5; // 更高的采样率
                self.monitoring.logging.level = "debug".to_string();
            }
            Environment::Testing => {
                self.maintenance.enable_auto_repair = false; // 测试环境不自动修复
                self.monitoring.alerts.enabled = false; // 测试环境不发送告警
            }
            Environment::Production => {
                self.gap_detection.sampling_rate = 0.05; // 生产环境较低采样率
                self.performance.enable_compression = true; // 生产环境启用压缩
                self.monitoring.logging.level = "info".to_string();
            }
            Environment::Staging => {
                // 预发布环境使用接近生产的配置
                self.gap_detection.sampling_rate = 0.1;
                self.monitoring.alerts.enabled = false;
            }
        }
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        if self.maintenance.full_scan_interval_hours == 0 {
            return Err(anyhow::anyhow!("全量扫描间隔不能为0"));
        }

        if self.maintenance.incremental_scan_interval_minutes == 0 {
            return Err(anyhow::anyhow!("增量扫描间隔不能为0"));
        }

        if self.gap_detection.sampling_rate <= 0.0 || self.gap_detection.sampling_rate > 1.0 {
            return Err(anyhow::anyhow!("采样率必须在0到1之间"));
        }

        if self.maintenance.max_concurrent_symbols == 0 {
            return Err(anyhow::anyhow!("最大并发符号数不能为0"));
        }

        Ok(())
    }

    /// 获取默认交易所配置
    fn default_exchanges() -> HashMap<String, ExchangeConfig> {
        let mut exchanges = HashMap::new();
        
        exchanges.insert("binance".to_string(), ExchangeConfig {
            name: "Binance".to_string(),
            base_url: "https://api.binance.com".to_string(),
            timeout_seconds: 30,
            rate_limit_per_minute: 1200,
            retry: RetryConfig {
                max_attempts: 3,
                base_delay_ms: 1000,
                max_delay_ms: 10000,
                backoff_strategy: BackoffStrategy::Exponential,
            },
            enabled: true,
        });
        
        exchanges
    }

    /// 获取当前环境的配置摘要
    pub fn get_config_summary(&self) -> String {
        format!(
            "Environment: {:?}, Startup: {:?}, Full Scan: {}h, Incremental: {}m, Cache Sync: {}s",
            self.environment,
            self.service.startup_mode,
            self.maintenance.full_scan_interval_hours,
            self.maintenance.incremental_scan_interval_minutes,
            self.cache_sync.sync_interval_seconds
        )
    }

    /// 导出配置到文件
    pub fn export_to_file(&self, file_path: &str) -> Result<()> {
        let content = if file_path.ends_with(".toml") {
            toml::to_string_pretty(self)?
        } else if file_path.ends_with(".yaml") || file_path.ends_with(".yml") {
            serde_yaml::to_string(self)?
        } else {
            serde_json::to_string_pretty(self)?
        };

        std::fs::write(file_path, content)?;
        info!("配置已导出到: {}", file_path);
        Ok(())
    }

    /// 热重载配置
    pub fn hot_reload(&mut self, file_path: &str) -> Result<()> {
        info!("开始热重载配置: {}", file_path);

        let old_summary = self.get_config_summary();
        self.load_from_file(file_path)?;
        self.adjust_for_environment();
        self.validate()?;

        let new_summary = self.get_config_summary();
        if old_summary != new_summary {
            info!("配置已更新: {} -> {}", old_summary, new_summary);
        } else {
            info!("配置无变化");
        }

        Ok(())
    }
}

/// 配置管理器
///
/// 提供配置的加载、验证、热重载等功能
pub struct ConfigManager {
    config: EnhancedMaintenanceConfig,
    config_file_path: Option<String>,
    last_modified: Option<std::time::SystemTime>,
}

impl ConfigManager {
    /// 创建配置管理器
    pub fn new(config_file: Option<&str>) -> Result<Self> {
        let config = EnhancedMaintenanceConfig::from_env_and_file(config_file)?;
        let config_file_path = config_file.map(|s| s.to_string());

        let last_modified = if let Some(ref path) = config_file_path {
            std::fs::metadata(path).ok().and_then(|m| m.modified().ok())
        } else {
            None
        };

        Ok(Self {
            config,
            config_file_path,
            last_modified,
        })
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &EnhancedMaintenanceConfig {
        &self.config
    }

    /// 检查配置文件是否有更新并自动重载
    pub fn check_and_reload(&mut self) -> Result<bool> {
        if let Some(ref path) = self.config_file_path {
            if let Ok(metadata) = std::fs::metadata(path) {
                if let Ok(modified) = metadata.modified() {
                    if self.last_modified.map_or(true, |last| modified > last) {
                        info!("检测到配置文件更新，开始重载: {}", path);
                        self.config.hot_reload(path)?;
                        self.last_modified = Some(modified);
                        return Ok(true);
                    }
                }
            }
        }
        Ok(false)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        self.config.validate()
    }

    /// 获取配置摘要
    pub fn get_summary(&self) -> String {
        self.config.get_config_summary()
    }

    /// 导出当前配置
    pub fn export_config(&self, file_path: &str) -> Result<()> {
        self.config.export_to_file(file_path)
    }
}
