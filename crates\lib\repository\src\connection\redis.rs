use std::sync::Arc;
use r2d2::{Pool, PooledConnection};
use r2d2_redis::RedisConnectionManager;
use r2d2_redis::redis::{Commands, FromRedisValue, RedisError, RedisResult, ToRedisArgs};
use std::time::Duration;
use crate::{Result, RepositoryError};
use common::config::redis_config::RedisConfig;
use super::PoolStatus;
use log::{debug, info, warn, error};

/// Redis连接池管理器
#[derive(Clone)]
pub struct RedisPoolManager {
    pool: Pool<RedisConnectionManager>,
    config: RedisConfig,
}

impl RedisPoolManager {
    /// 创建新的Redis连接池管理器
    pub async fn new(config: RedisConfig) -> Result<Self> {
        // 构建Redis连接URL
        let url = match config.password.is_empty() {
            true => format!("redis://{}:{}/", config.host, config.port),
            false => format!("redis://:{}@{}:{}/", config.password, config.host, config.port),
        };

        // 创建Redis连接管理器
        let manager = RedisConnectionManager::new(url)
            .map_err(|e| RepositoryError::Connection(format!("Failed to create Redis connection manager: {}", e)))?;

        // 构建连接池
        let pool = Pool::builder()
            .max_size(config.max_connections)
            .min_idle(Some(config.min_connections))
            .connection_timeout(Duration::from_secs(config.connect_timeout))
            .idle_timeout(Some(Duration::from_secs(config.idle_timeout)))
            .build(manager)
            .map_err(|e| RepositoryError::Connection(format!("Failed to create Redis pool: {}", e)))?;

        Ok(Self { pool, config })
    }

    /// 获取连接池引用
    pub fn pool(&self) -> &Pool<RedisConnectionManager> {
        &self.pool
    }

    /// 获取一个连接
    pub fn get_connection(&self) -> Result<PooledConnection<RedisConnectionManager>> {
        self.pool.get()
            .map_err(|e| RepositoryError::Connection(format!("Failed to get Redis connection: {}", e)))
    }

    /// 获取连接池状态
    pub fn status(&self) -> PoolStatus {
        let state = self.pool.state();
        PoolStatus {
            active_connections: state.connections,
            idle_connections: state.idle_connections,
            total_connections: state.connections,
            is_healthy: state.connections > 0,
        }
    }

    /// 测试连接是否正常
    pub async fn test_connection(&self) -> Result<()> {
        let mut conn = self.get_connection()?;
        // 使用简单的GET命令测试连接
        let _: Option<String> = conn.get("__test_connection__")
            .map_err(|e| RepositoryError::Connection(format!("Redis connection test failed: {}", e)))?;
        Ok(())
    }

    /// 关闭连接池
    pub fn close(&self) {
        // r2d2连接池会在Drop时自动关闭
    }

    /// 获取配置信息
    pub fn config(&self) -> &RedisConfig {
        &self.config
    }
}

/// Redis操作工具类
#[derive(Clone)]
pub struct RedisOperations {
    pool_manager: RedisPoolManager,
}

impl RedisOperations {
    pub fn new(pool_manager: RedisPoolManager) -> Self {
        Self { pool_manager }
    }

    /// 获取连接
    fn get_connection(&self) -> Result<PooledConnection<RedisConnectionManager>> {
        self.pool_manager.get_connection()
    }

    /// Sets a key with an expiration time in seconds.
    pub fn set_ex<K, V>(&self, key: K, value: V, seconds: usize) -> Result<()>
    where
        K: ToRedisArgs + Send + Sync,
        V: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        let _: () = conn.set_ex(key, value, seconds)?;
        Ok(())
    }

    /// Sets a key without expiration.
    pub fn set<K, V>(&self, key: K, value: V) -> Result<()>
    where
        K: ToRedisArgs + Send + Sync,
        V: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        let _: () = conn.set(key, value)?;
        Ok(())
    }

    /// Gets a value by key.
    pub fn get<T, K>(&self, key: K) -> Result<T>
    where
        T: FromRedisValue + Send + Sync,
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.get(key)?)
    }

    /// Deletes a key.
    pub fn del<K>(&self, key: K) -> Result<()>
    where
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        let _: () = conn.del(key)?;
        Ok(())
    }

    /// 检测指定key是否存在
    pub fn exists<K>(&self, key: K) -> Result<bool>
    where
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.exists(key)?)
    }

    /// 获取key的剩余过期时间（秒）
    pub fn ttl<K>(&self, key: K) -> Result<i32>
    where
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.ttl(key)?)
    }

    /// 为key设置过期时间（秒）
    pub fn expire<K>(&self, key: K, seconds: usize) -> Result<bool>
    where
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.expire(key, seconds)?)
    }

    /// Pushes a value to the left end of a list.
    pub fn lpush<K, V>(&self, key: K, value: V) -> Result<()>
    where
        K: ToRedisArgs + Send + Sync,
        V: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        let _: () = conn.lpush(key, value)?;
        Ok(())
    }

    /// Retrieves a range of elements from a list.
    pub fn lrange<K, T>(&self, key: K, start: isize, stop: isize) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.lrange(key, start, stop)?)
    }

    /// 获取Redis List中指定索引的元素
    pub fn lindex<K, T>(&self, key: K, index: isize) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.lindex(key, index)?)
    }

    /// 获取匹配模式的所有key
    pub fn keys<P, T>(&self, pattern: P) -> Result<T>
    where
        P: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.keys(pattern)?)
    }

    /// 批量获取多个key的值
    pub fn mget<K, T>(&self, keys: K) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.get(keys)?)
    }

    /// 获取hash中所有fields和values
    pub fn hgetall<K, T>(&self, key: K) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.hgetall(key)?)
    }

    /// Sets a field-value pair in a hash.
    pub fn hset<K, F, V, T>(&self, key: K, field: F, value: V) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        F: ToRedisArgs + Send + Sync,
        V: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.hset(key, field, value)?)
    }

    /// Gets a value from a hash by field.
    pub fn hget<K, F, T>(&self, key: K, field: F) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        F: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.hget(key, field)?)
    }

    /// 向Sorted Set添加成员
    pub fn zadd<K, S, M>(&self, key: K, member: M, score: S) -> Result<i64>
    where
        K: ToRedisArgs + Send + Sync,
        S: ToRedisArgs + Send + Sync,
        M: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.zadd(key, member, score)?)
    }

    /// 根据分数范围获取Sorted Set成员
    pub fn zrangebyscore<K, T>(&self, key: K, min: f64, max: f64) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.zrangebyscore(key, min, max)?)
    }

    /// 根据索引范围获取Sorted Set成员
    pub fn zrange<K, T>(&self, key: K, start: isize, stop: isize) -> Result<T>
    where
        K: ToRedisArgs + Send + Sync,
        T: FromRedisValue + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.zrange(key, start, stop)?)
    }

    /// 执行PING命令测试连接
    pub fn ping(&self) -> Result<String>
    {
        let mut conn = self.get_connection()?;
        let result: String = conn.get("__ping__").unwrap_or_else(|_| "PONG".to_string());
        Ok(result)
    }

    /// 批量向Sorted Set添加成员
    pub fn zadd_multiple<K>(&self, key: K, items: &[(f64, String)]) -> Result<i64>
    where
        K: ToRedisArgs + Send + Sync + Clone,
    {
        let mut conn = self.get_connection()?;
        let mut total_added = 0i64;
        
        for (score, member) in items {
            let added: i64 = conn.zadd(key.clone(), member, *score)?;
            total_added += added;
        }
        
        Ok(total_added)
    }

    /// 获取Sorted Set的大小
    pub fn zcard<K>(&self, key: K) -> Result<i64>
    where
        K: ToRedisArgs + Send + Sync,
    {
        let mut conn = self.get_connection()?;
        Ok(conn.zcard(key)?)
    }
} 