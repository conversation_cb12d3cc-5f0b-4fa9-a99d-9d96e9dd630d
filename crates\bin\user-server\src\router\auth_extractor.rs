use axum::{
    http::StatusCode,
    response::{IntoResponse, Response, Json},
    Extension,
};
use common::domain::dto::{Clai<PERSON>, ApiErrorResponse};

/// 权限检查结果
pub struct PermissionCheck {
    pub claims: Claims,
}

impl PermissionCheck {
    /// 检查是否拥有指定权限
    pub fn require_permission(claims: &Claims, permission: &str) -> Result<Self, Response> {
        if claims.permissions.contains(&permission.to_string()) {
            Ok(PermissionCheck {
                claims: claims.clone(),
            })
        } else {
            let error_response = (
                StatusCode::FORBIDDEN,
                Json(ApiErrorResponse::error(format!("权限不足：需要 '{}' 权限", permission)))
            ).into_response();
            Err(error_response)
        }
    }

    /// 检查是否拥有指定角色
    pub fn require_role(claims: &Claims, role: &str) -> Result<Self, Response> {
        if claims.roles.contains(&role.to_string()) {
            Ok(PermissionCheck {
                claims: claims.clone(),
            })
        } else {
            let error_response = (
                StatusCode::FOR<PERSON><PERSON><PERSON><PERSON>,
                <PERSON><PERSON>(ApiErrorResponse::error(format!("权限不足：需要 '{}' 角色", role)))
            ).into_response();
            Err(error_response)
        }
    }

    /// 检查是否拥有多个权限
    pub fn require_multiple_permissions(claims: &Claims, permissions: &[&str]) -> Result<Self, Response> {
        let missing_permissions: Vec<&str> = permissions.iter()
            .filter(|&&perm| !claims.permissions.contains(&perm.to_string()))
            .copied()
            .collect();

        if missing_permissions.is_empty() {
            Ok(PermissionCheck {
                claims: claims.clone(),
            })
        } else {
            let error_response = (
                StatusCode::FORBIDDEN,
                Json(ApiErrorResponse::error(format!("权限不足：需要权限 {:?}", missing_permissions)))
            ).into_response();
            Err(error_response)
        }
    }

    /// 检查是否拥有任意一个权限
    pub fn require_any_permission(claims: &Claims, permissions: &[&str]) -> Result<Self, Response> {
        let has_any_permission = permissions.iter()
            .any(|&perm| claims.permissions.contains(&perm.to_string()));

        if has_any_permission {
            Ok(PermissionCheck {
                claims: claims.clone(),
            })
        } else {
            let error_response = (
                StatusCode::FORBIDDEN,
                Json(ApiErrorResponse::error(format!("权限不足：需要以下权限之一 {:?}", permissions)))
            ).into_response();
            Err(error_response)
        }
    }
}

/// 便利函数：权限检查
pub fn check_permission(claims: &Claims, required_permission: &str) -> bool {
    claims.permissions.contains(&required_permission.to_string())
}

/// 便利函数：角色检查
pub fn check_role(claims: &Claims, required_role: &str) -> bool {
    claims.roles.contains(&required_role.to_string())
}

/// 便利函数：多权限检查
pub fn check_multiple_permissions(claims: &Claims, permissions: &[&str]) -> bool {
    permissions.iter().all(|&perm| claims.permissions.contains(&perm.to_string()))
}

/// 便利函数：任意权限检查
pub fn check_any_permission(claims: &Claims, permissions: &[&str]) -> bool {
    permissions.iter().any(|&perm| claims.permissions.contains(&perm.to_string()))
}

/// 创建统一的权限不足响应
pub fn permission_denied_response(message: &str) -> Response {
    (
        StatusCode::FORBIDDEN,
        Json(ApiErrorResponse::error(message.to_string()))
    ).into_response()
}

/// 创建统一的未认证响应
pub fn unauthorized_response(message: &str) -> Response {
    (
        StatusCode::UNAUTHORIZED,
        Json(ApiErrorResponse::error(message.to_string()))
    ).into_response()
} 