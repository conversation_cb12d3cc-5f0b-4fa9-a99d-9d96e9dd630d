use serde::{Deserialize, Serialize};
use std::time::Duration;

/// 市场数据维护服务配置 - 简化版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceConfig {
    /// 全量扫描间隔（小时）
    pub full_scan_interval_hours: u64,
    /// 增量扫描间隔（分钟）
    pub incremental_scan_interval_minutes: u64,
    /// Binance API基础URL
    pub binance_base_url: String,
    /// API请求超时时间（秒）
    pub request_timeout_seconds: u64,
    /// 批量插入大小
    pub batch_insert_size: usize,
    /// 数据清理阈值（天）
    pub cleanup_threshold_days: i64,
}

impl Default for MaintenanceConfig {
    fn default() -> Self {
        Self {
            full_scan_interval_hours: 24,         // 每天一次全量扫描
            incremental_scan_interval_minutes: 5, // 每5分钟一次增量扫描
            binance_base_url: "https://api.binance.com".to_string(),
            request_timeout_seconds: 30,
            batch_insert_size: 1000,
            cleanup_threshold_days: 365,
        }
    }
}

impl MaintenanceConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Self {
        Self::default()
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), String> {
        if self.full_scan_interval_hours == 0 {
            return Err("全量扫描间隔不能为0".to_string());
        }

        if self.incremental_scan_interval_minutes == 0 {
            return Err("增量扫描间隔不能为0".to_string());
        }

        if self.batch_insert_size == 0 {
            return Err("批量插入大小不能为0".to_string());
        }

        Ok(())
    }

    /// 获取全量扫描间隔
    pub fn full_scan_interval(&self) -> Duration {
        Duration::from_secs(self.full_scan_interval_hours * 3600)
    }

    /// 获取增量扫描间隔
    pub fn incremental_scan_interval(&self) -> Duration {
        Duration::from_secs(self.incremental_scan_interval_minutes * 60)
    }

    /// 获取请求超时时间
    pub fn request_timeout(&self) -> Duration {
        Duration::from_secs(self.request_timeout_seconds)
    }
} 