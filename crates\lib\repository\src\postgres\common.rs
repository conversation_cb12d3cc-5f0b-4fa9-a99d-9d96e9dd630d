use sqlx::{Pg<PERSON><PERSON>, Query<PERSON>uilder, Postgres};
use crate::{Result, RepositoryError};
use serde::{Serialize, Deserialize};

/// 通用的分页参数
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub page_size: u32,
}

impl Default for Pagination {
    fn default() -> Self {
        Self { page: 1, page_size: 20 }
    }
}

impl Pagination {
    pub fn offset(&self) -> u32 {
        (self.page - 1) * self.page_size
    }
    
    pub fn limit(&self) -> u32 {
        self.page_size
    }
}

/// 分页查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PagedResult<T> {
    pub data: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl<T> PagedResult<T> {
    pub fn new(data: Vec<T>, total: u64, pagination: &Pagination) -> Self {
        let total_pages = (total as f64 / pagination.page_size as f64).ceil() as u32;
        Self {
            data,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
            total_pages,
        }
    }
}

/// 通用的PostgreSQL操作帮助函数
pub struct PostgresHelper;

impl PostgresHelper {
    /// 执行带分页的查询
    pub async fn paginated_query<T, F>(
        pool: &PgPool,
        query_builder_fn: F,
        pagination: &Pagination,
    ) -> Result<PagedResult<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
        F: Fn() -> QueryBuilder<'static, Postgres>,
    {
        // 先查询总数
        let mut count_builder = query_builder_fn();
        let count_sql = format!("SELECT COUNT(*) FROM ({})", count_builder.sql());
        let total: (i64,) = sqlx::query_as(&count_sql)
            .fetch_one(pool)
            .await?;

        // 查询数据
        let mut data_builder = query_builder_fn();
        data_builder.push(" LIMIT ").push_bind(pagination.limit() as i64);
        data_builder.push(" OFFSET ").push_bind(pagination.offset() as i64);
        
        let data: Vec<T> = data_builder
            .build_query_as()
            .fetch_all(pool)
            .await?;

        Ok(PagedResult::new(data, total.0 as u64, pagination))
    }

    /// 检查记录是否存在
    pub async fn exists(pool: &PgPool, table: &str, condition: &str, value: &str) -> Result<bool> {
        let query = format!("SELECT EXISTS(SELECT 1 FROM {} WHERE {} = $1)", table, condition);
        let result: (bool,) = sqlx::query_as(&query)
            .bind(value)
            .fetch_one(pool)
            .await?;
        Ok(result.0)
    }

    /// 软删除记录
    pub async fn soft_delete(
        pool: &PgPool,
        table: &str,
        id_column: &str,
        id_value: &str,
    ) -> Result<u64> {
        let query = format!(
            "UPDATE {} SET deleted_at = NOW() WHERE {} = $1 AND deleted_at IS NULL",
            table, id_column
        );
        let result = sqlx::query(&query)
            .bind(id_value)
            .execute(pool)
            .await?;
        Ok(result.rows_affected())
    }

    /// 恢复软删除的记录
    pub async fn restore(
        pool: &PgPool,
        table: &str,
        id_column: &str,
        id_value: &str,
    ) -> Result<u64> {
        let query = format!(
            "UPDATE {} SET deleted_at = NULL WHERE {} = $1",
            table, id_column
        );
        let result = sqlx::query(&query)
            .bind(id_value)
            .execute(pool)
            .await?;
        Ok(result.rows_affected())
    }
} 