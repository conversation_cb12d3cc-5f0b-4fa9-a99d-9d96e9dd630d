use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;
use sqlx::Row;

// 导入标准的KlineEntity定义
pub(crate) use common::domain::entity::KlineEntity;

type Result<T> = std::result::Result<T, RepositoryError>;
/// 时间桶统计结果
#[derive(Debug, Clone)]
pub struct TimeBucketCount {
    pub time_bucket: DateTime<Utc>,
    pub actual_count: i64,
}

/// 数据统计实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct DataStatsEntity {
    pub total_records: i64,
    pub earliest_time: Option<DateTime<Utc>>,
    pub latest_time: Option<DateTime<Utc>>,
}

/// 时间范围实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct TimeRangeEntity {
    pub min_time: Option<DateTime<Utc>>,
    pub max_time: Option<DateTime<Utc>>,
}

/// 时间桶实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct TimeBucketEntity {
    pub minute_bucket: DateTime<Utc>,
    pub data_count: Option<i64>,
}

/// OHLCV市场数据
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct MarketData {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub volume: Decimal,
    pub interval_type: String, // '1m', '5m', '1h', '1d' etc.
}

/// 市场数据插入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateMarketDataRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub volume: Decimal,
    pub interval_type: String,
    pub quote_asset_volume: Decimal,
    pub number_of_trades: i32,
    pub taker_buy_base_asset_volume: Decimal,
    pub taker_buy_quote_asset_volume: Decimal,
    pub close_time: DateTime<Utc>,
}

/// 市场数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketDataQuery {
    pub symbol: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub interval_type: String,
    pub limit: Option<u32>,
}

/// 市场数据统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct MarketStats {
    pub count: i64,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub total_volume: Option<Decimal>,
    pub avg_price: Option<Decimal>,
}

/// K线数据Repository
#[derive(Clone)]
pub struct KlineRepository {
    pool: PgPool,
}

impl KlineRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化K线表结构
    pub async fn initialize_table(&self) -> Result<()> {
        // 创建K线数据表（更新结构以支持新的字段设计）
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS klines (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                interval TEXT,
                open_price DECIMAL NOT NULL,
                high_price DECIMAL NOT NULL,
                low_price DECIMAL NOT NULL,
                close_price DECIMAL NOT NULL,
                volume DECIMAL NOT NULL,
                quote_asset_volume DECIMAL NOT NULL,
                number_of_trades INTEGER NOT NULL,
                taker_buy_base_asset_volume DECIMAL NOT NULL,
                taker_buy_quote_asset_volume DECIMAL NOT NULL,
                close_time TIMESTAMPTZ NOT NULL,
                source TEXT
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将K线表转换为超级表
        log::info!("正在创建klines超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("klines")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建klines超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("klines超级表创建成功");

        // 添加唯一约束（更新为基于time和symbol）
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'klines_unique' AND conrelid = 'klines'::regclass
                ) THEN
                    ALTER TABLE klines ADD CONSTRAINT klines_unique UNIQUE (time, symbol);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_klines_symbol_time
             ON klines (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_klines_source_time
             ON klines (source, time DESC) WHERE source IS NOT NULL;",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("K线表结构初始化完成");
        Ok(())
    }

    /// 数据库迁移：添加source字段并调整interval字段
    pub async fn migrate_schema(&self) -> Result<()> {
        log::info!("开始数据库架构迁移...");

        // 添加source列（如果不存在）
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'klines' AND column_name = 'source'
                ) THEN
                    ALTER TABLE klines ADD COLUMN source TEXT;
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 修改interval为可选（如果当前是NOT NULL）
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'klines' AND column_name = 'interval' AND is_nullable = 'NO'
                ) THEN
                    ALTER TABLE klines ALTER COLUMN interval DROP NOT NULL;
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 更新历史数据：为没有source的记录设置默认值
        let updated_rows = sqlx::query(
            "UPDATE klines SET source = 'unknown' WHERE source IS NULL;"
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("已更新 {} 条历史记录的source字段", updated_rows.rows_affected());

        // 清理1m数据的interval字段
        let cleaned_rows = sqlx::query(
            "UPDATE klines SET interval = NULL WHERE interval = '1m';"
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("已清理 {} 条1m数据的interval字段", cleaned_rows.rows_affected());

        // 创建新的索引
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_klines_source_time
             ON klines (source, time DESC) WHERE source IS NOT NULL;",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("数据库架构迁移完成");
        Ok(())
    }

    /// 插入单条市场数据（支持重复数据处理）
    pub async fn insert(&self, request: CreateMarketDataRequest,source:&str) -> Result<MarketData> {
        let data = sqlx::query_as::<_, MarketData>(
            r#"
            INSERT INTO klines (symbol, time, open_price, high_price, low_price, close_price, volume, interval, 
                               quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, 
                               taker_buy_quote_asset_volume, close_time, source)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            ON CONFLICT (time, symbol) DO UPDATE SET
                open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                close_price = EXCLUDED.close_price,
                volume = EXCLUDED.volume,
                interval = EXCLUDED.interval,
                quote_asset_volume = EXCLUDED.quote_asset_volume,
                number_of_trades = EXCLUDED.number_of_trades,
                taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume,
                close_time = EXCLUDED.close_time,
                source = EXCLUDED.source
            RETURNING symbol, time as timestamp, open_price as open, high_price as high, 
                     low_price as low, close_price as close, volume, interval as interval_type
            "#,
        )
        .bind(&request.symbol)
        .bind(&request.timestamp)
        .bind(&request.open)
        .bind(&request.high)
        .bind(&request.low)
        .bind(&request.close)
        .bind(&request.volume)
        .bind(&request.interval_type)
        .bind(&request.quote_asset_volume)
        .bind(&request.number_of_trades)
        .bind(&request.taker_buy_base_asset_volume)
        .bind(&request.taker_buy_quote_asset_volume)
        .bind(&request.close_time)
        .bind(source) // 默认source为unknown，实际使用时可以通过新方法指定
        .fetch_one(&self.pool)
        .await?;

        Ok(data)
    }

    /// 批量插入市场数据（支持重复数据处理）
    pub async fn batch_insert(
        &self,
        requests: Vec<CreateMarketDataRequest>,
    ) -> std::result::Result<u64, RepositoryError> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO klines (symbol, time, open_price, high_price, low_price, close_price, volume, interval, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, close_time, source) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.symbol)
                .push_bind(&request.timestamp)
                .push_bind(&request.open)
                .push_bind(&request.high)
                .push_bind(&request.low)
                .push_bind(&request.close)
                .push_bind(&request.volume)
                .push_bind(&request.interval_type)
                .push_bind(&request.quote_asset_volume)
                .push_bind(&request.number_of_trades)
                .push_bind(&request.taker_buy_base_asset_volume)
                .push_bind(&request.taker_buy_quote_asset_volume)
                .push_bind(&request.close_time)
                .push_bind("unknown"); // 默认source
        });

        // 更新 ON CONFLICT 子句以适应新的约束
        query_builder.push(
            " ON CONFLICT (time, symbol) DO UPDATE SET \
             interval = EXCLUDED.interval, \
             open_price = EXCLUDED.open_price, \
             high_price = EXCLUDED.high_price, \
             low_price = EXCLUDED.low_price, \
             close_price = EXCLUDED.close_price, \
             volume = EXCLUDED.volume, \
             quote_asset_volume = EXCLUDED.quote_asset_volume, \
             number_of_trades = EXCLUDED.number_of_trades, \
             taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume, \
             taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume, \
             close_time = EXCLUDED.close_time, \
             source = EXCLUDED.source"
        );

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询市场数据（兼容interval参数）
    pub async fn query(&self, params: MarketDataQuery) -> Result<Vec<MarketData>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT symbol, time as timestamp, open_price as open, high_price as high, low_price as low, close_price as close, volume, COALESCE(interval, '1m') as interval_type FROM klines WHERE symbol = "
        );
        query.push_bind(&params.symbol);
        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        
        // 如果interval_type不为空，添加interval条件
        if !params.interval_type.is_empty() && params.interval_type != "1m" {
            query.push(" AND interval = ").push_bind(&params.interval_type);
        } else {
            // 对于1m数据，查询interval为NULL或'1m'的记录
            query.push(" AND (interval IS NULL OR interval = '1m')");
        }
        
        query.push(" ORDER BY time ASC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let data: Vec<MarketData> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(data)
    }

    /// 获取最新的市场数据（兼容interval参数）
    pub async fn get_latest(&self, symbol: &str, interval_type: &str) -> Result<Option<MarketData>> {
        let data = if interval_type.is_empty() || interval_type == "1m" {
            // 查询1m数据
            sqlx::query_as::<_, MarketData>(
                "SELECT symbol, time as timestamp, open_price as open, high_price as high, low_price as low, close_price as close, volume, COALESCE(interval, '1m') as interval_type FROM klines WHERE symbol = $1 AND (interval IS NULL OR interval = '1m') ORDER BY time DESC LIMIT 1"
            )
            .bind(symbol)
            .fetch_optional(&self.pool)
            .await?
        } else {
            // 查询其他interval数据
            sqlx::query_as::<_, MarketData>(
                "SELECT symbol, time as timestamp, open_price as open, high_price as high, low_price as low, close_price as close, volume, interval as interval_type FROM klines WHERE symbol = $1 AND interval = $2 ORDER BY time DESC LIMIT 1"
            )
            .bind(symbol)
            .bind(interval_type)
            .fetch_optional(&self.pool)
            .await?
        };

        Ok(data)
    }

    /// 获取指定时间段的数据统计
    pub async fn get_stats(&self, symbol: &str, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> Result<MarketStats> {
        let stats = sqlx::query_as::<_, MarketStats>(
            r#"
            SELECT 
                COUNT(*) as count,
                MIN(low_price) as min_price,
                MAX(high_price) as max_price,
                SUM(volume) as total_volume,
                AVG(close_price) as avg_price
            FROM klines 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 删除旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM klines WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 分析指定时间范围内的数据统计
    pub async fn analyze_data_stats(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<DataStatsEntity> {
        let result = if interval.is_empty() || interval == "1m" {
            // 查询1m数据（interval为NULL或'1m'）
            sqlx::query_as::<_, DataStatsEntity>(
                "SELECT 
                    COUNT(*) as total_records,
                    MIN(time) as earliest_time,
                    MAX(time) as latest_time
                 FROM klines 
                 WHERE symbol = $1 AND (interval IS NULL OR interval = '1m')
                    AND time >= $2 AND time <= $3"
            )
            .bind(symbol)
            .bind(start_time)
            .bind(end_time)
            .fetch_one(&self.pool)
            .await?
        } else {
            // 查询其他interval数据
            sqlx::query_as::<_, DataStatsEntity>(
                "SELECT 
                    COUNT(*) as total_records,
                    MIN(time) as earliest_time,
                    MAX(time) as latest_time
                 FROM klines 
                 WHERE symbol = $1 AND interval = $2 
                    AND time >= $3 AND time <= $4"
            )
            .bind(symbol)
            .bind(interval)
            .bind(start_time)
            .bind(end_time)
            .fetch_one(&self.pool)
            .await?
        };

        Ok(result)
    }

    /// 获取指定交易对和间隔的时间范围
    pub async fn get_time_range(&self, symbol: &str, interval: &str) -> Result<TimeRangeEntity> {
        let result = if interval.is_empty() || interval == "1m" {
            // 查询1m数据
            sqlx::query_as::<_, TimeRangeEntity>(
                "SELECT MIN(time) as min_time, MAX(time) as max_time 
                 FROM klines 
                 WHERE symbol = $1 AND (interval IS NULL OR interval = '1m')"
            )
            .bind(symbol)
            .fetch_one(&self.pool)
            .await?
        } else {
            // 查询其他interval数据
            sqlx::query_as::<_, TimeRangeEntity>(
                "SELECT MIN(time) as min_time, MAX(time) as max_time 
                 FROM klines 
                 WHERE symbol = $1 AND interval = $2"
            )
            .bind(symbol)
            .bind(interval)
            .fetch_one(&self.pool)
            .await?
        };

        Ok(result)
    }

    /// 查找缺失的时间桶
    pub async fn find_missing_time_buckets(
        &self,
        symbol: &str,
        interval: &str,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketEntity>> {
        let query = format!(
            r#"
            SELECT 
                time_bucket('{}', generate_series($3, $4, '{}')) as minute_bucket,
                NULL::bigint as data_count
            FROM generate_series($3, $4, '{}') 
            EXCEPT 
            SELECT 
                time_bucket('{}', time) as minute_bucket,
                COUNT(*) as data_count
            FROM klines 
            WHERE symbol = $1 
                AND {} 
                AND time >= $3 
                AND time < $4
            GROUP BY minute_bucket
            ORDER BY minute_bucket ASC
            "#,
            bucket_interval, bucket_interval, bucket_interval, bucket_interval,
            if interval.is_empty() || interval == "1m" {
                "(interval IS NULL OR interval = '1m')"
            } else {
                "interval = $2"
            }
        );

        let rows = if interval.is_empty() || interval == "1m" {
            sqlx::query(&query)
                .bind(symbol)
                .bind(start_time)
                .bind(end_time)
                .fetch_all(&self.pool)
                .await
                .map_err(|e| RepositoryError::Database(e))?
        } else {
            sqlx::query(&query)
                .bind(symbol)
                .bind(interval)
                .bind(start_time)
                .bind(end_time)
                .fetch_all(&self.pool)
                .await
                .map_err(|e| RepositoryError::Database(e))?
        };

        let mut missing_buckets = Vec::new();
        for row in rows {
            let minute_bucket: DateTime<Utc> = row.get("minute_bucket");
            let data_count: Option<i64> = row.get("data_count");
            
            missing_buckets.push(TimeBucketEntity {
                minute_bucket,
                data_count,
            });
        }

        Ok(missing_buckets)
    }

    /// 查找K线数据
    pub async fn find_klines(
        &self,
        symbol: &str,
        interval: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<i64>,
    ) -> Result<Vec<KlineEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT time, symbol, interval, open_price, high_price, low_price, close_price, volume, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, close_time, source FROM klines WHERE symbol = "
        );
        
        query.push_bind(symbol);
        
        // 处理interval条件
        if interval.is_empty() || interval == "1m" {
            query.push(" AND (interval IS NULL OR interval = '1m')");
        } else {
            query.push(" AND interval = ");
            query.push_bind(interval);
        }

        if let Some(start) = start_time {
            query.push(" AND time >= ");
            query.push_bind(start);
        }

        if let Some(end) = end_time {
            query.push(" AND time <= ");
            query.push_bind(end);
        }

        query.push(" ORDER BY time ASC");

        if let Some(limit) = limit {
            query.push(" LIMIT ");
            query.push_bind(limit);
        }

        let klines = query
            .build_query_as::<KlineEntity>()
            .fetch_all(&self.pool)
            .await?;

        Ok(klines)
    }

    /// 执行时间桶聚合查询（用于数据完整性检查）
    pub async fn query_time_bucket_aggregation(
        &self,
        symbol: &str,
        interval: &str,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketCount>> {
        let query = format!(
            r#"
            SELECT 
                time_bucket('{}', time) as time_bucket,
                COUNT(*) as actual_count
            FROM klines 
            WHERE symbol = $1 
                AND {}
                AND time >= $3 
                AND time < $4
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
            bucket_interval,
            if interval.is_empty() || interval == "1m" {
                "(interval IS NULL OR interval = '1m')"
            } else {
                "interval = $2"
            }
        );

        let rows = if interval.is_empty() || interval == "1m" {
            sqlx::query(&query)
                .bind(symbol)
                .bind(start_time)
                .bind(end_time)
                .fetch_all(&self.pool)
                .await
                .map_err(|e| RepositoryError::Database(e))?
        } else {
            sqlx::query(&query)
                .bind(symbol)
                .bind(interval)
                .bind(start_time)
                .bind(end_time)
                .fetch_all(&self.pool)
                .await
                .map_err(|e| RepositoryError::Database(e))?
        };

        let mut bucket_counts = Vec::new();
        for row in rows {
            let time_bucket: DateTime<Utc> = row.get("time_bucket");
            let actual_count: i64 = row.get("actual_count");
            
            bucket_counts.push(TimeBucketCount {
                time_bucket,
                actual_count,
            });
        }

        Ok(bucket_counts)
    }

    /// 获取活跃交易对
    pub async fn get_active_symbols(&self, within_duration: chrono::Duration) -> Result<Vec<String>> {
        let cutoff_time = Utc::now() - within_duration;

        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT symbol FROM klines WHERE time >= $1 ORDER BY symbol"
        )
        .bind(cutoff_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(symbols)
    }

    /// 统计指定时间范围内的K线数量
    pub async fn count_klines(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<i64> {
        let count = if interval.is_empty() || interval == "1m" {
            // 查询1m数据
            sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM klines WHERE symbol = $1 AND (interval IS NULL OR interval = '1m') AND time >= $2 AND time < $3"
            )
            .bind(symbol)
            .bind(start_time)
            .bind(end_time)
            .fetch_one(&self.pool)
            .await?
        } else {
            // 查询其他interval数据
            sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM klines WHERE symbol = $1 AND interval = $2 AND time >= $3 AND time < $4"
            )
            .bind(symbol)
            .bind(interval)
            .bind(start_time)
            .bind(end_time)
            .fetch_one(&self.pool)
            .await?
        };

        Ok(count)
    }

    /// 获取K线数据
    pub async fn get_klines(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<i64>,
    ) -> Result<Vec<KlineEntity>> {
        self.find_klines(symbol, interval, Some(start_time), Some(end_time), limit).await
    }

    /// 批量插入K线数据（新版本，直接使用KlineEntity）
    pub async fn batch_insert_klines(&self, klines: &[KlineEntity],source:&str) -> Result<u64> {
        if klines.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO klines (time, symbol, interval, open_price, high_price, low_price, close_price, volume, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, close_time, source) "
        );

        query_builder.push_values(klines.iter(), |mut b, kline| {
            b.push_bind(&kline.time)
                .push_bind(&kline.symbol)
                .push_bind(&kline.interval)
                .push_bind(&kline.open_price)
                .push_bind(&kline.high_price)
                .push_bind(&kline.low_price)
                .push_bind(&kline.close_price)
                .push_bind(&kline.volume)
                .push_bind(&kline.quote_asset_volume)
                .push_bind(&kline.number_of_trades)
                .push_bind(&kline.taker_buy_base_asset_volume)
                .push_bind(&kline.taker_buy_quote_asset_volume)
                .push_bind(&kline.close_time)
                .push_bind(source);
        });

        // 添加 ON CONFLICT 子句
        query_builder.push(
            " ON CONFLICT (time, symbol) DO UPDATE SET \
             interval = EXCLUDED.interval, \
             open_price = EXCLUDED.open_price, \
             high_price = EXCLUDED.high_price, \
             low_price = EXCLUDED.low_price, \
             close_price = EXCLUDED.close_price, \
             volume = EXCLUDED.volume, \
             quote_asset_volume = EXCLUDED.quote_asset_volume, \
             number_of_trades = EXCLUDED.number_of_trades, \
             taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume, \
             taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume, \
             close_time = EXCLUDED.close_time, \
             source = EXCLUDED.source"
        );

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&self.pool).await?;
        Ok(())
    }

    /// 从 1m 数据聚合生成更高时间级别的K线数据
    pub async fn aggregate_from_1m(
        &self,
        symbol: &str,
        target_interval_str: &str,
        bucket_width: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> std::result::Result<u64, RepositoryError> {
        let query = r#"
        WITH new_klines AS (
            SELECT
                time_bucket($3, timestamp) AS bucket_time,
                FIRST(open, timestamp) as open,
                MAX(high) as high,
                MIN(low) as low,
                LAST(close, timestamp) as close,
                SUM(volume) as volume,
                SUM(quote_asset_volume) as quote_asset_volume,
                SUM(number_of_trades) as number_of_trades,
                SUM(taker_buy_base_asset_volume) as taker_buy_base_asset_volume,
                SUM(taker_buy_quote_asset_volume) as taker_buy_quote_asset_volume,
                LAST(close_time, timestamp) as close_time
            FROM market_data
            WHERE symbol = $1 AND interval_type = '1m' AND timestamp >= $4 AND timestamp < $5
            GROUP BY bucket_time
        )
        INSERT INTO market_data (
            timestamp, symbol, interval_type, open, high, low, close, volume, 
            quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, 
            taker_buy_quote_asset_volume, close_time
        )
        SELECT
            bucket_time,
            $1,
            $2,
            open,
            high,
            low,
            close,
            volume,
            quote_asset_volume,
            number_of_trades,
            taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume,
            close_time
        FROM new_klines
        ON CONFLICT (symbol, interval_type, timestamp) DO UPDATE SET
            open = EXCLUDED.open,
            high = EXCLUDED.high,
            low = EXCLUDED.low,
            close = EXCLUDED.close,
            volume = EXCLUDED.volume,
            quote_asset_volume = EXCLUDED.quote_asset_volume,
            number_of_trades = EXCLUDED.number_of_trades,
            taker_buy_base_asset_volume = EXCLUDED.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume = EXCLUDED.taker_buy_quote_asset_volume,
            close_time = EXCLUDED.close_time;
        "#;

        let result = sqlx::query(query)
            .bind(symbol)
            .bind(target_interval_str)
            .bind(bucket_width)
            .bind(start_time)
            .bind(end_time)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected())
    }
} 