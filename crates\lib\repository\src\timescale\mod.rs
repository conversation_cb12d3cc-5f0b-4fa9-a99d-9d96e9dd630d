pub mod market_data; // 市场数据聚合Repository
pub mod klines; // K线数据
pub mod tickers; // 价格数据
pub mod order_books; // 订单簿数据
pub mod stats_24hr; // 24小时统计
pub mod trades; // 交易数据
pub mod aggregations; // 聚合查询
pub mod symbol; // 交易对管理

// 重新导出主要类型 - K线相关
pub use klines::{
    KlineRepository, KlineEntity, MarketData, CreateMarketDataRequest, 
    MarketDataQuery, MarketStats, TimeBucketCount, DataStatsEntity,
    TimeRangeEntity, TimeBucketEntity
};

// 重新导出价格相关
pub use tickers::{
    TickerRepository, TickerEntity, CreateTickerRequest, 
    TickerQuery, TickerStats
};

// 重新导出订单簿相关
pub use order_books::{
    OrderBookRepository, OrderBookEntity, CreateOrderBookRequest,
    OrderBookQuery, OrderBookStats, OrderBookSnapshot, OrderBookEntry
};

// 重新导出24小时统计相关
pub use stats_24hr::{
    Stats24hrRepository, Stats24hrEntity, CreateStats24hrRequest,
    Stats24hrQuery, SimpleStats24hr, MarketOverview
};

// 重新导出交易相关
pub use trades::{
    TradeRepository, TradeEntity, CreateTradeRequest,
    TradeQuery, TradeStats
};

// 重新导出其他模块
pub use aggregations::*;
pub use symbol::*;

// 重新导出市场数据聚合Repository
pub use market_data::MarketDataRepository; 