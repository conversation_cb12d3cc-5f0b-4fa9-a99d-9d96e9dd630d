# 多阶段构建的Dockerfile - 市场数据维护服务
# 支持不同环境的部署

# 构建阶段
FROM rust:1.75-slim as builder

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制Cargo文件
COPY Cargo.toml Cargo.lock ./
COPY crates/ ./crates/

# 构建依赖（利用Docker缓存）
RUN cargo build --release --bin user-server

# 运行时阶段 - 市场数据维护服务
FROM ubuntu:latest AS market-data-maintenance

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false -m -d /app appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/target/release/user-server ./bin/
COPY --chown=appuser:appuser config/ ./config/
COPY --chown=appuser:appuser scripts/ ./scripts/

# 创建必要的目录
RUN mkdir -p logs pids data && \
    chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 设置环境变量
ENV RUST_LOG=info
ENV ENVIRONMENT=production
ENV CONFIG_DIR=/app/config

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8091/health || exit 1

# 暴露端口
EXPOSE 8091 50051

# 启动命令
CMD ["./bin/user-server"]

# 原有的服务保持不变
FROM ubuntu:latest AS data-grpc

# 解决代理认证的问题
RUN apt-get update && \
    apt-get install -y ca-certificates && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

ENV http_proxy ************:7897
ENV https_proxy ************:7897

WORKDIR /app
COPY   ./target/release/data-grpc ./
ENTRYPOINT ./data-grpc -e prod

FROM ubuntu:latest AS data-http

ENV REDIS_HOST ***********
ENV REDIS_PORT 16379
ENV REDIS_PASSWORD 123456

WORKDIR /app
COPY   ./target/release/data-http ./
COPY   ./.env ./
ENTRYPOINT ./data-http

FROM ubuntu:latest AS dcaut-runner

WORKDIR /app
COPY   ./dist/dcaut-runner ./
COPY   ./dist/dcaut-demo  ./target/debug/
COPY   ./config   ./test/config
COPY   ./store    ./store

ENTRYPOINT ./dcaut-runner

