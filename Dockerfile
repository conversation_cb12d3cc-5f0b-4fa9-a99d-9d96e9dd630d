#FROM m.daocloud.io/docker.io/library/ubuntu:latest AS data_grpc
FROM ubuntu:latest AS data-grpc
#RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/LISTS/*
#FROM alpine:latest AS opc-mqtt
#ENV TZ=Asia/Shanghai
#RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone

# 解决代理认证的问题
RUN apt-get update && \
    apt-get install -y ca-certificates && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

ENV http_proxy ************:7897
ENV https_proxy ************:7897

WORKDIR /app
COPY   ./target/release/data-grpc ./
ENTRYPOINT ./data-grpc -e prod

#FROM m.daocloud.io/docker.io/library/ubuntu:latest AS data_grpc
FROM ubuntu:latest AS data-http

ENV REDIS_HOST ***********
ENV REDIS_PORT 16379
ENV REDIS_PASSWORD 123456

WORKDIR /app
COPY   ./target/release/data-http ./
COPY   ./.env ./
ENTRYPOINT ./data-http



#FROM m.daocloud.io/docker.io/library/ubuntu:latest AS data_grpc
FROM ubuntu:latest AS dcaut-runner

WORKDIR /app
COPY   ./dist/dcaut-runner ./
COPY   ./dist/dcaut-demo  ./target/debug/
COPY   ./config   ./test/config
COPY   ./store    ./store

ENTRYPOINT ./dcaut-runner