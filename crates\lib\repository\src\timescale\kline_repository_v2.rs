// =====================================================
// 重构的K线数据Repository - 基于新的架构设计
// 核心原则：1m数据作为SSOT，连续聚合生成其他时间级别
// =====================================================

use chrono::{DateTime, Utc, Duration};
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool, Row};
use anyhow::Result;
use std::collections::HashMap;

use crate::error::RepositoryError;
use crate::cache::kline_cache::{KlineCache, KlineCacheData};

/// 1分钟K线实体 - 唯一真相源(SSOT)
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Kline1mEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub exchange: String,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub close_price: Decimal,
    pub volume: Decimal,
    pub quote_volume: Decimal,
    pub trades_count: i32,
    pub taker_buy_volume: Decimal,
    pub taker_buy_quote_volume: Decimal,
    pub created_at: Option<DateTime<Utc>>,
}

/// 聚合K线实体 - 用于所有时间级别的聚合数据
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AggregatedKlineEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub exchange: String,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub close_price: Decimal,
    pub volume: Decimal,
    pub quote_volume: Decimal,
    pub trades_count: i64,
    pub taker_buy_volume: Decimal,
    pub taker_buy_quote_volume: Decimal,
}

/// K线插入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InsertKlineRequest {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub exchange: String,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub close_price: Decimal,
    pub volume: Decimal,
    pub quote_volume: Decimal,
    pub trades_count: i32,
    pub taker_buy_volume: Decimal,
    pub taker_buy_quote_volume: Decimal,
}

/// 数据完整性检查结果
#[derive(Debug, Clone)]
pub struct IntegrityCheckResult {
    pub symbol: String,
    pub exchange: String,
    pub total_expected: i64,
    pub actual_count: i64,
    pub missing_count: i64,
    pub completeness_percentage: f64,
    pub missing_ranges: Vec<(DateTime<Utc>, DateTime<Utc>)>,
    pub latest_data_time: Option<DateTime<Utc>>,
    pub data_lag_minutes: Option<i64>,
}

/// 支持的时间间隔
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TimeInterval {
    OneMinute,
    FiveMinutes,
    FifteenMinutes,
    OneHour,
    FourHours,
    OneDay,
}

impl TimeInterval {
    pub fn to_string(&self) -> &'static str {
        match self {
            TimeInterval::OneMinute => "1m",
            TimeInterval::FiveMinutes => "5m",
            TimeInterval::FifteenMinutes => "15m",
            TimeInterval::OneHour => "1h",
            TimeInterval::FourHours => "4h",
            TimeInterval::OneDay => "1d",
        }
    }

    pub fn to_table_name(&self) -> &'static str {
        match self {
            TimeInterval::OneMinute => "kline_1m",
            TimeInterval::FiveMinutes => "kline_5m",
            TimeInterval::FifteenMinutes => "kline_15m",
            TimeInterval::OneHour => "kline_1h",
            TimeInterval::FourHours => "kline_4h",
            TimeInterval::OneDay => "kline_1d",
        }
    }
}

/// 重构的K线Repository
pub struct KlineRepositoryV2 {
    pool: PgPool,
    cache: Option<KlineCache>,
}

impl KlineRepositoryV2 {
    /// 创建新的Repository实例
    pub fn new(pool: PgPool) -> Self {
        Self {
            pool,
            cache: None,
        }
    }

    /// 创建带缓存的Repository实例
    pub fn with_cache(pool: PgPool, cache: KlineCache) -> Self {
        Self {
            pool,
            cache: Some(cache),
        }
    }

    /// 获取数据库连接池引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 初始化数据库结构
    pub async fn initialize_schema(&self) -> Result<()> {
        tracing::info!("🔧 开始初始化K线数据库结构");

        // 1. 创建基础1分钟K线表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS kline_1m (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                exchange TEXT NOT NULL DEFAULT 'binance',
                open_price DECIMAL(20,8) NOT NULL,
                high_price DECIMAL(20,8) NOT NULL,
                low_price DECIMAL(20,8) NOT NULL,
                close_price DECIMAL(20,8) NOT NULL,
                volume DECIMAL(20,8) NOT NULL,
                quote_volume DECIMAL(20,8) NOT NULL,
                trades_count INTEGER NOT NULL,
                taker_buy_volume DECIMAL(20,8) NOT NULL,
                taker_buy_quote_volume DECIMAL(20,8) NOT NULL,
                created_at TIMESTAMPTZ DEFAULT NOW()
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // 2. 创建TimescaleDB超级表
        let _ = sqlx::query(
            "SELECT create_hypertable('kline_1m', 'time', chunk_time_interval => INTERVAL '1 day', if_not_exists => TRUE)"
        )
        .execute(&self.pool)
        .await; // 忽略错误，可能已经是超级表

        // 3. 创建唯一性约束
        let _ = sqlx::query(
            "CREATE UNIQUE INDEX IF NOT EXISTS idx_kline_1m_unique ON kline_1m (time, symbol)"
        )
        .execute(&self.pool)
        .await;

        // 4. 创建查询优化索引
        let _ = sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_kline_1m_symbol_time ON kline_1m (symbol, time DESC)"
        )
        .execute(&self.pool)
        .await;

        let _ = sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_kline_1m_exchange_symbol_time ON kline_1m (exchange, symbol, time DESC)"
        )
        .execute(&self.pool)
        .await;

        tracing::info!("✅ K线数据库结构初始化完成");
        Ok(())
    }

    /// 双写操作：同时写入TimescaleDB和Redis
    pub async fn insert_kline_with_cache(
        &self,
        request: InsertKlineRequest,
    ) -> Result<()> {
        // 1. 写入TimescaleDB (幂等操作)
        sqlx::query(
            r#"
            INSERT INTO kline_1m (
                time, symbol, exchange, open_price, high_price, low_price, close_price,
                volume, quote_volume, trades_count, taker_buy_volume, taker_buy_quote_volume
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            ON CONFLICT (time, symbol) DO UPDATE SET
                exchange = EXCLUDED.exchange,
                open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                close_price = EXCLUDED.close_price,
                volume = EXCLUDED.volume,
                quote_volume = EXCLUDED.quote_volume,
                trades_count = EXCLUDED.trades_count,
                taker_buy_volume = EXCLUDED.taker_buy_volume,
                taker_buy_quote_volume = EXCLUDED.taker_buy_quote_volume
            "#,
        )
        .bind(&request.time)
        .bind(&request.symbol)
        .bind(&request.exchange)
        .bind(&request.open_price)
        .bind(&request.high_price)
        .bind(&request.low_price)
        .bind(&request.close_price)
        .bind(&request.volume)
        .bind(&request.quote_volume)
        .bind(&request.trades_count)
        .bind(&request.taker_buy_volume)
        .bind(&request.taker_buy_quote_volume)
        .execute(&self.pool)
        .await?;

        // 2. 写入Redis缓存 (如果启用)
        if let Some(cache) = &self.cache {
            let cache_data = KlineCacheData {
                timestamp: request.time.timestamp_millis(),
                open: request.open_price.to_string(),
                high: request.high_price.to_string(),
                low: request.low_price.to_string(),
                close: request.close_price.to_string(),
                volume: request.volume.to_string(),
                quote_volume: request.quote_volume.to_string(),
                trades: request.trades_count,
            };

            cache.write_kline(&request.symbol, &cache_data).await?;
        }

        Ok(())
    }

    /// 批量插入K线数据
    pub async fn batch_insert_klines(
        &self,
        requests: &[InsertKlineRequest],
    ) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO kline_1m (time, symbol, exchange, open_price, high_price, low_price, close_price, volume, quote_volume, trades_count, taker_buy_volume, taker_buy_quote_volume) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.time)
                .push_bind(&request.symbol)
                .push_bind(&request.exchange)
                .push_bind(&request.open_price)
                .push_bind(&request.high_price)
                .push_bind(&request.low_price)
                .push_bind(&request.close_price)
                .push_bind(&request.volume)
                .push_bind(&request.quote_volume)
                .push_bind(&request.trades_count)
                .push_bind(&request.taker_buy_volume)
                .push_bind(&request.taker_buy_quote_volume);
        });

        query_builder.push(
            " ON CONFLICT (time, symbol) DO UPDATE SET \
             exchange = EXCLUDED.exchange, \
             open_price = EXCLUDED.open_price, \
             high_price = EXCLUDED.high_price, \
             low_price = EXCLUDED.low_price, \
             close_price = EXCLUDED.close_price, \
             volume = EXCLUDED.volume, \
             quote_volume = EXCLUDED.quote_volume, \
             trades_count = EXCLUDED.trades_count, \
             taker_buy_volume = EXCLUDED.taker_buy_volume, \
             taker_buy_quote_volume = EXCLUDED.taker_buy_quote_volume"
        );

        let result = query_builder.build().execute(&self.pool).await?;

        // 批量写入缓存
        if let Some(cache) = &self.cache {
            // 按交易对分组
            let mut symbol_groups: HashMap<String, Vec<KlineCacheData>> = HashMap::new();
            
            for request in requests {
                let cache_data = KlineCacheData {
                    timestamp: request.time.timestamp_millis(),
                    open: request.open_price.to_string(),
                    high: request.high_price.to_string(),
                    low: request.low_price.to_string(),
                    close: request.close_price.to_string(),
                    volume: request.volume.to_string(),
                    quote_volume: request.quote_volume.to_string(),
                    trades: request.trades_count,
                };
                
                symbol_groups.entry(request.symbol.clone())
                    .or_insert_with(Vec::new)
                    .push(cache_data);
            }

            // 批量写入每个交易对的缓存
            for (symbol, klines) in symbol_groups {
                if let Err(e) = cache.write_klines_batch(&symbol, &klines).await {
                    tracing::warn!("缓存写入失败 {}: {}", symbol, e);
                }
            }
        }

        Ok(result.rows_affected())
    }

    /// Cache-Aside模式读取K线数据
    pub async fn get_klines(
        &self,
        symbol: &str,
        interval: TimeInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> Result<Vec<AggregatedKlineEntity>> {
        // 1. 如果是1分钟数据且启用了缓存，优先从缓存读取
        if interval == TimeInterval::OneMinute {
            if let Some(cache) = &self.cache {
                match cache.read_klines(symbol, start_time, end_time).await {
                    Ok(cached_data) => {
                        if !cached_data.is_empty() {
                            // 转换缓存数据为实体
                            let mut entities = Vec::new();
                            for cache_item in cached_data {
                                if let Ok(entity) = self.convert_cache_to_entity(symbol, &cache_item) {
                                    entities.push(entity);
                                }
                            }
                            
                            // 应用limit限制
                            if let Some(limit) = limit {
                                entities.truncate(limit as usize);
                            }
                            
                            return Ok(entities);
                        }
                    }
                    Err(e) => {
                        tracing::warn!("缓存读取失败，回退到数据库: {}", e);
                    }
                }
            }
        }

        // 2. 从数据库读取
        self.get_klines_from_db(symbol, interval, start_time, end_time, limit).await
    }

    /// 从数据库读取K线数据
    async fn get_klines_from_db(
        &self,
        symbol: &str,
        interval: TimeInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> Result<Vec<AggregatedKlineEntity>> {
        let table_name = interval.to_table_name();
        
        let mut query = format!(
            "SELECT time, symbol, exchange, open_price, high_price, low_price, close_price, \
             volume, quote_volume, trades_count, taker_buy_volume, taker_buy_quote_volume \
             FROM {} WHERE symbol = $1 AND time >= $2 AND time <= $3 \
             ORDER BY time ASC",
            table_name
        );

        if let Some(limit) = limit {
            query.push_str(&format!(" LIMIT {}", limit));
        }

        let klines = sqlx::query_as::<_, AggregatedKlineEntity>(&query)
            .bind(symbol)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(&self.pool)
            .await?;

        Ok(klines)
    }

    /// 转换缓存数据为实体
    fn convert_cache_to_entity(
        &self,
        symbol: &str,
        cache_data: &KlineCacheData,
    ) -> Result<AggregatedKlineEntity> {
        use std::str::FromStr;

        let time = DateTime::from_timestamp_millis(cache_data.timestamp)
            .ok_or_else(|| anyhow::anyhow!("Invalid timestamp"))?;

        Ok(AggregatedKlineEntity {
            time,
            symbol: symbol.to_string(),
            exchange: "binance".to_string(), // 默认值，实际应该从缓存中获取
            open_price: Decimal::from_str(&cache_data.open)?,
            high_price: Decimal::from_str(&cache_data.high)?,
            low_price: Decimal::from_str(&cache_data.low)?,
            close_price: Decimal::from_str(&cache_data.close)?,
            volume: Decimal::from_str(&cache_data.volume)?,
            quote_volume: Decimal::from_str(&cache_data.quote_volume)?,
            trades_count: cache_data.trades as i64,
            taker_buy_volume: Decimal::ZERO, // 缓存中未存储，使用默认值
            taker_buy_quote_volume: Decimal::ZERO,
        })
    }

    /// 使用time_bucket_gapfill进行数据完整性检查
    pub async fn check_data_integrity(
        &self,
        symbol: &str,
        exchange: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<IntegrityCheckResult> {
        // 调用数据库函数进行完整性检查
        let query = "SELECT * FROM check_kline_data_integrity($1, $2, $3, $4)";

        let missing_gaps: Vec<(DateTime<Utc>, String, String, i64, bool, Option<i32>)> =
            sqlx::query_as(query)
                .bind(symbol)
                .bind(exchange)
                .bind(start_time)
                .bind(end_time)
                .fetch_all(&self.pool)
                .await?;

        // 获取实际数据统计
        let stats_query = r#"
            SELECT
                COUNT(*) as actual_count,
                MAX(time) as latest_time
            FROM kline_1m
            WHERE symbol = $1 AND exchange = $2
              AND time >= $3 AND time <= $4
        "#;

        let stats: (i64, Option<DateTime<Utc>>) = sqlx::query_as(stats_query)
            .bind(symbol)
            .bind(exchange)
            .bind(start_time)
            .bind(end_time)
            .fetch_one(&self.pool)
            .await?;

        let total_expected = (end_time - start_time).num_minutes();
        let actual_count = stats.0;
        let missing_count = total_expected - actual_count;
        let completeness_percentage = if total_expected > 0 {
            (actual_count as f64 / total_expected as f64) * 100.0
        } else {
            100.0
        };

        // 计算数据延迟
        let data_lag_minutes = if let Some(latest_time) = stats.1 {
            Some((Utc::now() - latest_time).num_minutes())
        } else {
            None
        };

        // 转换缺失范围
        let missing_ranges = self.convert_gaps_to_ranges(missing_gaps);

        Ok(IntegrityCheckResult {
            symbol: symbol.to_string(),
            exchange: exchange.to_string(),
            total_expected,
            actual_count,
            missing_count,
            completeness_percentage,
            missing_ranges,
            latest_data_time: stats.1,
            data_lag_minutes,
        })
    }

    /// 批量检查所有交易对的数据完整性
    pub async fn check_all_symbols_integrity(
        &self,
        exchange: &str,
        hours_back: i32,
    ) -> Result<Vec<IntegrityCheckResult>> {
        let query = "SELECT * FROM check_all_symbols_integrity($1, $2)";

        let results: Vec<(String, String, i64, i64, i64, Decimal, Option<DateTime<Utc>>, Option<i32>)> =
            sqlx::query_as(query)
                .bind(exchange)
                .bind(hours_back)
                .fetch_all(&self.pool)
                .await?;

        let mut integrity_results = Vec::new();

        for (symbol, exchange, total_expected, actual_count, missing_count, completeness_pct, latest_time, lag_minutes) in results {
            integrity_results.push(IntegrityCheckResult {
                symbol,
                exchange,
                total_expected,
                actual_count,
                missing_count,
                completeness_percentage: completeness_pct.to_f64().unwrap_or(0.0),
                missing_ranges: Vec::new(), // 批量检查时不返回详细的缺失范围
                latest_data_time: latest_time,
                data_lag_minutes: lag_minutes.map(|m| m as i64),
            });
        }

        Ok(integrity_results)
    }

    /// 获取数据延迟监控信息
    pub async fn get_data_lag_status(&self) -> Result<Vec<DataLagStatus>> {
        let query = "SELECT * FROM v_kline_data_lag ORDER BY lag_minutes DESC";

        let results: Vec<(String, String, DateTime<Utc>, DateTime<Utc>, i32, String, i64, i64)> =
            sqlx::query_as(query)
                .fetch_all(&self.pool)
                .await?;

        let mut lag_statuses = Vec::new();

        for (symbol, exchange, latest_time, current_time, lag_minutes, status, points_1h, points_24h) in results {
            lag_statuses.push(DataLagStatus {
                symbol,
                exchange,
                latest_data_time: latest_time,
                current_time,
                lag_minutes: lag_minutes as i64,
                status: LagStatus::from_string(&status),
                data_points_last_hour: points_1h,
                data_points_last_24h: points_24h,
            });
        }

        Ok(lag_statuses)
    }

    /// 检查数据质量
    pub async fn check_data_quality(
        &self,
        symbol: &str,
        exchange: &str,
        hours_back: i32,
    ) -> Result<Vec<DataQualityIssue>> {
        let query = "SELECT * FROM check_data_quality($1, $2, $3)";

        let results: Vec<(DateTime<Utc>, String, String, String, Decimal, Decimal, Decimal, Decimal, Decimal)> =
            sqlx::query_as(query)
                .bind(symbol)
                .bind(exchange)
                .bind(hours_back)
                .fetch_all(&self.pool)
                .await?;

        let mut quality_issues = Vec::new();

        for (time, symbol, issue_type, description, open, high, low, close, volume) in results {
            quality_issues.push(DataQualityIssue {
                time,
                symbol,
                issue_type,
                description,
                open_price: open,
                high_price: high,
                low_price: low,
                close_price: close,
                volume,
            });
        }

        Ok(quality_issues)
    }

    /// 检查聚合数据一致性
    pub async fn check_aggregation_consistency(
        &self,
        symbol: &str,
        exchange: &str,
        date: chrono::NaiveDate,
    ) -> Result<Vec<AggregationConsistency>> {
        let query = "SELECT * FROM check_aggregation_consistency($1, $2, $3)";

        let results: Vec<(String, i64, i64, bool, Decimal)> =
            sqlx::query_as(query)
                .bind(symbol)
                .bind(exchange)
                .bind(date)
                .fetch_all(&self.pool)
                .await?;

        let mut consistency_results = Vec::new();

        for (period, source_count, agg_count, is_consistent, variance_pct) in results {
            consistency_results.push(AggregationConsistency {
                time_period: period,
                source_count,
                aggregate_count: agg_count,
                is_consistent,
                variance_percentage: variance_pct.to_f64().unwrap_or(0.0),
            });
        }

        Ok(consistency_results)
    }

    /// 转换缺失间隙为时间范围
    fn convert_gaps_to_ranges(
        &self,
        gaps: Vec<(DateTime<Utc>, String, String, i64, bool, Option<i32>)>,
    ) -> Vec<(DateTime<Utc>, DateTime<Utc>)> {
        let mut ranges = Vec::new();

        for (time, _symbol, _exchange, _count, is_missing, gap_duration) in gaps {
            if is_missing {
                let end_time = if let Some(duration) = gap_duration {
                    time + Duration::minutes(duration as i64)
                } else {
                    time + Duration::minutes(1)
                };
                ranges.push((time, end_time));
            }
        }

        ranges
    }

    /// 获取活跃交易对列表
    pub async fn get_active_symbols(&self, exchange: &str, hours_back: i32) -> Result<Vec<String>> {
        let query = r#"
            SELECT DISTINCT symbol
            FROM kline_1m
            WHERE exchange = $1
              AND time >= NOW() - ($2 || ' hours')::INTERVAL
            ORDER BY symbol
        "#;

        let symbols: Vec<(String,)> = sqlx::query_as(query)
            .bind(exchange)
            .bind(hours_back)
            .fetch_all(&self.pool)
            .await?;

        Ok(symbols.into_iter().map(|(s,)| s).collect())
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self, retention_days: i32) -> Result<u64> {
        let cutoff_time = Utc::now() - Duration::days(retention_days as i64);

        let result = sqlx::query("DELETE FROM kline_1m WHERE time < $1")
            .bind(cutoff_time)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected())
    }
}

/// 数据延迟状态
#[derive(Debug, Clone)]
pub struct DataLagStatus {
    pub symbol: String,
    pub exchange: String,
    pub latest_data_time: DateTime<Utc>,
    pub current_time: DateTime<Utc>,
    pub lag_minutes: i64,
    pub status: LagStatus,
    pub data_points_last_hour: i64,
    pub data_points_last_24h: i64,
}

/// 延迟状态枚举
#[derive(Debug, Clone, PartialEq)]
pub enum LagStatus {
    Healthy,
    Warning,
    Critical,
}

impl LagStatus {
    fn from_string(s: &str) -> Self {
        match s {
            "HEALTHY" => LagStatus::Healthy,
            "WARNING" => LagStatus::Warning,
            "CRITICAL" => LagStatus::Critical,
            _ => LagStatus::Critical,
        }
    }
}

/// 数据质量问题
#[derive(Debug, Clone)]
pub struct DataQualityIssue {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub issue_type: String,
    pub description: String,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub close_price: Decimal,
    pub volume: Decimal,
}

/// 聚合一致性检查结果
#[derive(Debug, Clone)]
pub struct AggregationConsistency {
    pub time_period: String,
    pub source_count: i64,
    pub aggregate_count: i64,
    pub is_consistent: bool,
    pub variance_percentage: f64,
}
