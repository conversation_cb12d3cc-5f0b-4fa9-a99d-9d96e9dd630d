use crate::nacos_config::{NacosClient, NacosConfig};
use anyhow::{Context, Result};
use log::warn;
use nacos_sdk::api::{
    config::ConfigServiceBuilder,
    naming::{NamingServiceBuilder, ServiceInstance},
    props::ClientProps,
};
use serde::Deserialize;
use std::sync::Arc;
use toml;
use common::domain::r#enum::error::AppError;

impl NacosClient {
    pub async fn new(config: &NacosConfig) -> Result<Self, AppError> {
        let client_props = ClientProps::new()
            .server_addr(&config.server_addr)
            .namespace("")
            .app_name(&config.app_name)
            .auth_username(&config.username)
            .auth_password(&config.password);

        // --- 为 config_service 构建器应用条件逻辑 ---
        let mut config_builder = ConfigServiceBuilder::new(client_props.clone());
        let mut naming_builder = NamingServiceBuilder::new(client_props);

        // 根据 app_name 条件性地启用认证插件
        if config.username == "nacos" {
            config_builder = config_builder.enable_auth_plugin_http();
            naming_builder = naming_builder.enable_auth_plugin_http();
        }
        // 继续构建 config_service
        let config_service = config_builder
            .build()
            .map_err(|e| AppError::NacosError(format!("Failed to create Nacos config client: {}", e)))?;

        let naming_service = naming_builder
            .build()
            .map_err(|e| AppError::NacosError(format!("Failed to create Nacos naming client: {}", e)))?;

        let instance = ServiceInstance {
            instance_id: None,
            service_name: Some(config.app_name.clone()),
            ip: config.client_ip.clone(),
            port: config.client_port,
            cluster_name: Some("DEFAULT".to_string()),
            weight: 1.0,
            healthy: true,
            enabled: true,
            ephemeral: true,
            metadata: Default::default(),
        };

        let client = Self {
            config: config.clone(),
            config_service: Arc::new(config_service),
            naming_service: Arc::new(naming_service),
            instance,
        };
        Ok(client)
    }

    // 注册服务实例
    pub async fn register_service(&mut self) -> Result<(), AppError> {
        let instance = self.instance.clone();
        let group_name = self.config.group.clone();
        let service_name = self.config.app_name.clone();
        self.naming_service
            .register_instance(service_name.to_string(), Some(group_name), instance.clone())
            .await
            .map_err(|e| AppError::NacosError(format!("Failed to register db: {}", e)))?;
        warn!("✅ 服务注册成功: {}", instance.ip);
        Ok(())
    }

    // 注销服务实例
    pub async fn deregister_service(&self) -> Result<(), AppError> {
        let instance = self.instance.clone();
        let group_name = self.config.group.clone();
        let service_name = self.config.app_name.clone();
        self.naming_service
            .deregister_instance(service_name, Some(group_name), instance)
            .await
            .expect("TODO: panic message");
        warn!("🛑 服务注销成功");
        Ok(())
    }

    // 在 NacosConfigClient impl 块中
    pub async fn get_toml_config<T: for<'de> Deserialize<'de>>(&self, data_id: &str, group: &str) -> Result<T> {
        let config_resp = self
            .config_service
            .get_config(data_id.to_string(), group.to_string())
            .await
            .map_err(|e| anyhow::anyhow!("Nacos SDK error getting config (data_id: '{}', group: '{}'): {}", data_id, group, e))?;
        toml::from_str(config_resp.content()).with_context(|| {
            format!(
                "Failed to deserialize TOML config for data_id \'{}\', group \'{}\'. Content:\n{}",
                data_id,
                group,
                config_resp.content()
            )
        })
    }

    // 获取 YAML 格式配置
    pub async fn get_yaml_config<T: for<'de> Deserialize<'de>>(&self, data_id: &str, group: &str) -> Result<T> {
        let config_resp = self
            .config_service
            .get_config(data_id.to_string(), group.to_string())
            .await
            .map_err(|e| anyhow::anyhow!("Nacos SDK error getting config (data_id: '{}', group: '{}'): {}", data_id, group, e))?;
        let content = config_resp.content(); // 获取配置内容字符串
        serde_yaml::from_str(content).with_context(|| format!("Failed to deserialize YAML config for data_id \'{}\', group \'{}\'. Content:\n{}", data_id, group, content))
    }
}
