use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use crate::{Result, RepositoryError};

/// 系统配置实体
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SystemConfig {
    pub key: String,
    pub value: String,
    pub description: Option<String>,
    pub config_type: String,
    pub is_encrypted: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 配置创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateConfigRequest {
    pub key: String,
    pub value: String,
    pub description: Option<String>,
    pub config_type: String,
    pub is_encrypted: bool,
}

/// 配置更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateConfigRequest {
    pub value: Option<String>,
    pub description: Option<String>,
    pub is_encrypted: Option<bool>,
}

/// 配置Repository
pub struct ConfigRepository {
    pool: PgPool,
}

impl ConfigRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建配置
    pub async fn create(&self, request: CreateConfigRequest) -> Result<SystemConfig> {
        let config = sqlx::query_as::<_, SystemConfig>(
            r#"
            INSERT INTO system_configs (key, value, description, config_type, is_encrypted, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING *
            "#,
        )
        .bind(&request.key)
        .bind(&request.value)
        .bind(&request.description)
        .bind(&request.config_type)
        .bind(&request.is_encrypted)
        .fetch_one(&self.pool)
        .await?;

        Ok(config)
    }

    /// 根据key获取配置
    pub async fn get_by_key(&self, key: &str) -> Result<Option<SystemConfig>> {
        let config = sqlx::query_as::<_, SystemConfig>(
            "SELECT * FROM system_configs WHERE key = $1"
        )
        .bind(key)
        .fetch_optional(&self.pool)
        .await?;

        Ok(config)
    }

    /// 根据类型获取配置列表
    pub async fn get_by_type(&self, config_type: &str) -> Result<Vec<SystemConfig>> {
        let configs = sqlx::query_as::<_, SystemConfig>(
            "SELECT * FROM system_configs WHERE config_type = $1 ORDER BY key ASC"
        )
        .bind(config_type)
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }

    /// 更新配置
    pub async fn update(&self, key: &str, request: UpdateConfigRequest) -> Result<Option<SystemConfig>> {
        let mut query_parts = Vec::new();
        let mut bind_count = 1;

        if request.value.is_some() {
            query_parts.push(format!("value = ${}", bind_count));
            bind_count += 1;
        }
        if request.description.is_some() {
            query_parts.push(format!("description = ${}", bind_count));
            bind_count += 1;
        }
        if request.is_encrypted.is_some() {
            query_parts.push(format!("is_encrypted = ${}", bind_count));
            bind_count += 1;
        }

        if query_parts.is_empty() {
            return self.get_by_key(key).await;
        }

        query_parts.push("updated_at = NOW()".to_string());
        let set_clause = query_parts.join(", ");
        let sql = format!(
            "UPDATE system_configs SET {} WHERE key = ${} RETURNING *",
            set_clause, bind_count
        );

        let mut query = sqlx::query_as::<_, SystemConfig>(&sql);
        
        if let Some(value) = &request.value {
            query = query.bind(value);
        }
        if let Some(description) = &request.description {
            query = query.bind(description);
        }
        if let Some(is_encrypted) = &request.is_encrypted {
            query = query.bind(is_encrypted);
        }
        query = query.bind(key);

        let config = query.fetch_optional(&self.pool).await?;
        Ok(config)
    }

    /// 删除配置
    pub async fn delete(&self, key: &str) -> Result<bool> {
        let result = sqlx::query("DELETE FROM system_configs WHERE key = $1")
            .bind(key)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    /// 获取所有配置
    pub async fn get_all(&self) -> Result<Vec<SystemConfig>> {
        let configs = sqlx::query_as::<_, SystemConfig>(
            "SELECT * FROM system_configs ORDER BY config_type ASC, key ASC"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(configs)
    }

    /// 批量获取配置
    pub async fn get_batch(&self, keys: Vec<String>) -> Result<Vec<SystemConfig>> {
        if keys.is_empty() {
            return Ok(Vec::new());
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "SELECT * FROM system_configs WHERE key IN ("
        );
        let mut separated = query_builder.separated(", ");
        for key in keys {
            separated.push_bind(key);
        }
        separated.push_unseparated(") ORDER BY key ASC");

        let configs: Vec<SystemConfig> = query_builder
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(configs)
    }
} 