use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;
use crate::domain::entity::user::User;

#[derive(Debug, Serialize, Deserialize, sqlx::Type, PartialEq, Eq)]
#[sqlx(type_name = "user_status", rename_all = "snake_case")]
pub enum UserStatus {
    Pending,
    Active,
    Frozen,
}

/// 用户信息数据 - 适配前端 UserInfo 接口
#[derive(Debug, Serialize, ToSchema)]
pub struct UserInfoData {
    /// 头像URL
    pub avatar: String,
    /// 用户名（使用邮箱作为用户名）
    pub username: String,
    /// 昵称
    pub nickname: String,
    /// 邮箱
    pub email: String,
    /// 联系电话
    pub phone: String,
    /// 简介
    pub description: String,
}

impl From<User> for UserInfoData {
    fn from(user: User) -> Self {
        Self {
            avatar: "https://via.placeholder.com/150".to_string(), // 默认头像
            username: user.email.clone(), // 使用邮箱作为用户名
            nickname: user.nickname,
            email: user.email,
            phone: "".to_string(), // 暂时为空，可以后续扩展用户表
            description: "".to_string(), // 暂时为空，可以后续扩展用户表
        }
    }
}


/// 登录成功时返回的用户数据 - 适配前端 UserResult.data 接口
#[derive(Debug, Serialize, ToSchema)]
pub struct LoginUserData {
    /// 头像URL
    pub avatar: String,
    /// 用户名（使用邮箱作为用户名）
    pub username: String,
    /// 昵称
    pub nickname: String,
    /// 用户角色列表
    pub roles: Vec<String>,
    /// 用户权限列表
    pub permissions: Vec<String>,
    /// 访问令牌
    #[serde(rename = "accessToken")]
    pub access_token: String,
    /// 刷新令牌
    #[serde(rename = "refreshToken")]
    pub refresh_token: String,
    /// 令牌过期时间
    pub expires: DateTime<Utc>,
}

impl From<User> for LoginUserData {
    fn from(user: User) -> Self {
        // 计算令牌过期时间（24小时后）
        let expires = Utc::now() + Duration::hours(24);

        Self {
            avatar: "https://via.placeholder.com/150".to_string(), // 默认头像
            username: user.email.clone(), // 使用邮箱作为用户名
            nickname: user.nickname,
            roles: vec!["user".to_string()], // 默认角色
            permissions: vec!["read".to_string()], // 默认权限
            access_token: "".to_string(), // 将在处理函数中设置
            refresh_token: "".to_string(), // 将在处理函数中设置
            expires,
        }
    }
}


/// 用户注册响应数据
#[derive(Debug, Serialize, ToSchema)]
pub struct RegisterUserData {
    /// 用户ID
    pub id: String,
    /// 邮箱
    pub email: String,
    /// 昵称
    pub nickname: String,
    /// 用户状态
    pub status: String,
    /// 创建时间
    pub created_at: String,
}

impl From<User> for RegisterUserData {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            email: user.email,
            nickname: user.nickname,
            status: format!("{:?}", user.status),
            created_at: user.created_at.format("%Y-%m-%d %H:%M:%S").to_string(),
        }
    }
}
