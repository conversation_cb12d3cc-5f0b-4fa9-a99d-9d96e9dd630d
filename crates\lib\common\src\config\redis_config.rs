use serde::Deserialize;

/// Redis 配置结构体
#[derive(Debug, Deserialize, Clone)]
pub struct Redis {
    // 或者其他能代表整个配置的名称
    pub redis_config: RedisConfig, // 这个字段名必须是 'spring'，对应 YAML 的根字段
}

#[derive(Debug, Deserialize, Clone)]
pub struct RedisConfig {
    pub host: String,
    pub port: i32,
    pub password: String,
    /// 最大连接数
    #[serde(default = "default_max_connections")]
    pub max_connections: u32,
    /// 最小空闲连接数
    #[serde(default = "default_min_connections")]
    pub min_connections: u32,
    /// 连接超时时间（秒）
    #[serde(default = "default_connect_timeout")]
    pub connect_timeout: u64,
    /// 空闲超时时间（秒）
    #[serde(default = "default_idle_timeout")]
    pub idle_timeout: u64,
}

fn default_max_connections() -> u32 { 20 }
fn default_min_connections() -> u32 { 5 }
fn default_connect_timeout() -> u64 { 5 }
fn default_idle_timeout() -> u64 { 300 }

impl RedisConfig {
    pub fn from_env() -> Self {
        RedisConfig {
            host: std::env::var("REDIS_HOST").unwrap_or_else(|_| "***********".to_string()),
            port: std::env::var("REDIS_PORT").unwrap_or_else(|_| "16379".to_string()).parse().unwrap(),
            password: std::env::var("REDIS_PASSWORD").unwrap_or_else(|_| "123456".to_string()),
            max_connections: std::env::var("REDIS_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "20".to_string())
                .parse()
                .unwrap_or(20),
            min_connections: std::env::var("REDIS_MIN_CONNECTIONS")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            connect_timeout: std::env::var("REDIS_CONNECT_TIMEOUT")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            idle_timeout: std::env::var("REDIS_IDLE_TIMEOUT")
                .unwrap_or_else(|_| "300".to_string())
                .parse()
                .unwrap_or(300),
        }
    }
}