[package]
name = "exchange"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true

[dependencies]
# 异步支持
async-trait = "0.1"
tokio = { workspace = true, features = ["full"] }

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }

# 序列化
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true

# 时间处理
chrono = { workspace = true, features = ["serde"] }

# 数值处理
rust_decimal = { workspace = true, features = ["serde"] }

# 错误处理
anyhow.workspace = true

# 日志
log.workspace = true 