use sqlx::{PgPool, Pool, Postgres};
use sqlx::postgres::PgPoolOptions;
use crate::{Result, RepositoryError};
use super::{DatabaseConfig, PoolStatus};

/// PostgreSQL连接池管理器
#[derive(Clone)]
pub struct PostgresPoolManager {
    pool: PgPool,
    config: DatabaseConfig,
}

impl PostgresPoolManager {
    /// 创建新的连接池管理器
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        let pool = PgPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .acquire_timeout(std::time::Duration::from_secs(config.connect_timeout))
            .idle_timeout(Some(std::time::Duration::from_secs(config.idle_timeout)))
            .connect(&config.connection_string())
            .await?;

        Ok(Self { pool, config })
    }

    /// 获取连接池引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 获取连接池状态
    pub fn status(&self) -> PoolStatus {
        let size = self.pool.size() as u32;
        PoolStatus {
            active_connections: size,
            idle_connections: 0, // SQLx不直接提供idle_size方法
            total_connections: size,
            is_healthy: !self.pool.is_closed(),
        }
    }

    /// 测试连接是否正常
    pub async fn test_connection(&self) -> Result<()> {
        sqlx::query("SELECT 1")
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    /// 关闭连接池
    pub async fn close(&self) {
        self.pool.close().await;
    }

    /// 获取配置信息
    pub fn config(&self) -> &DatabaseConfig {
        &self.config
    }
} 