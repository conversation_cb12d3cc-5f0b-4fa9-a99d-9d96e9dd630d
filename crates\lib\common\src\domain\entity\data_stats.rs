use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 数据统计实体
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct DataStatsEntity {
    pub total_records: i64,
    pub earliest_time: Option<DateTime<Utc>>,
    pub latest_time: Option<DateTime<Utc>>,
}

/// 数据时间范围实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TimeRangeEntity {
    pub min_time: Option<DateTime<Utc>>,
    pub max_time: Option<DateTime<Utc>>,
}

/// 缺失数据时间桶实体
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TimeBucketEntity {
    pub minute_bucket: DateTime<Utc>,
    pub data_count: Option<i64>,
} 