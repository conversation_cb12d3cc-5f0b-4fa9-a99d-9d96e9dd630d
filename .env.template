# 市场数据维护服务环境变量模板
# 复制此文件为 .env 并根据实际环境修改配置

# =============================================================================
# 基础配置
# =============================================================================

# 环境类型 (development|testing|staging|production)
ENVIRONMENT=development

# 服务配置
SERVICE_NAME=market-data-maintenance
SERVICE_VERSION=1.0.0
STARTUP_MODE=standard

# 日志配置
LOG_LEVEL=info
RUST_LOG=info

# =============================================================================
# 数据库配置
# =============================================================================

# TimescaleDB配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=market_data
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_CONNECT_TIMEOUT=30
DB_IDLE_TIMEOUT=600

# 数据库URL (优先级高于单独配置)
DATABASE_URL=postgresql://postgres:password@localhost:5432/market_data

# =============================================================================
# Redis配置
# =============================================================================

# Redis连接配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=10
REDIS_MIN_CONNECTIONS=3

# Redis URL (优先级高于单独配置)
REDIS_URL=redis://localhost:6379

# =============================================================================
# Nacos配置中心
# =============================================================================

# Nacos服务器配置
NACOS_SERVER_ADDR=localhost:8848
NACOS_NAMESPACE=public
NACOS_GROUP=DEFAULT_GROUP
NACOS_USERNAME=nacos
NACOS_PASSWORD=nacos
NACOS_SERVICE_NAME=market-data-maintenance

# =============================================================================
# 服务配置
# =============================================================================

# 网络配置
LISTEN_ADDR=0.0.0.0:8091
GRPC_SERVER_HOST=0.0.0.0
GRPC_SERVER_PORT=50051

# 维护服务配置
FULL_SCAN_INTERVAL_HOURS=24
INCREMENTAL_SCAN_INTERVAL_MINUTES=5
CACHE_SYNC_INTERVAL_SECONDS=60

# 批处理配置
BATCH_SIZE=1000
MAX_CONCURRENT_SYMBOLS=10

# =============================================================================
# 交易所API配置
# =============================================================================

# Binance API配置
BINANCE_API_URL=https://api.binance.com
BINANCE_API_KEY=
BINANCE_SECRET_KEY=
BINANCE_RATE_LIMIT=1200

# OKX API配置
OKX_API_URL=https://www.okx.com
OKX_API_KEY=
OKX_SECRET_KEY=
OKX_PASSPHRASE=
OKX_RATE_LIMIT=600

# =============================================================================
# 监控和告警配置
# =============================================================================

# Prometheus配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
METRICS_INTERVAL_SECONDS=60

# 告警配置
ALERTS_ENABLED=true
GAP_DETECTION_THRESHOLD=10
SYNC_DELAY_THRESHOLD_MINUTES=10
ERROR_RATE_THRESHOLD=0.05

# 健康检查配置
HEALTH_CHECK_INTERVAL_SECONDS=60
HEALTH_CHECK_TIMEOUT_SECONDS=10

# =============================================================================
# 性能调优配置
# =============================================================================

# 线程池配置
WORKER_THREADS=
BLOCKING_THREADS=

# 内存配置
MEMORY_LIMIT_MB=
ENABLE_COMPRESSION=true

# 缓存配置
MAX_CACHE_SIZE_MB=1024
CACHE_TTL_HOURS=24

# =============================================================================
# 安全配置
# =============================================================================

# JWT配置
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRATION_HOURS=24

# API密钥配置
API_KEY_HEADER=X-API-Key
API_KEY_VALUE=your-api-key-here

# =============================================================================
# 开发和调试配置
# =============================================================================

# 调试配置
DEBUG_MODE=false
ENABLE_PROFILING=false
PROFILING_PORT=6060

# 测试配置
ENABLE_TEST_DATA=false
TEST_DATA_SYMBOLS=BTCUSDT,ETHUSDT
TEST_DATA_INTERVALS=1m,5m,1h

# Mock配置
MOCK_EXCHANGE_API=false
MOCK_DATABASE=false
MOCK_REDIS=false

# =============================================================================
# Docker和容器化配置
# =============================================================================

# Docker网络配置
DOCKER_NETWORK=market-data-network
DOCKER_SUBNET=172.20.0.0/16

# 容器资源限制
CONTAINER_MEMORY_LIMIT=2g
CONTAINER_CPU_LIMIT=2

# =============================================================================
# 文件路径配置
# =============================================================================

# 配置文件路径
CONFIG_DIR=./config
CONFIG_FILE=

# 日志文件路径
LOG_DIR=./logs
LOG_FILE=market_data_maintenance.log

# 数据文件路径
DATA_DIR=./data
BACKUP_DIR=./backups

# =============================================================================
# 特定环境配置示例
# =============================================================================

# 开发环境示例
# ENVIRONMENT=development
# LOG_LEVEL=debug
# FULL_SCAN_INTERVAL_HOURS=1
# INCREMENTAL_SCAN_INTERVAL_MINUTES=2
# ALERTS_ENABLED=false

# 测试环境示例
# ENVIRONMENT=testing
# LOG_LEVEL=info
# ENABLE_AUTO_REPAIR=false
# ALERTS_ENABLED=false

# 生产环境示例
# ENVIRONMENT=production
# LOG_LEVEL=warn
# FULL_SCAN_INTERVAL_HOURS=24
# INCREMENTAL_SCAN_INTERVAL_MINUTES=5
# ALERTS_ENABLED=true
# ENABLE_COMPRESSION=true
