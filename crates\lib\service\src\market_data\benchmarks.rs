#[cfg(test)]
mod benchmarks {
    use super::*;
    use crate::market_data::{
        enhanced_config::EnhancedMaintenanceConfig,
        shared_state::ServiceStateManager,
        gap_detector::GapDetectionConfig,
    };
    use std::time::{Duration, Instant};
    use tokio;

    /// 性能基准测试结果
    #[derive(Debug)]
    struct BenchmarkResult {
        pub test_name: String,
        pub duration: Duration,
        pub operations_per_second: f64,
        pub memory_usage_mb: Option<f64>,
    }

    impl BenchmarkResult {
        fn new(test_name: &str, duration: Duration, operations: u64) -> Self {
            let ops_per_sec = operations as f64 / duration.as_secs_f64();
            Self {
                test_name: test_name.to_string(),
                duration,
                operations_per_second: ops_per_sec,
                memory_usage_mb: None,
            }
        }

        fn print(&self) {
            println!("📊 基准测试: {}", self.test_name);
            println!("   ⏱️  耗时: {:?}", self.duration);
            println!("   🚀 操作/秒: {:.2}", self.operations_per_second);
            if let Some(memory) = self.memory_usage_mb {
                println!("   💾 内存使用: {:.2} MB", memory);
            }
            println!();
        }
    }

    #[tokio::test]
    async fn benchmark_state_manager_updates() {
        let state_manager = ServiceStateManager::new();
        let operations = 10000u64;
        
        println!("🔥 开始状态管理器更新基准测试...");
        
        let start = Instant::now();
        
        for i in 0..operations {
            state_manager.update_maintenance_progress(
                operations as u32,
                i as u32,
                "benchmark",
                None
            ).await;
        }
        
        let duration = start.elapsed();
        let result = BenchmarkResult::new("状态管理器更新", duration, operations);
        result.print();
        
        // 验证性能要求
        assert!(result.operations_per_second > 1000.0, 
                "状态更新性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    #[tokio::test]
    async fn benchmark_concurrent_state_access() {
        let state_manager = ServiceStateManager::new();
        let concurrent_tasks = 100;
        let operations_per_task = 100;
        
        println!("🔥 开始并发状态访问基准测试...");
        
        let start = Instant::now();
        
        let handles: Vec<_> = (0..concurrent_tasks).map(|task_id| {
            let sm = state_manager.clone();
            tokio::spawn(async move {
                for i in 0..operations_per_task {
                    sm.update_maintenance_progress(
                        (concurrent_tasks * operations_per_task) as u32,
                        (task_id * operations_per_task + i) as u32,
                        "benchmark",
                        None
                    ).await;
                    sm.get_state().await;
                }
            })
        }).collect();
        
        for handle in handles {
            handle.await.unwrap();
        }
        
        let duration = start.elapsed();
        let total_operations = (concurrent_tasks * operations_per_task * 2) as u64; // 更新 + 读取
        let result = BenchmarkResult::new("并发状态访问", duration, total_operations);
        result.print();
        
        // 验证并发性能
        assert!(result.operations_per_second > 500.0,
                "并发访问性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    #[tokio::test]
    async fn benchmark_config_serialization() {
        let config = EnhancedMaintenanceConfig::default();
        let iterations = 1000u64;
        
        println!("🔥 开始配置序列化基准测试...");
        
        // TOML序列化基准
        let start = Instant::now();
        for _ in 0..iterations {
            let _toml_str = toml::to_string_pretty(&config).unwrap();
        }
        let toml_serialize_duration = start.elapsed();
        
        // TOML反序列化基准
        let toml_str = toml::to_string_pretty(&config).unwrap();
        let start = Instant::now();
        for _ in 0..iterations {
            let _: EnhancedMaintenanceConfig = toml::from_str(&toml_str).unwrap();
        }
        let toml_deserialize_duration = start.elapsed();
        
        // JSON序列化基准
        let start = Instant::now();
        for _ in 0..iterations {
            let _json_str = serde_json::to_string_pretty(&config).unwrap();
        }
        let json_serialize_duration = start.elapsed();
        
        // JSON反序列化基准
        let json_str = serde_json::to_string_pretty(&config).unwrap();
        let start = Instant::now();
        for _ in 0..iterations {
            let _: EnhancedMaintenanceConfig = serde_json::from_str(&json_str).unwrap();
        }
        let json_deserialize_duration = start.elapsed();
        
        // 输出结果
        BenchmarkResult::new("TOML序列化", toml_serialize_duration, iterations).print();
        BenchmarkResult::new("TOML反序列化", toml_deserialize_duration, iterations).print();
        BenchmarkResult::new("JSON序列化", json_serialize_duration, iterations).print();
        BenchmarkResult::new("JSON反序列化", json_deserialize_duration, iterations).print();
        
        // 验证序列化性能
        assert!(toml_serialize_duration.as_millis() < 1000, "TOML序列化太慢");
        assert!(json_serialize_duration.as_millis() < 500, "JSON序列化太慢");
    }

    #[tokio::test]
    async fn benchmark_config_validation() {
        let iterations = 10000u64;
        
        println!("🔥 开始配置验证基准测试...");
        
        let start = Instant::now();
        
        for _ in 0..iterations {
            let config = EnhancedMaintenanceConfig::default();
            config.validate().unwrap();
        }
        
        let duration = start.elapsed();
        let result = BenchmarkResult::new("配置验证", duration, iterations);
        result.print();
        
        // 验证配置验证性能
        assert!(result.operations_per_second > 5000.0,
                "配置验证性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    #[tokio::test]
    async fn benchmark_gap_detection_config() {
        let iterations = 5000u64;
        
        println!("🔥 开始缺口检测配置基准测试...");
        
        let start = Instant::now();
        
        for i in 0..iterations {
            let config = GapDetectionConfig {
                intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
                batch_window_days: 7,
                max_concurrent_symbols: 10,
                enable_smart_sampling: i % 2 == 0,
                sampling_rate: (i as f64 % 100.0) / 100.0,
            };
            
            // 模拟配置使用
            let _interval_count = config.intervals.len();
            let _is_sampling = config.enable_smart_sampling;
        }
        
        let duration = start.elapsed();
        let result = BenchmarkResult::new("缺口检测配置创建", duration, iterations);
        result.print();
        
        // 验证配置创建性能
        assert!(result.operations_per_second > 10000.0,
                "配置创建性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    #[tokio::test]
    async fn benchmark_memory_usage_pattern() {
        println!("🔥 开始内存使用模式基准测试...");
        
        let state_manager = ServiceStateManager::new();
        let iterations = 1000;
        
        // 测试内存使用模式
        let start = Instant::now();
        
        for batch in 0..10 {
            // 批量更新
            for i in 0..iterations {
                state_manager.update_maintenance_progress(
                    (10 * iterations) as u32,
                    (batch * iterations + i) as u32,
                    "memory_test",
                    None
                ).await;
                state_manager.update_sync_stats(
                    ((batch * iterations + i) * 10) as u64,
                    (batch * iterations + i) as u32,
                    ((batch * iterations + i) * 100) as u64
                ).await;
            }
            
            // 模拟内存压力
            let _state = state_manager.get_state().await;
            
            // 短暂休息模拟实际工作负载
            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        }
        
        let duration = start.elapsed();
        let total_operations = (10 * iterations * 2) as u64; // 更新操作 * 2种类型
        let result = BenchmarkResult::new("内存使用模式", duration, total_operations);
        result.print();
        
        // 验证内存使用效率
        assert!(duration.as_millis() < 5000, "内存使用模式测试耗时过长");
    }

    #[test]
    fn benchmark_config_environment_adaptation() {
        let iterations = 1000u64;
        let environments = [
            crate::market_data::enhanced_config::Environment::Development,
            crate::market_data::enhanced_config::Environment::Testing,
            crate::market_data::enhanced_config::Environment::Staging,
            crate::market_data::enhanced_config::Environment::Production,
        ];
        
        println!("🔥 开始环境适配基准测试...");
        
        let start = Instant::now();
        
        for _ in 0..iterations {
            for env in &environments {
                let mut config = EnhancedMaintenanceConfig::default();
                config.environment = env.clone();
                config.adjust_for_environment();
                
                // 验证适配结果
                match env {
                    crate::market_data::enhanced_config::Environment::Development => {
                        assert_eq!(config.maintenance.full_scan_interval_hours, 1);
                    }
                    crate::market_data::enhanced_config::Environment::Production => {
                        assert_eq!(config.gap_detection.sampling_rate, 0.05);
                    }
                    _ => {}
                }
            }
        }
        
        let duration = start.elapsed();
        let total_operations = iterations * environments.len() as u64;
        let result = BenchmarkResult::new("环境适配", duration, total_operations);
        result.print();
        
        // 验证环境适配性能
        assert!(result.operations_per_second > 1000.0,
                "环境适配性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    #[tokio::test]
    async fn benchmark_stress_test() {
        println!("🔥 开始压力测试基准...");
        
        let state_manager = ServiceStateManager::new();
        let concurrent_tasks = 50;
        let operations_per_task = 200;
        
        let start = Instant::now();
        
        let handles: Vec<_> = (0..concurrent_tasks).map(|task_id| {
            let sm = state_manager.clone();
            tokio::spawn(async move {
                for i in 0..operations_per_task {
                    // 混合操作模拟真实负载
                    sm.update_maintenance_progress(
                        (concurrent_tasks * operations_per_task) as u32,
                        (task_id * operations_per_task + i) as u32,
                        "stress_test",
                        None
                    ).await;

                    if i % 10 == 0 {
                        sm.update_sync_stats((i * 10) as u64, i as u32, (i * 100) as u64).await;
                    }
                    
                    if i % 20 == 0 {
                        let _state = sm.get_state().await;
                    }
                    
                    if i % 50 == 0 {
                        sm.set_maintenance_running(i % 100 < 50).await;
                    }
                }
            })
        }).collect();
        
        for handle in handles {
            handle.await.unwrap();
        }
        
        let duration = start.elapsed();
        let total_operations = (concurrent_tasks * operations_per_task) as u64;
        let result = BenchmarkResult::new("压力测试", duration, total_operations);
        result.print();
        
        // 验证系统在压力下的稳定性
        let final_state = state_manager.get_state().await;
        assert!(final_state.maintenance.progress.processed_symbols >= 0);
        assert!(final_state.cache_sync.sync_stats.synced_records >= 0);
        
        // 性能要求
        assert!(result.operations_per_second > 100.0,
                "压力测试性能不足: {:.2} ops/sec", result.operations_per_second);
    }

    /// 运行所有基准测试的辅助函数
    #[test]
    fn run_all_benchmarks() {
        println!("🚀 开始运行所有基准测试...");
        println!("{}", "=".repeat(60));

        // 注意：这个函数仅用于演示，实际的基准测试应该单独运行
        println!("📝 请单独运行各个基准测试:");
        println!("   cargo test benchmark_state_manager_updates");
        println!("   cargo test benchmark_concurrent_state_access");
        println!("   cargo test benchmark_memory_usage_pattern");
        println!("   cargo test benchmark_stress_test");
        println!("   cargo test benchmark_config_serialization");
        println!("   cargo test benchmark_config_validation");
        println!("   cargo test benchmark_gap_detection_config");
        println!("   cargo test benchmark_config_environment_adaptation");

        println!("✅ 基准测试指南显示完成!");
        println!("{}", "=".repeat(60));
    }
}
