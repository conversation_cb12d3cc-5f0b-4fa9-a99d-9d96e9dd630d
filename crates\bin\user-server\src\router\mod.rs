use std::sync::Arc;
use firebase_auth::FirebaseAuth;
// 移除旧的DataMaintenanceService依赖
// use service::data_service::DataMaintenanceService;
use service::AppState as ServiceAppState;
use axum::extract::State;

pub mod router;
pub mod auth_jwt;
pub mod auth_extractor;
pub mod auth_firebase;

/// user-server特定的应用状态，组合了service层的通用状态和user-server特有的服务
#[derive(Clone)]
pub struct UserServerState {
    /// Service层的通用应用状态（包含auth_service等）
    pub service_state: ServiceAppState,
    /// Firebase认证服务（user-server特有）
    pub firebase_auth: FirebaseAuth,
    // 移除maintenance_service字段，维护服务现在在独立线程中运行
    // pub maintenance_service: Arc<DataMaintenanceService>,
}

impl UserServerState {
    /// 便捷方法：获取认证服务
    pub fn auth_service(&self) -> &Arc<service::user_service::auth::AuthService> {
        &self.service_state.auth_service
    }
    
    /// 便捷方法：获取市场数据管理器
    pub fn market_data_manager(&self) -> &Arc<service::market_data::KlineMaintenanceServiceV2> {
        &self.service_state.market_data_manager
    }
    
    /// 便捷方法：获取Redis操作接口
    pub fn redis_ops(&self) -> &Arc<repository::connection::redis::RedisOperations> {
        &self.service_state.redis_ops
    }
}

/// 为了向后兼容，保留AppState别名
pub type AppState = UserServerState;