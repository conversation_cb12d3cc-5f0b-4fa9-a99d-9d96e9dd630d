use std::sync::Arc;
use firebase_auth::FirebaseAuth;
// 移除旧的DataMaintenanceService依赖
// use service::data_service::DataMaintenanceService;
use service::AppState as ServiceAppState;
use axum::extract::State;
use service::market_data::MarketDataCrudService;

pub mod router;
pub mod auth_jwt;
pub mod auth_extractor;
pub mod auth_firebase;

/// user-server特定的应用状态，组合了service层的通用状态和user-server特有的服务
#[derive(Clone)]
pub struct UserServerState {
    /// Service层的通用应用状态（包含auth_service等）
    pub service_state: ServiceAppState,
    /// Firebase认证服务（user-server特有）
    pub firebase_auth: FirebaseAuth,
    /// 市场数据服务（user-server特有）
    pub market_data_service: Arc<MarketDataCrudService>,
    // 移除maintenance_service字段，维护服务现在在独立线程中运行
    // pub maintenance_service: Arc<DataMaintenanceService>,
}

impl UserServerState {
    /// 便捷方法：获取认证服务
    pub fn auth_service(&self) -> &Arc<service::user_service::auth::AuthService> {
        &self.service_state.auth_service
    }
}

/// 为了向后兼容，保留AppState别名
pub type AppState = UserServerState;