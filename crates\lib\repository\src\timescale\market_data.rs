// 市场数据聚合Repository - 组合所有表的操作
// 提供统一的市场数据访问接口

// 重新导出所有分离后的类型和函数
pub use super::klines::*;
pub use super::tickers::*;
pub use super::order_books::*;
pub use super::stats_24hr::*;
pub use super::trades::{TradeRepository, TradeEntity, CreateTradeRequest, TradeQuery, TradeStats};

use sqlx::PgPool;
use chrono::{DateTime, Utc};
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 综合的市场数据Repository - 组合了各个表的操作
/// 提供统一的接口来访问所有市场数据相关的表
#[derive(Clone)]
pub struct MarketDataRepository {
    /// K线数据操作
    pub klines: KlineRepository,
    /// 价格数据操作
    pub tickers: TickerRepository,
    /// 订单簿数据操作
    pub order_books: OrderBookRepository,
    /// 24小时统计数据操作
    pub stats_24hr: Stats24hrRepository,
    /// 交易数据操作
    pub trades: TradeRepository,
    /// 数据库连接池
    pool: PgPool,
}

impl MarketDataRepository {
    /// 创建新的市场数据Repository实例
    pub fn new(pool: PgPool) -> Self {
        Self {
            klines: KlineRepository::new(pool.clone()),
            tickers: TickerRepository::new(pool.clone()),
            order_books: OrderBookRepository::new(pool.clone()),
            stats_24hr: Stats24hrRepository::new(pool.clone()),
            trades: TradeRepository::new(pool.clone()),
            pool,
        }
    }

    /// 初始化所有TimescaleDB表结构
    /// 按照依赖关系顺序创建表和超表
    pub async fn initialize_tables(&self) -> Result<()> {
        log::info!("开始初始化所有市场数据表结构...");
        
        // 逐个初始化各个表
        self.klines.initialize_table().await?;
        log::info!("K线表初始化完成");
        
        self.tickers.initialize_table().await?;
        log::info!("价格表初始化完成");
        
        self.order_books.initialize_table().await?;
        log::info!("订单簿表初始化完成");
        
        self.stats_24hr.initialize_table().await?;
        log::info!("24小时统计表初始化完成");
        
        self.trades.initialize_table().await?;
        log::info!("交易表初始化完成");
        
        log::info!("所有TimescaleDB表结构初始化完成");
        Ok(())
    }

    /// 整体健康检查 - 检查所有表的可用性
    pub async fn health_check(&self) -> Result<()> {
        log::debug!("开始执行市场数据整体健康检查...");
        
        // 检查各个Repository的健康状态
        self.klines.health_check().await?;
        self.tickers.health_check().await?;
        self.order_books.health_check().await?;
        self.stats_24hr.health_check().await?;
        self.trades.health_check().await?;
        
        log::debug!("市场数据整体健康检查通过");
        Ok(())
    }

    /// 获取数据库连接池引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 批量清理所有表的历史数据
    /// 
    /// # Arguments
    /// * `retention_days` - 保留天数，超过此天数的数据将被清理
    pub async fn cleanup_all_old_data(&self, retention_days: i32) -> Result<()> {
        log::info!("开始清理所有表 {} 天前的历史数据...", retention_days);
        
        // 计算截止时间
        let before_time = chrono::Utc::now() - chrono::Duration::days(retention_days as i64);
        
        // 并行清理各个表的历史数据
        let results = tokio::try_join!(
            self.klines.cleanup_old_data(before_time),
            self.tickers.cleanup_old_data(before_time),
            self.order_books.cleanup_old_data(before_time),
            self.stats_24hr.cleanup_old_data(before_time),
            self.trades.cleanup_old_data(before_time),
        );
        
        match results {
            Ok(_) => {
                log::info!("所有表的历史数据清理完成");
                Ok(())
            }
            Err(e) => {
                log::error!("清理历史数据时发生错误: {}", e);
                Err(e)
            }
        }
    }

    /// 获取所有表的活跃交易对
    pub async fn get_all_active_symbols(&self) -> Result<Vec<String>> {
        // 默认查询24小时内的活跃交易对
        let within_duration = chrono::Duration::hours(24);
        
        // 从各个表中获取活跃交易对，然后合并去重
        let results = tokio::try_join!(
            self.klines.get_active_symbols(within_duration),
            self.tickers.get_active_symbols(within_duration),
            self.order_books.get_active_symbols(within_duration),
            self.stats_24hr.get_active_symbols(within_duration),
            self.trades.get_active_symbols(within_duration),
        )?;

        let mut all_symbols = std::collections::HashSet::new();
        for symbols in [results.0, results.1, results.2, results.3, results.4] {
            all_symbols.extend(symbols);
        }

        let mut sorted_symbols: Vec<String> = all_symbols.into_iter().collect();
        sorted_symbols.sort();
        
        Ok(sorted_symbols)
    }
} 