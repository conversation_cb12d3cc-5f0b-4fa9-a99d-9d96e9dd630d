version: '3.8'

# 开发环境配置
# 使用: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # 市场数据维护服务 - 开发配置
  market-data-maintenance:
    environment:
      - ENVIRONMENT=development
      - RUST_LOG=debug
      - STARTUP_MODE=fast
      - FULL_SCAN_INTERVAL_HOURS=1
      - INCREMENTAL_SCAN_INTERVAL_MINUTES=2
      - CACHE_SYNC_INTERVAL_SECONDS=30
    volumes:
      - ./config/development.toml:/app/config/config.toml:ro
      - ./target/debug:/app/bin  # 使用debug构建
    ports:
      - "8091:8091"
      - "50051:50051"
      - "9000:9000"  # 调试端口
    command: ["./bin/user-server", "--config", "/app/config/config.toml"]

  # TimescaleDB - 开发配置
  timescaledb:
    environment:
      - POSTGRES_DB=market_data_dev
      - POSTGRES_USER=dev_user
      - POSTGRES_PASSWORD=dev_password
    ports:
      - "5433:5432"  # 避免与本地PostgreSQL冲突
    volumes:
      - timescaledb_dev_data:/var/lib/postgresql/data
      - ./scripts/dev_init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro

  # Redis - 开发配置
  redis:
    command: redis-server --appendonly yes --requirepass dev_password
    ports:
      - "6380:6379"  # 避免与本地Redis冲突
    volumes:
      - redis_dev_data:/data

  # Nacos - 开发配置
  nacos:
    environment:
      - MODE=standalone
      - NACOS_AUTH_ENABLE=false  # 开发环境禁用认证
    ports:
      - "8849:8848"  # 避免端口冲突

  # 开发工具容器
  dev-tools:
    image: alpine:latest
    container_name: dev-tools
    volumes:
      - .:/workspace
      - /var/run/docker.sock:/var/run/docker.sock
    working_dir: /workspace
    command: tail -f /dev/null  # 保持容器运行
    networks:
      - market-data-network

  # 数据库管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - timescaledb
    networks:
      - market-data-network

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379:0:dev_password
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - market-data-network

  # 日志聚合
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: fluentd
    restart: unless-stopped
    volumes:
      - ./config/fluentd:/fluentd/etc:ro
      - ./logs:/var/log/app
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    networks:
      - market-data-network

  # 测试数据生成器
  data-generator:
    build:
      context: .
      dockerfile: Dockerfile.data-generator
    container_name: data-generator
    restart: "no"
    environment:
      - TARGET_SERVICE=market-data-maintenance:8091
      - GENERATION_RATE=100  # 每秒生成的数据点数
      - SYMBOLS=BTCUSDT,ETHUSDT,BNBUSDT
    depends_on:
      - market-data-maintenance
    networks:
      - market-data-network
    profiles:
      - testing  # 只在测试时启动

volumes:
  timescaledb_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local

# 开发环境特定的网络配置
networks:
  market-data-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
