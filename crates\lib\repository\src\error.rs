/// Repository层错误类型 - 简化版
#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Redis error: {0}")]
    Redis(#[from] r2d2_redis::redis::RedisError),
    
    #[error("Connection error: {0}")]
    Connection(String),
    
    #[error("Query error: {0}")]
    Query(String),
    
    #[error("Serialization error: {0}")]
    Serialization(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Not found")]
    NotFound,
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("Timeout")]
    Timeout,
    
    #[error("Internal error: {0}")]
    Internal(String),
}

impl RepositoryError {
    /// 创建连接错误
    pub fn connection<T: std::fmt::Display>(msg: T) -> Self {
        Self::Connection(msg.to_string())
    }
    
    /// 创建查询错误
    pub fn query<T: std::fmt::Display>(msg: T) -> Self {
        Self::Query(msg.to_string())
    }
    
    /// 创建序列化错误
    pub fn serialization<T: std::fmt::Display>(msg: T) -> Self {
        Self::Serialization(msg.to_string())
    }
    
    /// 创建配置错误
    pub fn configuration<T: std::fmt::Display>(msg: T) -> Self {
        Self::Configuration(msg.to_string())
    }
    
    /// 创建冲突错误
    pub fn conflict<T: std::fmt::Display>(msg: T) -> Self {
        Self::Conflict(msg.to_string())
    }
    
    /// 创建内部错误
    pub fn internal<T: std::fmt::Display>(msg: T) -> Self {
        Self::Internal(msg.to_string())
    }
}

/// Repository层结果类型
pub type Result<T> = std::result::Result<T, RepositoryError>; 