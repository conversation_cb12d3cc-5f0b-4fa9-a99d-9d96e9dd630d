use serde::Deserialize;


/// 数据库连接池配置
#[derive(Debug, <PERSON><PERSON>,Deserialize)]
pub struct DatabaseConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: u64,
    pub idle_timeout: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            host: "localhost".to_string(),
            port: 5432,
            username: "postgres".to_string(),
            password: "password".to_string(),
            database: "market_data".to_string(),
            max_connections: 20,
            min_connections: 5,
            connect_timeout: 30,
            idle_timeout: 600,
        }
    }
}

impl DatabaseConfig {
    /// 从环境变量创建配置
    pub fn from_env() -> Self {
        Self {
            host: std::env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string()),
            port: std::env::var("DB_PORT").unwrap_or_else(|_| "5432".to_string()).parse().unwrap_or(5432),
            username: std::env::var("DB_USERNAME").unwrap_or_else(|_| "postgres".to_string()),
            password: std::env::var("DB_PASSWORD").unwrap_or_else(|_| "password".to_string()),
            database: std::env::var("DB_DATABASE").unwrap_or_else(|_| "market_data".to_string()),
            max_connections: std::env::var("DB_MAX_CONNECTIONS").unwrap_or_else(|_| "20".to_string()).parse().unwrap_or(20),
            min_connections: std::env::var("DB_MIN_CONNECTIONS").unwrap_or_else(|_| "5".to_string()).parse().unwrap_or(5),
            connect_timeout: std::env::var("DB_CONNECT_TIMEOUT").unwrap_or_else(|_| "30".to_string()).parse().unwrap_or(30),
            idle_timeout: std::env::var("DB_IDLE_TIMEOUT").unwrap_or_else(|_| "600".to_string()).parse().unwrap_or(600),
        }
    }

    /// 构建数据库连接字符串
    pub fn connection_string(&self) -> String {
        format!("postgres://{}:{}@{}:{}/{}", self.username, self.password, self.host, self.port, self.database)
    }
}

