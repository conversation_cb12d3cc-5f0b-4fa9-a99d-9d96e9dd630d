use serde::Deserialize;

#[derive(Debug, Deserialize, Clone)]
pub struct KafkaSpring {
    // 或者其他能代表整个配置的名称
    pub spring: KafkaConfig, // 这个字段名必须是 'spring'，对应 YAML 的根字段
}

// 对应顶层的 'spring'
#[derive(Debug, Default, Deserialize, Clone)] // 添加 Debug 和 Clone，方便打印和使用
pub struct KafkaConfig {
    pub kafka: Kafka,
}

// 对应 'spring.kafka'
#[derive(Debug, Deserialize, Clone, Default)]
pub struct Kafka {
    #[serde(rename = "bootstrap-servers")]
    pub bootstrap_servers: String,
    pub producer: KafkaProducerConfig,
    pub consumer: KafkaConsumerConfig,
    pub listener: KafkaListenerConfig,
}

#[derive(Debug, Deserialize, Clone, Default)]
pub struct KafkaProducerConfig {
    pub retries: u64, // 或 i32
    pub acks: String, // "-1" 是字符串
    #[serde(rename = "batch-size")]
    pub batch_size: u64, // 或 usize/i32/u64
    // 注意这里的 properties.linger.ms，在 YAML 中是 producer 下的一个 properties 块里的键
    #[serde(rename = "properties.linger.ms")]
    pub properties: u64,
    #[serde(rename = "buffer-memory")]
    pub buffer_memory: u64, // 内存大小通常用 u64/i64
}

// 对应 'spring.kafka.producer.properties'
#[derive(Debug, Deserialize, Clone)]
pub struct KafkaProducerProperties {
    #[serde(rename = "linger.ms")]
    pub linger_ms: u64, // 毫秒通常用 u64/i64
}

// 对应 'spring.kafka.consumer'
#[derive(Debug, Deserialize, Clone, Default)]
pub struct KafkaConsumerConfig {
    #[serde(rename = "enable-auto-commit")]
    pub enable_auto_commit: bool,
    #[serde(rename = "max-poll-records")]
    pub max_poll_records: u32, // 或 usize/i32
    #[serde(rename = "auto-commit-interval")]
    pub auto_commit_interval: u64, // 毫秒通常用 u64/i64
    #[serde(rename = "group-id")]
    pub group_id: String,
    #[serde(rename = "auto-offset-reset")]
    pub auto_offset_reset: String,

    // 对应 'spring.kafka.consumer.properties'
    pub properties: KafkaConsumerProperties,
}

// 对应 'spring.kafka.consumer.properties'
#[derive(Debug, Deserialize, Clone, Default)]
pub struct KafkaConsumerProperties {
    #[serde(rename = "session.timeout.ms")]
    pub session_timeout_ms: u64, // 毫秒通常用 u64/i64
    #[serde(rename = "request.timeout.ms")]
    pub request_timeout_ms: u64, // 毫秒通常用 u64/i64
}

// 对应 'spring.kafka.listener'
#[derive(Debug, Deserialize, Clone, Default)]
pub struct KafkaListenerConfig {
    #[serde(rename = "batch-listener")]
    pub batch_listener: bool,
    pub concurrency: u32, // 或 usize/i32
}
