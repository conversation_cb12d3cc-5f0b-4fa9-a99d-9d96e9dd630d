# 数字货币交易系统数据行情服务器

本项目是一个基于Rust的数字货币交易系统，提供多种数据服务和交易功能，包括高性能的市场数据查询、交易接口、回测功能等。

## 项目架构

```
market_data_server/
├── crates/
│   ├── bin/
│   │   ├── data-grpc/          # gRPC服务器主程序
│   │   ├── data-http/          # HTTP API服务器 (Trade API等)
│   │   ├── data-api/           # RESTful API服务器 (Market Data API)
│   │   └── client-grpc/        # gRPC客户端测试程序
│   ├── lib/
│   │   ├── common/             # 通用库（domain、工具类等）
│   │   ├── service/            # 业务服务层（数据库操作等）
│   │   └── nacos/              # 服务注册发现
│   └── exchange/
│       └── binance_api/        # Binance交易所API接口
├── proto/                      # gRPC协议定义文件
├── web/my-vite-app/           # React前端测试应用
├── doc/                       # 项目文档
└── docker-compose*.yaml      # Docker部署配置
```

## 服务架构

### 1. 数据服务层
- **data-grpc**: 高性能gRPC服务，提供市场数据查询
- **data-api**: RESTful API服务，标准HTTP接口
- **data-http**: HTTP服务，主要提供交易相关接口

### 2. 交易服务层
- **Trade API**: 提供订单查询、回测任务管理、持仓查询等功能
- **统一响应格式**: 采用ResponseBuilder模式，确保前后端数据一致性

### 3. 前端测试层
- **React应用**: 提供交易接口的测试控制台
- **CORS支持**: 完整的跨域配置和代理设置

## 功能特性

- **多协议支持**: 同时支持gRPC和HTTP/RESTful API
- **统一响应格式**: 采用ResponseBuilder模式，支持灵活的状态码和消息管理
- **多交易所支持**: 封装各大交易所API接口，目前支持Binance
- **完整的市场数据**: 提供K线、价格、深度、统计、交易历史等数据
- **交易功能**: 订单管理、持仓查询、回测任务等
- **清晰的分层架构**: Domain、Service、API三层架构设计
- **类型安全**: 使用Rust的类型系统确保数据安全
- **异步处理**: 全异步设计，支持高并发访问
- **现代化前端**: React + TypeScript + Vite技术栈

## 支持的数据类型

### 1. K线数据 (Kline)
- 开盘价、最高价、最低价、收盘价
- 成交量、成交额
- 交易笔数等详细信息

### 2. 实时价格 (Ticker)
- 实时价格数据
- 支持单个或全部交易对查询

### 3. 深度数据 (OrderBook)
- 买卖盘深度信息
- 可配置深度层级

### 4. 24小时统计 (24hr Stats)
- 价格变化、涨跌幅
- 成交量、成交额
- 最高价、最低价等

### 5. 交易历史 (Trade History)
- 历史成交记录
- 支持分页查询

## 快速开始

### 环境要求

- Rust 1.70+
- PostgreSQL 12+
- Redis 6+

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd DataServer

# 构建项目
cargo build
```

### 配置环境变量

```bash
# 复制环境变量模板
cp .env_example .env

# 编辑配置文件
vim .env
```

### 启动服务

```bash
# 启动gRPC服务器
cargo run --bin data-grpc

# 在另一个终端运行客户端测试
cargo run --bin client-grpc
```

### 使用Docker

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f data-grpc
```

## API接口

### 1. gRPC服务接口 (data-grpc)

服务定义在 `proto/market_data.proto` 文件中：

```protobuf
service MarketDataService {
  rpc GetKlineData(KlineRequest) returns (KlineResponse);
  rpc GetTickerPrice(TickerRequest) returns (TickerResponse);
  rpc GetOrderBook(OrderBookRequest) returns (OrderBookResponse);
  rpc Get24hrStats(StatsRequest) returns (StatsResponse);
  rpc GetTradeHistory(TradeHistoryRequest) returns (TradeHistoryResponse);
}
```

### 2. Trade HTTP API (data-http)

基于Salvo框架的HTTP API服务，提供交易相关功能：

**Base URL**: `http://localhost:8091/backtest/strategy`

主要接口：
- `GET /orders` - 查询订单明细
- `POST /pub_task` - 发起回测请求
- `GET /result` - 检查回测任务状态
- `GET /positions` - 查询历史仓位信息

**统一响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": { /* 响应数据 */ }
}
```

详细API文档请参考：[Trade API 接口规范](doc/trade_api_specification.md)

### 3. RESTful API (data-api)

基于Axum框架的标准RESTful API：

**Base URL**: `http://localhost:8080/api/v1`

主要接口：
- `GET /market/klines` - K线数据查询
- `GET /market/tickers` - 价格数据查询
- `GET /market/orderbook` - 深度数据查询
- `GET /market/stats24hr` - 24小时统计数据
- `GET /market/trades` - 交易历史数据

### 使用示例

```rust
use market_data::market_data_service_client::MarketDataServiceClient;
use market_data::KlineRequest;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let mut client = MarketDataServiceClient::connect("http://127.0.0.1:50051").await?;
    
    let request = tonic::Request::new(KlineRequest {
        symbol: "BTCUSDT".to_string(),
        interval: "1h".to_string(),
        start_time: None,
        end_time: None,
        limit: Some(10),
    });
    
    let response = client.get_kline_data(request).await?;
    println!("K线数据: {:?}", response.into_inner());
    
    Ok(())
}
```

## 开发指南

### 项目结构说明

- **crates/lib/common**: 通用库，包含domain实体、DTO、工具类等
- **crates/lib/service**: 业务服务层，处理数据库操作和业务逻辑
- **crates/exchange**: 交易所API接口封装
- **crates/bin/data-grpc**: gRPC服务器实现
- **crates/bin/client-grpc**: 客户端测试程序

### 添加新的交易所

1. 在 `crates/exchange/` 下创建新的交易所模块
2. 实现对应的API客户端
3. 在service层集成新的数据源
4. 更新workspace配置

### 编码规范

- 使用Rust标准命名规范
- 所有公共API都要有文档注释
- 错误处理使用 `anyhow::Result`
- 异步函数使用 `async/await`
- 避免过度设计，保持代码简洁

## 部署

### 生产环境部署

```bash
# 构建发布版本
cargo build --release

# 使用Docker部署
docker-compose -f docker-compose-prod.yaml up -d
```

### 监控和日志

- 日志文件位置: `log/app.log`
- 日志级别可通过环境变量 `LOG_LEVEL` 配置
- 支持控制台和文件双重输出

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

Apache-2.0 License
