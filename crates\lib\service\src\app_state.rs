use std::sync::Arc;
use anyhow::Result;
use sqlx::PgPool;

use repository::connection::postgres::PostgresPoolManager;
use repository::connection::timescale::TimescalePoolManager;
use repository::connection::redis::{RedisPoolManager, RedisOperations};
use repository::postgres::user::UserRepository;
use repository::timescale::{KlineRepository, SymbolRepository};
use crate::user_service::auth::AuthService;
use exchange::binance::BinanceClient;
use common::config::pgsql_config::DatabaseConfig;
use common::config::redis_config::RedisConfig;
use common::{REDIS_CONFIG, TRADERDATABASE_CONFIG, USERDATABASE_CONFIG};
// use crate::market_data::{MarketDataManager, MarketDataManagerConfig};

/// 应用状态，包含所有共享的服务实例
#[derive(Clone)]
pub struct AppState {
    pub auth_service: Arc<AuthService>,
    pub redis_ops: Arc<RedisOperations>,
    pub market_data_manager: Arc<KlineMaintenanceServiceV2>,
}

/// 应用初始化器，负责创建连接池和服务实例
pub struct AppInitializer;

impl AppInitializer {
    /// 初始化应用状态
    /// 
    /// 这个方法会：
    /// 1. 创建数据库连接池（整个应用共享）
    /// 2. 创建Redis连接池（整个应用共享）
    /// 3. 创建Repository实例（注入连接池）
    /// 4. 创建Service实例（注入Repository）
    /// 5. 将所有Service包装到AppState中
    pub async fn initialize(
        user_db_config: DatabaseConfig,
        trade_db_config: DatabaseConfig,
        redis_config: RedisConfig,
    ) -> Result<AppState> {
        // --- 1. 集中创建一次共享资源 ---
        
        // 创建PostgreSQL连接池（用于用户数据）
        let postgres_pool_manager = PostgresPoolManager::new(user_db_config).await?;
        let user_db_pool = postgres_pool_manager.pool().clone();
        
        // 创建TimescaleDB连接池（用于市场数据）
        let timescale_pool_manager = TimescalePoolManager::new(trade_db_config).await?;
        let trade_db_pool = timescale_pool_manager.pool().clone();
        
        // 创建Redis连接池，整个应用共享这一个
        let redis_pool_manager = RedisPoolManager::new(redis_config.clone()).await
            .map_err(|e| anyhow::anyhow!("Failed to create Redis pool manager: {}", e))?;
        
        // 创建Redis操作工具
        let redis_ops = Arc::new(RedisOperations::new(redis_pool_manager));
        
        // --- 2. 创建 Repository 实例，注入对应的连接池 ---
        let user_repo = UserRepository::new(user_db_pool.clone());
        
        // --- 2.5. 初始化数据库表结构 ---
        // 初始化用户相关表（PostgreSQL）
        user_repo.initialize_tables().await
            .map_err(|e| anyhow::anyhow!("Failed to initialize user tables: {}", e))?;
        
        // 初始化市场数据相关表（TimescaleDB）
        Self::initialize_timescale_tables(&trade_db_pool).await?;
        
        // --- 3. 创建 Service 实例 ---
        let auth_service = Arc::new(AuthService::new(user_repo, redis_ops.as_ref().clone()));
        
        // 创建Binance客户端
        let binance_client = BinanceClient::new();
        
        // 创建市场数据管理器配置
        let market_data_config = MaintenanceConfig::default();
        
        // 创建市场数据管理器
        let market_data_manager = Arc::new(
            KlineMaintenanceServiceV2::new(
                market_data_config,
                timescale_pool_manager,
                Some(redis_ops.as_ref().clone()),
                binance_client,
            ).await
            .map_err(|e| anyhow::anyhow!("Failed to create MarketDataManager: {}", e))?
        );
        
        // --- 4. 将所有 Service 实例放入 AppState ---
        let app_state = AppState {
            auth_service,
            redis_ops,
            market_data_manager,
        };
        
        Ok(app_state)
    }
    
    /// 从Nacos配置初始化应用状态
    pub async fn initialize_from_nacos() -> Result<AppState> {
        log::info!("🔧 从Nacos配置初始化应用状态...");
        
        // 从Nacos获取配置
        let user_db_config = USERDATABASE_CONFIG.get()
            .expect("USERDATABASE_CONFIG not initialized")
            .clone();
        log::info!("✅ 获取到用户数据库配置");

        let trade_db_config = TRADERDATABASE_CONFIG.get()
            .expect("TRADERDATABASE_CONFIG not initialized")
            .clone();
        log::info!("✅ 获取到交易数据库配置");
        
        let redis_config = REDIS_CONFIG.get()
            .expect("REDIS_CONFIG not initialized")
            .clone();
        log::info!("✅ 获取到Redis配置");
        
        Self::initialize(user_db_config, trade_db_config, redis_config).await
    }
    
    /// 初始化TimescaleDB表结构
    async fn initialize_timescale_tables(timescale_pool: &PgPool) -> Result<()> {
        // 检查是否需要初始化TimescaleDB表
        let should_init_timescale = std::env::var("INIT_TIMESCALE_TABLES")
            .unwrap_or_else(|_| "true".to_string())
            .parse::<bool>()
            .unwrap_or(true);
        
        if should_init_timescale {
            log::info!("🔧 开始初始化TimescaleDB表结构...");
            
            // 初始化符号表
            let symbol_repo = SymbolRepository::new(timescale_pool.clone());
            symbol_repo.initialize_tables().await
                .map_err(|e| anyhow::anyhow!("Failed to initialize TimescaleDB symbol tables: {}", e))?;
            log::info!("✅ 符号表初始化完成");
            
            // 初始化K线和市场数据表
            let kline_repo = KlineRepository::new(timescale_pool.clone());
            kline_repo.initialize_table().await
                .map_err(|e| anyhow::anyhow!("Failed to initialize TimescaleDB kline tables: {}", e))?;
            log::info!("✅ K线和市场数据表初始化完成");
            
            log::info!("🎉 TimescaleDB表结构初始化完成");
        } else {
            log::info!("⏭️ 跳过TimescaleDB表初始化（设置INIT_TIMESCALE_TABLES=true启用）");
        }
        
        Ok(())
    }

    /// 健康检查
    pub async fn health_check(app_state: &AppState) -> Result<()> {
        // 检查市场数据管理器健康状态
        match app_state.market_data_manager.health_check().await {
            Ok(health_status) => {
                log::info!("市场数据管理器健康检查通过: {:?}", health_status);
            }
            Err(e) => {
                log::error!("市场数据管理器健康检查失败: {}", e);
                return Err(anyhow::anyhow!("Market data manager health check failed: {}", e));
            }
        }
        
        Ok(())
    }
    
    /// 启动应用服务
    pub async fn start_services(app_state: &AppState) -> Result<()> {
        log::info!("🚀 启动应用服务...");
        
        // 启动市场数据管理器
        // 注意：这里需要特殊处理，因为start()需要&mut self
        // 在实际应用中，应该在创建AppState之前启动服务
        log::warn!("市场数据管理器需要在AppState创建前启动，这里跳过启动步骤");
        // 如果需要启动，应该在MarketDataManager::new之后立即调用start()
        
        log::info!("✅ 所有服务启动完成");
        Ok(())
    }
}

/// 为了向后兼容，提供一个便捷的初始化函数
pub async fn initialize_app_state() -> Result<AppState> {
    AppInitializer::initialize_from_nacos().await
}

/// 初始化并启动应用状态
pub async fn initialize_and_start_app_state() -> Result<AppState> {
    let app_state = AppInitializer::initialize_from_nacos().await?;
    AppInitializer::start_services(&app_state).await?;
    Ok(app_state)
} 