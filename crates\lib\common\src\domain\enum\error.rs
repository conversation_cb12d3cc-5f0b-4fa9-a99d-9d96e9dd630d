use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use thiserror::Error;
use utoipa::ToSchema;

/// 应用全局错误枚举
#[derive(Error, Debug)]
pub enum AppError {
    /// 数据库错误，通常由 sqlx::Error 转换而来
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    /// 序列化或反序列化错误，通常由 serde_json::Error 转换而来
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    /// 无效输入，用于校验请求参数
    #[error("参数错误: {0}")]
    InvalidInput(String),

    /// 资源未找到
    #[error("未找到数据: {0}")]
    NotFound(String),

    /// 未知的服务器内部错误
    #[error("服务器内部错误: {0}")]
    Internal(String),

    /// 配置加载错误，通常由 anyhow::Error 转换而来
    #[error("配置错误: {0}")]
    Config(#[from] anyhow::Error),

    /// 认证失败
    #[error("认证错误: {0}")]
    Auth(String),

    /// 错误的请求
    #[error("请求错误: {0}")]
    BadRequest(String),

    /// 验证码已存在
    #[error("验证码已存在: {0}")]
    VerificationCodeExist(String),
    

    /// 未授权，通常是权限不足
    #[error("未授权: {0}")]
    Unauthorized(String),

    /// 通用服务器错误
    #[error("服务器错误: {0}")]
    ServerError(String),

    /// Nacos 配置中心错误
    #[error("Nacos 配置中心错误: {0}")]
    NacosError(String),

    /// 通用配置错误
    #[error("配置错误: {0}")]
    ConfigError(String),
}

/// 标准错误响应体
#[derive(Serialize, Deserialize, ToSchema, Debug)]
pub struct ErrorResponse {
    /// 错误的通用类别
    pub error: String,
    /// 详细的错误信息
    pub message: String,
    /// HTTP 状态码
    pub code: u16,
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        // `thiserror` 为我们实现了 Display trait，可以方便地获取详细错误信息
        let detailed_message = self.to_string();

        // 根据错误类型匹配 HTTP 状态码和错误的通用类别
        let (status, error_category) = match self {
            AppError::Database(_) => (StatusCode::INTERNAL_SERVER_ERROR, "数据库错误"),
            AppError::Serialization(_) => (StatusCode::BAD_REQUEST, "数据格式错误"),
            AppError::InvalidInput(_) => (StatusCode::BAD_REQUEST, "无效的输入参数"),
            AppError::NotFound(_) => (StatusCode::NOT_FOUND, "资源未找到"),
            AppError::Internal(_) => (StatusCode::INTERNAL_SERVER_ERROR, "服务器内部错误"),
            AppError::Config(_) => (StatusCode::INTERNAL_SERVER_ERROR, "配置加载错误"),
            AppError::Auth(_) => (StatusCode::UNAUTHORIZED, "认证失败"),
            AppError::BadRequest(_) => (StatusCode::BAD_REQUEST, "错误的请求"),

            // --- 以下是补全的字段 ---
            AppError::Unauthorized(_) => (StatusCode::UNAUTHORIZED, "访问未授权"),
            AppError::ServerError(_) => (StatusCode::INTERNAL_SERVER_ERROR, "服务器错误"),
            AppError::NacosError(_) => (StatusCode::INTERNAL_SERVER_ERROR, "依赖服务错误"),
            AppError::ConfigError(_) => (StatusCode::INTERNAL_SERVER_ERROR, "配置错误"),
            AppError::VerificationCodeExist(_)=>(StatusCode::BAD_REQUEST, "验证码已存在")
        };

        // 构建 JSON 响应体
        let body = Json(ErrorResponse {
            error: error_category.to_string(), // 通用类别
            message: detailed_message,         // 详细信息
            code: status.as_u16(),
        });

        (status, body).into_response()
    }
}