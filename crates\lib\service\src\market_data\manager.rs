use chrono::{DateTime, Utc, Duration};
use crate::market_data::{
    types::{Result, MarketDataServiceError, IncrementalUpdateResult, IntegrityCheckResult, CacheStatus, KlineInterval},
    checker::<PERSON><PERSON><PERSON><PERSON>,
    fetcher::DataFetcher,
};
use repository::{
    connection::{
        timescale::TimescalePoolManager,
        redis::RedisOperations,
    },
    timescale::{MarketDataRepository, SymbolRepository},
};
use exchange::binance::BinanceClient;
use std::collections::HashMap;
use tokio::task::Join<PERSON><PERSON><PERSON>;

/// 市场数据管理器配置
#[derive(Debug, Clone)]
pub struct MarketDataManagerConfig {
    /// 交易所名称
    pub exchange_name: String,
    /// 增量更新间隔（分钟）
    pub update_interval_minutes: u64,
    /// 活跃交易对列表
    pub active_symbols: Vec<String>,
    /// 数据完整性检查的开始日期
    pub integrity_check_start_date: DateTime<Utc>,
    /// 是否启用自动增量更新
    pub enable_auto_update: bool,
    /// 是否启用Redis缓存
    pub enable_redis_cache: bool,
}

impl Default for MarketDataManagerConfig {
    fn default() -> Self {
        Self {
            exchange_name: "binance".to_string(),
            update_interval_minutes: 5,
            active_symbols: vec![
                "BTCUSDT".to_string(),
                "ETHUSDT".to_string(),
                "BNBUSDT".to_string(),
            ],
            integrity_check_start_date: Utc::now() - Duration::days(365),
            enable_auto_update: true,
            enable_redis_cache: true,
        }
    }
}

/// 市场数据管理器 (V2)
/// 负责协调所有市场数据相关的服务
pub struct MarketDataManager {
    config: MarketDataManagerConfig,
    checker: DataChecker,
    fetcher: DataFetcher,
    symbol_repo: SymbolRepository,
    // TODO: cache: CacheManager,
    background_tasks: Vec<JoinHandle<()>>,
}

impl MarketDataManager {
    /// 创建新的市场数据管理器 (V2)
    pub async fn new(
        config: MarketDataManagerConfig,
        timescale_pool_manager: TimescalePoolManager,
        redis_ops: Option<RedisOperations>,
        binance_client: BinanceClient,
    ) -> Result<Self> {
        log::info!("初始化市场数据管理器 (V2)...");

        let checker = DataChecker::new(&timescale_pool_manager);
        let fetcher = DataFetcher::new(&timescale_pool_manager, binance_client);
        let symbol_repo = SymbolRepository::new(timescale_pool_manager.pool().clone());
        
        Ok(Self {
            config,
            checker,
            fetcher,
            symbol_repo,
            background_tasks: Vec::new(),
        })
    }

    /// 启动后台任务
    pub async fn start(&mut self) -> Result<()> {
        log::info!("启动市场数据管理器后台任务 (V2)...");
        if !self.config.enable_auto_update {
            log::warn!("自动更新未启用，跳过后台任务启动");
            return Ok(());
        }

        // 每日全量检查任务
        let daily_check_handle = self.spawn_daily_full_check_task();
        self.background_tasks.push(daily_check_handle);

        // TODO: 增量更新任务
        // let incremental_update_handle = self.spawn_incremental_update_task();
        // self.background_tasks.push(incremental_update_handle);
        
        log::info!("后台任务已启动，共 {} 个任务", self.background_tasks.len());
        Ok(())
    }
    
    /// 执行一次全量数据完整性检查
    pub async fn perform_full_integrity_check(&self) -> Result<Vec<IntegrityCheckResult>> {
        log::info!("开始执行全量数据完整性检查 (V2)...");
        
        let symbols = self.get_active_symbols().await?;
        if symbols.is_empty() {
            log::warn!("没有需要检查的交易对，完整性检查结束");
            return Ok(Vec::new());
        }

        let mut all_results = Vec::new();
        let end_time = Utc::now();
        let start_time = self.config.integrity_check_start_date;

        for symbol in &symbols {
            for interval in KlineInterval::all() {
                log::debug!("正在检查 {} {}", symbol, interval);

                let check_result = self.checker
                    .check_symbol_integrity(symbol, &interval, start_time, end_time)
                    .await?;
                
                if !check_result.missing_periods.is_empty() {
                    log::warn!(
                        "发现 {} {} 的缺失数据，共 {} 个时间段，将开始填充...",
                        symbol,
                        interval,
                        check_result.missing_periods.len()
                    );
                    if let Err(e) = self.fetcher.fill_missing_data(symbol, &interval, &check_result.missing_periods).await {
                        log::error!("填充 {} {} 数据失败: {}", symbol, interval, e);
                    }
                }
                
                all_results.push(check_result);
            }
        }
        
        log::info!("全量数据完整性检查完成");
        Ok(all_results)
    }

    fn spawn_daily_full_check_task(&self) -> JoinHandle<()> {
        let self_clone = self.clone_for_task();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(24 * 60 * 60)); // 每天执行一次
            loop {
                interval.tick().await;
                log::info!("开始每日例行全量数据完整性检查...");
                if let Err(e) = self_clone.perform_full_integrity_check().await {
                    log::error!("每日全量检查任务失败: {}", e);
                }
            }
        })
    }
    
    /// 为后台任务克隆一个独立的Manager实例
    fn clone_for_task(&self) -> Self {
         // Background tasks need their own instances
        Self {
            config: self.config.clone(),
            checker: self.checker.clone(),
            fetcher: self.fetcher.clone(),
            symbol_repo: self.symbol_repo.clone(),
            background_tasks: Vec::new(), // Cloned task manager doesn't manage other tasks
        }
    }

    /// 获取活跃的交易对列表
    async fn get_active_symbols(&self) -> Result<Vec<String>> {
        match self.symbol_repo.get_active_symbols().await {
            Ok(symbols) if !symbols.is_empty() => Ok(symbols.into_iter().map(|s| s.symbol).collect()),
            _ => {
                log::warn!("数据库中未找到活跃交易对，使用配置中的默认列表");
                Ok(self.config.active_symbols.clone())
            }
        }
    }

    /// 停止所有后台服务
    pub async fn stop(&mut self) {
        log::info!("正在停止市场数据管理器的 {} 个后台任务...", self.background_tasks.len());
        for task in self.background_tasks.drain(..) {
            task.abort();
        }
        log::info!("所有后台任务已停止");
    }

    // --- 其他兼容性方法 ---
    pub async fn perform_startup_tasks(&self) -> Result<()> {
        log::info!("执行启动任务 (V2)...");
        self.perform_full_integrity_check().await?;
        Ok(())
    }

    pub fn get_config(&self) -> &MarketDataManagerConfig { &self.config }
    pub async fn get_service_statistics(&self) -> Result<HashMap<String, serde_json::Value>> { Ok(HashMap::new()) }
    pub async fn trigger_manual_update(&self) -> Result<Vec<IncrementalUpdateResult>> { Ok(Vec::new()) }
    pub async fn cleanup_expired_cache(&self) -> Result<i64> { Ok(0) }
    pub async fn health_check(&self) -> Result<HashMap<String, bool>> { Ok(HashMap::new()) }
}
