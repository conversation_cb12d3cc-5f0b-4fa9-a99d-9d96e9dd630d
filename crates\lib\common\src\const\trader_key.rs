
pub const TRADER_KEY: &str = "trader-TRADER-999";

// 任务相关key生成函数 trader-RUNNER-ROOT:task
pub fn task_key() -> String {
    return  "trader-RUNNER-ROOT:task".to_string();
}

// 订单相关key生成函数
pub fn orders_key_pattern() -> String {
    format!("{}:orders:*", TRADER_KEY)
}

pub fn orders_key_prefix() -> String {
    format!("{}:orders:", TRADER_KEY)
}

// 仓位相关key生成函数
pub fn positions_key_pattern() -> String {
    format!("{}:positions:*", TRADER_KEY)
}

pub fn positions_key_prefix() -> String {
    format!("{}:positions:", TRADER_KEY)
}

// 账户历史相关key生成函数
pub fn accounts_key_pattern() -> String {
    format!("{}:history:*", TRADER_KEY)
}

pub fn accounts_key_prefix() -> String {
    format!("{}:history:", TRADER_KEY)
}

// 分析报表key生成函数
pub fn accounts_key_report() -> String {
    format!("{}:general:analysis_report:BINANCE-001", TRADER_KEY)
}