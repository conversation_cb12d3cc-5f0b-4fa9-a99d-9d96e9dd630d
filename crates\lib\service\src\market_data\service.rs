use anyhow::Result;
use chrono::{DateTime, Utc, TimeZone, Timelike};
use rust_decimal::Decimal;
use sqlx;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::time::{sleep, Duration, Instant};

// 使用新的repository模块
use repository::{
    timescale::{KlineRepository, TradeRepository, CreateMarketDataRequest, CreateTradeRequest, Symbol, MarketDataQuery, TimeBucketCount},
    connection::{timescale::TimescalePoolManager, DatabaseConfig},
    CacheRepository, CacheError, TtlCalculator, CacheStats,
    RedisCacheRepository,
};

use common::domain::entity::{
    KlineEntity, TickerEntity, OrderBookEntity, 
    Stats24hrEntity, TradeEntity
};

use common::domain::dto::{
    KlineQueryDto, TickerQueryDto, OrderBookQueryDto,
    StatsQueryDto, TradeHistoryQueryDto, KlineDto
};
use common::TRADERDATABASE_CONFIG;

// 导入交易所客户端
use exchange::{ExchangeClient, ExchangeClientFactory, KlineQuery, UniversalKline};

// 导入维护配置和存储管理器
use super::config::MaintenanceConfig;
use super::storage::TimescaleStorageManager;

/// 数据缺口信息
#[derive(Debug, Clone)]
pub struct DataGap {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

/// 时间桶缺口检测结果
#[derive(Debug, Clone)]
pub struct TimeBucketGap {
    pub interval: String,
    pub time_bucket: DateTime<Utc>,
    pub expected_count: i64,
    pub actual_count: i64,
}

/// 精确缺口检测报告
#[derive(Debug, Clone)]
pub struct GapDetectionReport {
    pub symbol: String,
    pub interval: String,
    pub total_buckets_checked: i64,
    pub gaps_found: Vec<TimeBucketGap>,
    pub missing_data_ranges: Vec<DataGap>,
}

/// 缓存维护报告
#[derive(Debug, Clone)]
pub struct MaintenanceReport {
    /// 已处理的交易对数量
    pub processed_symbols: u32,
    /// TTL更新数量
    pub ttl_updates: u32,
    /// 删除的过期键数量
    pub deleted_keys: u32,
    /// 错误列表
    pub errors: Vec<String>,
}

impl MaintenanceReport {
    /// 创建新的维护报告
    pub fn new() -> Self {
        Self {
            processed_symbols: 0,
            ttl_updates: 0,
            deleted_keys: 0,
            errors: Vec::new(),
        }
    }
}

/// 数据完整性统计报告
#[derive(Debug, Clone)]
pub struct DataIntegrityStats {
    pub symbol: String,
    pub check_time_range: (DateTime<Utc>, DateTime<Utc>),
    pub interval_stats: Vec<IntervalIntegrityStats>,
    pub total_gaps: usize,
    pub total_missing_ranges: usize,
}

/// 单个时间级别的完整性统计
#[derive(Debug, Clone)]
pub struct IntervalIntegrityStats {
    pub interval: String,
    pub total_buckets_checked: i64,
    pub gaps_found: i64,
    pub missing_ranges: i64,
    pub integrity_percentage: f64,
}

/// 统一的市场数据服务 - 集成CRUD和维护功能
pub struct MarketDataService {
    // Repository层
    kline_repo: KlineRepository,
    trade_repo: TradeRepository,
    cache_repo: Arc<dyn CacheRepository>,
    
    // 维护相关
    config: Option<MaintenanceConfig>,
    storage: Option<Arc<TimescaleStorageManager>>,
    exchange_clients: HashMap<String, Box<dyn ExchangeClient>>,
    shutdown_signal: Arc<AtomicBool>,
    last_full_scan: Option<DateTime<Utc>>,
    last_incremental_scan: Option<DateTime<Utc>>,
}

impl MarketDataService {
    /// 创建新的市场数据服务实例（仅CRUD功能）
    pub async fn new(cache_repo: Arc<dyn CacheRepository>) -> Result<Self> {
        let timescale_config = TRADERDATABASE_CONFIG.get().expect("DATABASE_CONFIG not initialized");
        let timescale_pool = TimescalePoolManager::new(timescale_config.clone()).await?;
        let pool = timescale_pool.pool().clone();
        
        Ok(Self {
            kline_repo: KlineRepository::new(pool.clone()),
            trade_repo: TradeRepository::new(pool),
            cache_repo,
            config: None,
            storage: None,
            exchange_clients: HashMap::new(),
            shutdown_signal: Arc::new(AtomicBool::new(false)),
            last_full_scan: None,
            last_incremental_scan: None,
        })
    }

    /// 从连接池管理器创建服务实例
    pub fn from_pool_manager(timescale_pool: TimescalePoolManager, cache_repo: Arc<dyn CacheRepository>) -> Self {
        let pool = timescale_pool.pool().clone();
        
        Self {
            kline_repo: KlineRepository::new(pool.clone()),
            trade_repo: TradeRepository::new(pool),
            cache_repo,
            config: None,
            storage: None,
            exchange_clients: HashMap::new(),
            shutdown_signal: Arc::new(AtomicBool::new(false)),
            last_full_scan: None,
            last_incremental_scan: None,
        }
    }

    /// 创建带维护功能的市场数据服务实例
    pub async fn with_maintenance(
        config: MaintenanceConfig,
        storage: Arc<TimescaleStorageManager>,
        cache_repo: Arc<dyn CacheRepository>,
    ) -> Result<Self> {
        // 验证配置
        config.validate().map_err(|e| anyhow::anyhow!("配置验证失败: {}", e))?;

        let timescale_config = TRADERDATABASE_CONFIG.get().expect("DATABASE_CONFIG not initialized");
        let timescale_pool = TimescalePoolManager::new(timescale_config.clone()).await?;
        let pool = timescale_pool.pool().clone();

        // 初始化交易所客户端
        let mut exchange_clients = HashMap::new();
        
        // 创建Binance客户端
        let binance_client = ExchangeClientFactory::create_binance_client();
        exchange_clients.insert("binance".to_string(), binance_client);

        Ok(Self {
            kline_repo: KlineRepository::new(pool.clone()),
            trade_repo: TradeRepository::new(pool),
            cache_repo,
            config: Some(config),
            storage: Some(storage),
            exchange_clients,
            shutdown_signal: Arc::new(AtomicBool::new(false)),
            last_full_scan: None,
            last_incremental_scan: None,
        })
    }

    // =============================================================================
    // CRUD 操作方法
    // =============================================================================

    /// 将KlineEntity转换为CreateMarketDataRequest
    fn kline_to_market_data_request(kline: &KlineEntity) -> CreateMarketDataRequest {
        CreateMarketDataRequest {
            symbol: kline.symbol.clone(),
            timestamp: kline.time,
            open: kline.open_price,
            high: kline.high_price,
            low: kline.low_price,
            close: kline.close_price,
            volume: kline.volume,
            interval_type: kline.interval.clone(),
            quote_asset_volume: kline.quote_asset_volume,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            close_time: kline.close_time,
        }
    }

    /// 将TradeEntity转换为CreateTradeRequest
    fn trade_to_trade_request(trade: &TradeEntity) -> CreateTradeRequest {
        CreateTradeRequest {
            symbol: trade.symbol.clone(),
            timestamp: trade.time,
            price: trade.price,
            quantity: trade.qty,
            side: if trade.is_buyer_maker { "sell".to_string() } else { "buy".to_string() },
            trade_type: "market".to_string(),
            trader_id: None,
            order_id: None,
        }
    }

    /// 获取K线数据 - 实现Cache-Aside模式
    /// 
    /// 1. 首先检查缓存
    /// 2. 缓存未命中时查询数据库
    /// 3. 将数据库结果异步写入缓存
    /// 4. 返回数据
    pub async fn get_kline_data(&self, query: KlineQueryDto) -> Result<Vec<KlineEntity>> {
        use common::domain::dto::KlineDto;
        use tracing::{info, warn, debug};

        let start_time = query.start_time.unwrap_or_else(|| chrono::Utc::now() - chrono::Duration::days(1));
        let end_time = query.end_time.unwrap_or_else(|| chrono::Utc::now());
        
        debug!(
            "Getting klines for {} {} from {} to {} (limit: {:?})",
            query.symbol, query.interval, start_time, end_time, query.limit
        );

        // 1. 首先尝试从缓存获取数据
        match self.cache_repo.get_klines_by_range(
            &query.symbol,
            &query.interval,
            start_time,
            end_time,
            query.limit.map(|l| l as usize),
        ).await {
            Ok(cached_klines) if !cached_klines.is_empty() => {
                info!("Cache hit: Retrieved {} klines from cache for {}", cached_klines.len(), query.symbol);
                
                // 将KlineDto转换为KlineEntity
                let klines = cached_klines.into_iter().map(|dto| KlineEntity {
                    time: dto.time,
                    symbol: dto.symbol,
                    interval: dto.interval,
                    open_price: dto.open_price,
                    high_price: dto.high_price,
                    low_price: dto.low_price,
                    close_price: dto.close_price,
                    volume: dto.volume,
                    quote_asset_volume: dto.quote_asset_volume,
                    number_of_trades: dto.number_of_trades,
                    taker_buy_base_asset_volume: dto.taker_buy_base_asset_volume,
                    taker_buy_quote_asset_volume: dto.taker_buy_quote_asset_volume,
                    close_time: dto.close_time,
                }).collect();
                
                return Ok(klines);
            }
            Ok(_) => {
                debug!("Cache miss: No data found in cache for {}", query.symbol);
            }
            Err(e) => {
                warn!("Cache error: {}, falling back to database", e);
            }
        }

        // 2. 缓存未命中，查询数据库
        let market_query = repository::timescale::MarketDataQuery {
            symbol: query.symbol.clone(),
            start_time,
            end_time,
            interval_type: query.interval.clone(),
            limit: query.limit.map(|l| l as u32),
        };

        let market_data = self.kline_repo.query(market_query).await?;
        
        let klines: Vec<KlineEntity> = market_data.into_iter().map(|data| KlineEntity {
            time: data.timestamp,
            symbol: data.symbol,
            interval: data.interval_type,
            open_price: data.open,
            high_price: data.high,
            low_price: data.low,
            close_price: data.close,
            volume: data.volume,
            quote_asset_volume: Decimal::ZERO,
            number_of_trades: 0,
            taker_buy_base_asset_volume: Decimal::ZERO,
            taker_buy_quote_asset_volume: Decimal::ZERO,
            close_time: data.timestamp,
        }).collect();

        info!("Database query: Retrieved {} klines from database for {}", klines.len(), query.symbol);

        // 3. 异步将数据写入缓存（不阻塞返回）
        if !klines.is_empty() {
            let cache_repo = self.cache_repo.clone();
            let symbol = query.symbol.clone();
            let interval = query.interval.clone();
            let klines_for_cache = klines.clone();
            
            tokio::spawn(async move {
                if let Err(e) = Self::write_klines_to_cache(cache_repo, &symbol, &interval, &klines_for_cache).await {
                    warn!("Failed to write klines to cache: {}", e);
                }
            });
        }

        // 4. 返回数据
        Ok(klines)
    }

    /// 将K线数据写入缓存的辅助方法
    async fn write_klines_to_cache(
        cache_repo: Arc<dyn CacheRepository>,
        symbol: &str,
        interval: &str,
        klines: &[KlineEntity],
    ) -> Result<(), CacheError> {
        use common::domain::dto::KlineDto;
        use std::collections::HashMap;
        use repository::TtlCalculator;

        // 按日期分组K线数据
        let mut klines_by_date: HashMap<String, Vec<KlineDto>> = HashMap::new();
        
        for kline in klines {
            let date = KlineDto::timestamp_to_date_string(kline.time.timestamp());
            let dto = KlineDto {
                time: kline.time,
                symbol: kline.symbol.clone(),
                interval: kline.interval.clone(),
                open_time: kline.time,
                close_time: kline.close_time,
                open_price: kline.open_price,
                high_price: kline.high_price,
                low_price: kline.low_price,
                close_price: kline.close_price,
                volume: kline.volume,
                quote_asset_volume: kline.quote_asset_volume,
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            };
            
            klines_by_date.entry(date).or_insert_with(Vec::new).push(dto);
        }

        // 为每个日期写入缓存并设置TTL
        for (date, date_klines) in klines_by_date {
            // 添加K线数据
            cache_repo.add_klines_for_day(symbol, interval, &date, &date_klines).await?;
            
            // 设置TTL
            let current_date = TtlCalculator::get_current_date_string();
            let ttl = if date == current_date {
                // 当天的数据使用固定TTL
                TtlCalculator::get_fixed_ttl()
            } else {
                // 历史数据使用动态TTL
                TtlCalculator::calculate_dynamic_ttl(&date, &current_date)?
            };
            
            cache_repo.set_expiry_for_day(symbol, &date, ttl).await?;
        }

        Ok(())
    }
    
    /// 获取实时价格
    pub async fn get_ticker_price(&self, query: TickerQueryDto) -> Result<Vec<TickerEntity>> {
        if let Some(ref symbol) = query.symbol {
            if let Some(latest) = self.kline_repo.get_latest(symbol, "1m").await? {
                let ticker = TickerEntity {
                    time: latest.timestamp,
                    symbol: latest.symbol,
                    price: latest.close,
                };
                Ok(vec![ticker])
            } else {
                Ok(vec![])
            }
        } else {
            Ok(vec![])
        }
    }
    
    /// 获取深度数据
    pub async fn get_order_book(&self, query: OrderBookQueryDto) -> Result<OrderBookEntity> {
        Ok(OrderBookEntity {
            time: chrono::Utc::now(),
            symbol: query.symbol,
            last_update_id: 0,
            bids: sqlx::types::Json(vec![]),
            asks: sqlx::types::Json(vec![]),
        })
    }
    
    /// 获取24小时统计数据
    pub async fn get_24hr_stats(&self, query: StatsQueryDto) -> Result<Vec<Stats24hrEntity>> {
        if let Some(ref symbol) = query.symbol {
            let end_time = chrono::Utc::now();
            let start_time = end_time - chrono::Duration::hours(24);
            
            if let Some(stats) = self.kline_repo.get_stats(symbol, start_time, end_time).await.ok() {
                let stats_entity = Stats24hrEntity {
                    time: end_time,
                    symbol: symbol.clone(),
                    price_change: Decimal::ZERO,
                    price_change_percent: Decimal::ZERO,
                    weighted_avg_price: stats.avg_price.unwrap_or(Decimal::ZERO),
                    prev_close_price: Decimal::ZERO,
                    last_price: stats.max_price.unwrap_or(Decimal::ZERO),
                    last_qty: Decimal::ZERO,
                    bid_price: Decimal::ZERO,
                    ask_price: Decimal::ZERO,
                    open_price: stats.min_price.unwrap_or(Decimal::ZERO),
                    high_price: stats.max_price.unwrap_or(Decimal::ZERO),
                    low_price: stats.min_price.unwrap_or(Decimal::ZERO),
                    volume: stats.total_volume.unwrap_or(Decimal::ZERO),
                    quote_volume: Decimal::ZERO,
                    open_time: start_time,
                    close_time: end_time,
                    first_id: 0,
                    last_id: 0,
                    count: stats.count as i64,
                };
                Ok(vec![stats_entity])
            } else {
                Ok(vec![])
            }
        } else {
            Ok(vec![])
        }
    }
    
    /// 获取交易历史
    pub async fn get_trade_history(&self, query: TradeHistoryQueryDto) -> Result<Vec<TradeEntity>> {
        let trade_query = repository::timescale::TradeQuery {
            symbol: Some(query.symbol.clone()),
            trader_id: None,
            start_time: query.start_time.unwrap_or_else(|| chrono::Utc::now() - chrono::Duration::hours(1)),
            end_time: query.end_time.unwrap_or_else(|| chrono::Utc::now()),
            side: None,
            limit: query.limit.map(|l| l as u32),
        };

        let trades = self.trade_repo.query(trade_query).await?;
        
        let trade_entities = trades.into_iter().map(|trade| TradeEntity {
            trade_id: trade.id,
            time: trade.timestamp,
            symbol: trade.symbol,
            price: trade.price,
            qty: trade.quantity,
            quote_qty: trade.price * trade.quantity,
            is_buyer_maker: trade.side == "sell",
            is_best_match: true,
        }).collect();

        Ok(trade_entities)
    }

    /// 保存K线数据
    pub async fn save_kline_data(&self, klines: Vec<KlineEntity>) -> Result<()> {
        let requests: Vec<CreateMarketDataRequest> = klines.iter()
            .map(Self::kline_to_market_data_request)
            .collect();
        
        self.kline_repo.batch_insert(requests).await?;
        Ok(())
    }
    
    /// 保存价格数据
    pub async fn save_ticker_data(&self, tickers: Vec<TickerEntity>) -> Result<()> {
        let requests: Vec<CreateMarketDataRequest> = tickers.iter().map(|ticker| {
            CreateMarketDataRequest {
                symbol: ticker.symbol.clone(),
                timestamp: ticker.time,
                open: ticker.price,
                high: ticker.price,
                low: ticker.price,
                close: ticker.price,
                volume: Decimal::ZERO,
                interval_type: "1m".to_string(),
                quote_asset_volume: Decimal::ZERO,
                number_of_trades: 0,
                taker_buy_base_asset_volume: Decimal::ZERO,
                taker_buy_quote_asset_volume: Decimal::ZERO,
                close_time: ticker.time,
            }
        }).collect();
        
        self.kline_repo.batch_insert(requests).await?;
        Ok(())
    }
    
    /// 保存深度数据
    pub async fn save_order_book(&self, _order_book: OrderBookEntity) -> Result<()> {
        // 新的repository中没有order book表，暂时不实现
        Ok(())
    }
    
    /// 保存24小时统计数据
    pub async fn save_24hr_stats(&self, _stats: Vec<Stats24hrEntity>) -> Result<()> {
        // 24小时统计数据可以通过聚合查询实时计算，不需要单独存储
        Ok(())
    }
    
    /// 保存交易数据
    pub async fn save_trade_data(&self, trades: Vec<TradeEntity>) -> Result<()> {
        let requests: Vec<CreateTradeRequest> = trades.iter()
            .map(Self::trade_to_trade_request)
            .collect();
        
        self.trade_repo.batch_insert(requests).await?;
        Ok(())
    }

    // =============================================================================
    // 数据维护方法
    // =============================================================================

    /// 启动维护服务（在单独线程中运行）
    pub async fn start_maintenance(&mut self) -> Result<()> {
        if self.config.is_none() || self.storage.is_none() {
            return Err(anyhow::anyhow!("维护功能未配置，请使用with_maintenance方法创建服务"));
        }

        log::info!("启动数据维护服务...");

        // 初始化数据库
        if let Some(ref storage) = self.storage {
            log::info!("正在初始化数据库表结构...");
            if let Err(e) = storage.initialize_database().await {
                log::error!("数据库初始化失败: {}", e);
                return Err(e);
            }
            log::info!("数据库表结构初始化成功");
        }

        // 检查是否为首次启动
        let is_first_startup = self.last_full_scan.is_none();
        if is_first_startup {
            log::info!("🚀 检测到首次启动，将立即执行全量历史数据扫描...");
            if let Err(e) = self.run_full_scan().await {
                log::error!("首次全量扫描失败: {}", e);
                return Err(e);
            }
            log::info!("✅ 首次全量扫描完成，开始正常维护循环");
        }

        // 主循环
        let mut loop_count = 0;
        while !self.shutdown_signal.load(Ordering::Relaxed) {
            let loop_start = Instant::now();
            loop_count += 1;

            log::debug!("维护循环 #{} 开始", loop_count);

            if self.should_run_full_scan() {
                log::info!("触发定期全量扫描...");
                if let Err(e) = self.run_full_scan().await {
                    log::error!("定期全量扫描失败: {}", e);
                }
            }

            if self.should_run_incremental_scan() {
                log::debug!("触发增量扫描...");
                if let Err(e) = self.run_incremental_scan().await {
                    log::error!("增量扫描失败: {}", e);
                }
            }

            let elapsed = loop_start.elapsed();
            let sleep_duration = if elapsed < Duration::from_secs(60) {
                Duration::from_secs(60) - elapsed
            } else {
                Duration::from_secs(1)
            };

            log::debug!("维护循环 #{} 完成，耗时: {:?}，休眠: {:?}", loop_count, elapsed, sleep_duration);
            sleep(sleep_duration).await;
        }

        log::info!("数据维护服务已停止");
        Ok(())
    }

    /// 停止维护服务
    pub fn stop_maintenance(&self) {
        self.shutdown_signal.store(true, Ordering::Relaxed);
    }

    /// 判断是否应该运行全量扫描
    fn should_run_full_scan(&self) -> bool {
        if let Some(ref config) = self.config {
            match self.last_full_scan {
                None => true,
                Some(last) => {
                    let now = Utc::now();
                    let elapsed = now - last;
                    elapsed.num_seconds() >= config.full_scan_interval().as_secs() as i64
                }
            }
        } else {
            false
        }
    }

    /// 判断是否应该运行增量扫描
    fn should_run_incremental_scan(&self) -> bool {
        if let Some(ref config) = self.config {
            match self.last_incremental_scan {
                None => true,
                Some(last) => {
                    let now = Utc::now();
                    let elapsed = now - last;
                    elapsed.num_seconds() >= config.incremental_scan_interval().as_secs() as i64
                }
            }
        } else {
            false
        }
    }

    /// 执行全量扫描
    async fn run_full_scan(&mut self) -> Result<()> {
        log::info!("🔍 开始执行全量扫描...");
        let start_time = Instant::now();

        let symbols = self.get_maintained_symbols().await?;
        if symbols.is_empty() {
            log::warn!("symbols表中没有需要维护的币对，跳过全量扫描");
            return Ok(());
        }

        log::info!("📊 从symbols表获取到 {} 个需要维护的币对", symbols.len());

        // 显示将要处理的交易对
        for (i, symbol) in symbols.iter().enumerate().take(10) {
            log::info!("  {}. {} ({})", i + 1, symbol.symbol, symbol.exchange);
        }
        if symbols.len() > 10 {
            log::info!("  ... 还有 {} 个交易对", symbols.len() - 10);
        }

        log::info!("🔧 开始数据一致性检查...");
        self.check_data_consistency(&symbols).await?;

        log::info!("📈 开始维护历史数据（从2024-06-01至今）...");
        self.maintain_historical_data(&symbols).await?;

        if let Some(ref storage) = self.storage {
            log::info!("🧹 开始数据库维护...");
            storage.perform_maintenance().await?;
        }

        // 全量扫描完成后，重整Redis缓存
        log::info!("🚀 全量扫描完成，开始Redis缓存重整...");
        self.reorganize_redis_cache(&symbols).await?;

        self.last_full_scan = Some(Utc::now());
        log::info!("✅ 全量扫描和缓存重整完成，总耗时: {:?}", start_time.elapsed());
        Ok(())
    }

    /// 执行增量扫描
    async fn run_incremental_scan(&mut self) -> Result<()> {
        log::info!("开始执行增量扫描...");
        let start_time = Instant::now();

        let symbols = self.get_maintained_symbols().await?;
        if symbols.is_empty() {
            log::warn!("symbols表中没有活跃的币对，跳过增量扫描");
            return Ok(());
        }

        log::info!("找到 {} 个活跃交易符号", symbols.len());

        for symbol in symbols {
            if let Err(e) = self.fetch_latest_klines(&symbol).await {
                log::error!("获取{}的最新K线数据失败: {}", symbol.symbol, e);
            }
        }

        self.last_incremental_scan = Some(Utc::now());
        log::info!("增量扫描完成，耗时: {:?}", start_time.elapsed());
        Ok(())
    }

    /// 从symbols表获取需要维护的币对
    async fn get_maintained_symbols(&self) -> Result<Vec<Symbol>> {
        if let Some(ref storage) = self.storage {
            log::info!("从symbols表获取需要维护的币对列表...");
            
            let symbols = storage.symbol_manager().get_active_symbol_objects().await?;
            
            let mut exchange_counts = HashMap::new();
            for symbol in &symbols {
                *exchange_counts.entry(symbol.exchange.clone()).or_insert(0) += 1;
            }
            
            for (exchange, count) in exchange_counts {
                log::info!("交易所 {} 有 {} 个活跃币对", exchange, count);
            }
            
            Ok(symbols)
        } else {
            Ok(vec![])
        }
    }

    /// 检查数据一致性
    async fn check_data_consistency(&self, symbols: &[Symbol]) -> Result<()> {
        log::info!("检查数据一致性...");

        let mut missing_data_count = 0;
        for symbol in symbols {
            if let Err(e) = self.check_symbol_data_integrity(&symbol.symbol).await {
                log::warn!("符号{}数据完整性检查失败: {}", symbol.symbol, e);
                missing_data_count += 1;
            }
        }

        if missing_data_count > 0 {
            log::warn!("发现{}个符号存在数据完整性问题", missing_data_count);
        }

        Ok(())
    }

    /// 检查单个符号的数据完整性
    async fn check_symbol_data_integrity(&self, _symbol: &str) -> Result<()> {
        // 这里可以实现具体的数据完整性检查逻辑
        Ok(())
    }

    /// 获取最新K线数据
    async fn fetch_latest_klines(&self, symbol: &Symbol) -> Result<()> {
        let client = self.exchange_clients.get(&symbol.exchange)
            .ok_or_else(|| anyhow::anyhow!("不支持的交易所: {}", symbol.exchange))?;

        let query = KlineQuery {
            symbol: symbol.symbol.clone(),
            interval: "1m".to_string(),
            start_time: None,
            end_time: None,
            limit: Some(100),
        };

        let klines = client.get_klines(query).await?;
        
        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests(&symbol.symbol, &klines);
            if let Some(ref storage) = self.storage {
                storage.kline_manager().batch_insert(requests).await?;
                log::debug!("为符号 {} 插入了 {} 条K线数据", symbol.symbol, klines.len());
            }
        }

        Ok(())
    }

    /// 维护历史数据（使用新的时间桶检测方法）
    async fn maintain_historical_data(&self, symbols: &[Symbol]) -> Result<()> {
        log::info!("🔧 开始维护历史数据（2024-06-01 至今）- 使用时间桶精确检测...");
        let start_time = Instant::now();

        let maintenance_start = Utc.with_ymd_and_hms(2024, 6, 1, 0, 0, 0)
            .single()
            .ok_or_else(|| anyhow::anyhow!("无法创建维护起始时间"))?;
        
        let maintenance_end = Utc::now();
        
        log::info!("📅 历史数据维护时间范围: {} 到 {}", 
                  maintenance_start.format("%Y-%m-%d %H:%M:%S UTC"),
                  maintenance_end.format("%Y-%m-%d %H:%M:%S UTC"));

        if symbols.is_empty() {
            log::warn!("⚠️  没有找到需要维护的交易符号，跳过历史数据维护");
            return Ok(());
        }

        // 获取配置的时间级别
        let intervals = vec![
            "1m".to_string(),
            "5m".to_string(), 
            "15m".to_string(),
            "1h".to_string(),
            "4h".to_string(),
            "1d".to_string(),
        ];

        log::info!("🚀 开始为 {} 个交易符号维护 {} 个时间级别的历史数据", symbols.len(), intervals.len());

        let mut processed_symbols = 0;
        let mut total_records_fixed = 0;

        for symbol in symbols {
            log::info!("🔧 维护交易对: {} (多时间级别精确检测)", symbol.symbol);
            
            // 使用新的多时间级别检测方法
            match self.check_multi_interval_data_integrity(
                &symbol.symbol, 
                &intervals, 
                maintenance_start, 
                maintenance_end
            ).await {
                Ok(reports) => {
                    let total_gaps: usize = reports.iter().map(|r| r.gaps_found.len()).sum();
                    
                    if total_gaps > 0 {
                        log::info!("🔍 {} 发现 {} 个缺口，开始修复", symbol.symbol, total_gaps);
                        
                        match self.fix_detected_gaps(&symbol.symbol, reports).await {
                            Ok(fixed_count) => {
                                processed_symbols += 1;
                                total_records_fixed += fixed_count;
                                log::info!("✅ 成功修复 {} 的 {} 条记录", symbol.symbol, fixed_count);
                            }
                            Err(e) => {
                                log::error!("❌ 修复 {} 缺口失败: {}", symbol.symbol, e);
                            }
                        }
                    } else {
                        processed_symbols += 1;
                        log::info!("✅ {} 的历史数据完整，无需修复", symbol.symbol);
                    }
                }
                Err(e) => {
                    log::error!("❌ 检查 {} 数据完整性失败: {}", symbol.symbol, e);
                }
            }

            // 每处理10个符号休息一下，避免过载
            if processed_symbols % 10 == 0 {
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }

        log::info!("🎉 历史数据维护完成，处理了 {} 个符号，总共修复了 {} 条记录，耗时: {:?}", 
                  processed_symbols, total_records_fixed, start_time.elapsed());
        Ok(())
    }

    /// 为指定符号填补历史数据缺口
    async fn fill_historical_data_gaps(
        &self, 
        symbol: &Symbol, 
        start_time: DateTime<Utc>, 
        end_time: DateTime<Utc>
    ) -> Result<usize> {
        let gaps = self.detect_data_gaps(&symbol.symbol, start_time, end_time).await?;
        
        if gaps.is_empty() {
            return Ok(0);
        }

        log::info!("符号 {} 发现 {} 个数据缺口", symbol.symbol, gaps.len());
        
        let mut filled_gaps = 0;
        
        let client = self.exchange_clients.get(&symbol.exchange)
            .ok_or_else(|| anyhow::anyhow!("不支持的交易所: {}", symbol.exchange))?;
        
        for gap in gaps {
            match self.fetch_historical_klines_for_gap(client.as_ref(), symbol, gap.clone()).await {
                Ok(records_inserted) => {
                    filled_gaps += 1;
                    log::debug!("填补符号 {} 的数据缺口 ({} 到 {})，插入了 {} 条记录", 
                              symbol.symbol, 
                              gap.start.format("%Y-%m-%d %H:%M:%S"),
                              gap.end.format("%Y-%m-%d %H:%M:%S"),
                              records_inserted);
                }
                Err(e) => {
                    log::error!("填补符号 {} 数据缺口失败: {}", symbol.symbol, e);
                }
            }
            
            tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        }
        
        Ok(filled_gaps)
    }

    /// 检测数据缺口（改进版本 - 更精确的缺口检测）
    async fn detect_data_gaps(
        &self, 
        symbol: &str, 
        start_time: DateTime<Utc>, 
        end_time: DateTime<Utc>
    ) -> Result<Vec<DataGap>> {
        log::info!("检测符号 {} 的数据缺口，时间范围: {} 到 {}", 
                  symbol, 
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        // 首先检查是否有任何数据
        let has_any_data = self.check_data_exists(symbol, start_time, end_time).await?;
        if !has_any_data {
            log::info!("符号 {} 在指定时间范围内完全没有数据，将整个时间范围作为缺口", symbol);
            // 如果完全没有数据，将整个时间范围分割成较小的缺口以避免API限制
            return self.split_large_gap_into_chunks(start_time, end_time);
        }

        let mut gaps = Vec::new();
        let mut current = start_time;
        
        // 使用天为单位来检测缺口（更适合历史数据）
        while current < end_time {
            let next_day = current + chrono::Duration::days(1);
            let day_end = if next_day > end_time { end_time } else { next_day };
            
            let has_data = self.check_data_exists(symbol, current, day_end).await?;
            
            if !has_data {
                // 找到缺口的开始
                let gap_start = current;
                
                // 继续寻找缺口的结束
                let mut gap_end = day_end;
                while gap_end < end_time {
                    let next_check = gap_end + chrono::Duration::days(1);
                    let check_end = if next_check > end_time { end_time } else { next_check };
                    
                    let has_data_in_next = self.check_data_exists(symbol, gap_end, check_end).await?;
                    if has_data_in_next {
                        break;
                    }
                    gap_end = check_end;
                }
                
                // 将大的缺口分割成小块
                let gap_chunks = self.split_large_gap_into_chunks(gap_start, gap_end)?;
                gaps.extend(gap_chunks);
                
                current = gap_end;
            } else {
                current = day_end;
            }
        }
        
        log::info!("符号 {} 检测到 {} 个数据缺口", symbol, gaps.len());
        Ok(gaps)
    }

    /// 将大的数据缺口分割成小块，避免API限制
    fn split_large_gap_into_chunks(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> Result<Vec<DataGap>> {
        let mut chunks = Vec::new();
        let mut current = start_time;
        
        // 每个块最多包含12小时的数据（约720条1分钟K线，留有余量）
        let chunk_duration = chrono::Duration::hours(12);
        
        while current < end_time {
            let chunk_end = std::cmp::min(current + chunk_duration, end_time);
            
            chunks.push(DataGap {
                start: current,
                end: chunk_end,
            });
            
            current = chunk_end;
        }
        
        log::info!("将时间范围 {} 到 {} 分割为 {} 个数据块", 
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"),
                  chunks.len());
        
        Ok(chunks)
    }

    /// 合并相邻的数据缺口以减少API调用次数
    fn merge_adjacent_gaps(&self, gaps: Vec<DataGap>) -> Result<Vec<DataGap>> {
        if gaps.is_empty() {
            return Ok(gaps);
        }

        let mut merged_gaps = Vec::new();
        let mut current_gap = gaps[0].clone();

        for gap in gaps.into_iter().skip(1) {
            // 如果缺口之间的间隔小于1小时，则合并
            if gap.start - current_gap.end <= chrono::Duration::hours(1) {
                current_gap.end = gap.end;
            } else {
                merged_gaps.push(current_gap);
                current_gap = gap;
            }
        }
        
        merged_gaps.push(current_gap);
        Ok(merged_gaps)
    }

    /// 检查指定时间范围内是否存在数据
    async fn check_data_exists(
        &self, 
        symbol: &str, 
        start_time: DateTime<Utc>, 
        end_time: DateTime<Utc>
    ) -> Result<bool> {
        // 查询指定时间范围内是否有数据
        let market_query = repository::timescale::MarketDataQuery {
            symbol: symbol.to_string(),
            start_time,
            end_time,
            interval_type: "1m".to_string(),
            limit: Some(1), // 只需要检查是否有数据，不需要查询全部
        };

        let market_data = self.kline_repo.query(market_query).await?;
        Ok(!market_data.is_empty())
    }

    /// 获取历史K线数据填补缺口（改进版本）
    async fn fetch_historical_klines_for_gap(
        &self, 
        client: &dyn ExchangeClient,
        symbol: &Symbol, 
        gap: DataGap
    ) -> Result<usize> {
        log::info!("开始获取符号 {} 的历史数据，时间范围: {} 到 {}", 
                   symbol.symbol, 
                   gap.start.format("%Y-%m-%d %H:%M:%S"),
                   gap.end.format("%Y-%m-%d %H:%M:%S"));

        let query = KlineQuery {
            symbol: symbol.symbol.clone(),
            interval: "1m".to_string(),
            start_time: Some(gap.start.timestamp_millis()),
            end_time: Some(gap.end.timestamp_millis()),
            limit: Some(1000), // 保持1000的限制，但通过分块来处理大时间范围
        };

        let klines = client.get_klines(query).await?;
        
        let records_count = klines.len();
        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests(&symbol.symbol, &klines);
            log::info!("准备插入符号 {} 的 {} 条K线数据", symbol.symbol, requests.len());
            
            if let Some(ref storage) = self.storage {
                match storage.kline_manager().batch_insert(requests).await {
                    Ok(inserted) => {
                        log::info!("成功插入符号 {} 的 {} 条K线数据（实际受影响行数: {}）", 
                                  symbol.symbol, records_count, inserted);
                    }
                    Err(e) => {
                        log::warn!("插入符号 {} 的K线数据时发生错误: {}，这可能是由于数据重复导致的，已通过UPSERT处理", 
                                 symbol.symbol, e);
                        // 即使有错误也不返回错误，因为可能是重复数据，已通过ON CONFLICT处理
                    }
                }
            }
            
            // 添加延迟以避免API限制
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        } else {
            log::warn!("符号 {} 在时间范围 {} 到 {} 没有获取到数据", 
                       symbol.symbol,
                       gap.start.format("%Y-%m-%d %H:%M:%S"),
                       gap.end.format("%Y-%m-%d %H:%M:%S"));
        }

        Ok(records_count)
    }

    /// 将通用K线数据转换为数据库请求
    fn convert_klines_to_requests(&self, symbol: &str, klines: &[UniversalKline]) -> Vec<CreateMarketDataRequest> {
        klines.iter().map(|kline| {
            CreateMarketDataRequest {
                symbol: symbol.to_string(),
                timestamp: DateTime::from_timestamp_millis(kline.open_time)
                    .unwrap_or_else(Utc::now),
                open: kline.open_price.parse().unwrap_or_default(),
                high: kline.high_price.parse().unwrap_or_default(),
                low: kline.low_price.parse().unwrap_or_default(),
                close: kline.close_price.parse().unwrap_or_default(),
                volume: kline.volume.parse().unwrap_or_default(),
                interval_type: "1m".to_string(),
                quote_asset_volume: kline.quote_asset_volume.parse().unwrap_or_default(),
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse().unwrap_or_default(),
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse().unwrap_or_default(),
                close_time: DateTime::from_timestamp_millis(kline.close_time)
                    .unwrap_or_else(Utc::now),
            }
        }).collect()
    }

    /// 获取服务状态
    pub fn get_maintenance_status(&self) -> MaintenanceStatus {
        MaintenanceStatus {
            is_running: !self.shutdown_signal.load(Ordering::Relaxed),
            last_full_scan: self.last_full_scan,
            last_incremental_scan: self.last_incremental_scan,
        }
    }

    /// 手动触发数据同步（可用于API接口）
    pub async fn sync_symbol_data(&self, symbol: &str, start_time: Option<DateTime<Utc>>, end_time: Option<DateTime<Utc>>) -> Result<u64> {
        // 检查是否有对应的交易所客户端
        let exchange = "binance"; // 默认使用binance，实际应该从symbols表查询
        let client = self.exchange_clients.get(exchange)
            .ok_or_else(|| anyhow::anyhow!("不支持的交易所: {}", exchange))?;

        let query = KlineQuery {
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            start_time: start_time.map(|t| t.timestamp_millis()),
            end_time: end_time.map(|t| t.timestamp_millis()),
            limit: Some(1000),
        };

        let klines = client.get_klines(query).await?;
        
        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests(symbol, &klines);
            let inserted = self.kline_repo.batch_insert(requests).await?;
            log::info!("手动同步符号 {} 数据，插入了 {} 条记录", symbol, inserted);
            Ok(inserted)
        } else {
            Ok(0)
        }
    }

    // =============================================================================
    // 缓存维护方法 (整合自cache_service)
    // =============================================================================

    /// 处理新的K线数据到缓存（实时数据）
    /// 
    /// 对于新数据，使用固定的366天TTL
    /// 这确保了缓存窗口的一致性
    pub async fn handle_new_klines_to_cache(
        &self,
        symbol: &str,
        interval: &str,
        klines: &[KlineEntity],
    ) -> Result<(), CacheError> {
        if klines.is_empty() {
            return Ok(());
        }

        log::debug!("处理 {} 个新K线数据到缓存: {} {}", klines.len(), symbol, interval);

        // 转换为KlineDto并按日期分组
        let klines_by_date = self.group_klines_by_date(klines);

        // 为每个日期写入缓存
        for (date, date_klines) in klines_by_date {
            // 添加K线数据到缓存
            self.cache_repo
                .add_klines_for_day(symbol, interval, &date, &date_klines)
                .await?;

            // 为新数据设置固定TTL（366天）
            let ttl = TtlCalculator::get_fixed_ttl();
            self.cache_repo
                .set_expiry_for_day(symbol, &date, ttl)
                .await?;

            log::info!(
                "已缓存 {} {} 的 {} 条新K线数据，TTL: {}秒",
                symbol, date, date_klines.len(), ttl
            );
        }

        Ok(())
    }

    /// 批量处理多个交易对的K线数据到缓存
    pub async fn handle_multiple_symbols_klines_to_cache(
        &self,
        klines_data: Vec<(String, String, Vec<KlineEntity>)>, // (symbol, interval, klines)
    ) -> Result<Vec<Result<(), CacheError>>, CacheError> {
        let mut results = Vec::new();

        for (symbol, interval, klines) in klines_data {
            let result = self.handle_new_klines_to_cache(&symbol, &interval, &klines).await;
            results.push(result);
        }

        Ok(results)
    }

    /// 更新现有缓存的TTL
    /// 
    /// 用于定期维护任务，确保历史数据的TTL正确计算
    pub async fn refresh_ttl_for_symbol(
        &self,
        symbol: &str,
        days_back: u32,
    ) -> Result<u32, CacheError> {
        let current_date = TtlCalculator::get_current_date_string();
        let mut updated_count = 0;

        // 计算需要更新的日期范围
        let end_date = chrono::Utc::now().date_naive();
        let start_date = end_date - chrono::Duration::days(days_back as i64);

        let mut current = start_date;
        while current <= end_date {
            let date_str = current.format("%Y%m%d").to_string();

            // 检查该日期的缓存是否存在
            if self.cache_repo.exists_for_day(symbol, &date_str).await? {
                // 计算新的TTL
                let ttl = if date_str == current_date {
                    TtlCalculator::get_fixed_ttl()
                } else {
                    TtlCalculator::calculate_dynamic_ttl(&date_str, &current_date)?
                };

                // 更新TTL
                self.cache_repo.set_expiry_for_day(symbol, &date_str, ttl).await?;
                updated_count += 1;

                log::debug!("已更新 {} {} 的TTL: {}秒", symbol, date_str, ttl);
            }

            current = current.succ_opt().unwrap_or(current);
        }

        log::info!("已更新 {} 的 {} 个日期的TTL", symbol, updated_count);
        Ok(updated_count)
    }

    /// 清理过期的缓存键
    /// 
    /// 删除超出366天窗口的缓存数据
    pub async fn cleanup_expired_cache(
        &self,
        symbol: &str,
    ) -> Result<u32, CacheError> {
        let cutoff_date = chrono::Utc::now().date_naive() - chrono::Duration::days(366);
        let mut deleted_count = 0;

        // 检查过去400天的数据（留一些余量）
        let check_start = cutoff_date - chrono::Duration::days(34);
        let mut current = check_start;

        while current <= cutoff_date {
            let date_str = current.format("%Y%m%d").to_string();

            // 检查并删除过期的缓存
            if self.cache_repo.exists_for_day(symbol, &date_str).await? {
                self.cache_repo.delete_day_cache(symbol, &date_str).await?;
                deleted_count += 1;
                log::debug!("已删除过期缓存: {} {}", symbol, date_str);
            }

            current = current.succ_opt().unwrap_or(current);
        }

        if deleted_count > 0 {
            log::info!("已清理 {} 的 {} 个过期缓存", symbol, deleted_count);
        }

        Ok(deleted_count)
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self, symbol: &str) -> Result<CacheStats, CacheError> {
        let stats = self.cache_repo.get_stats(symbol).await?;
        Ok(stats)
    }

    /// 预热缓存
    /// 
    /// 为指定的交易对和时间范围预加载数据到缓存
    pub async fn warmup_cache(
        &self,
        symbol: &str,
        interval: &str,
        days: u32,
    ) -> Result<u32, CacheError> {
        log::info!("开始为 {} {} 预热缓存，天数: {}", symbol, interval, days);

        let end_date = chrono::Utc::now().date_naive();
        let start_date = end_date - chrono::Duration::days(days as i64);
        let mut warmed_count = 0;

        let mut current = start_date;
        while current <= end_date {
            let date_str = current.format("%Y%m%d").to_string();

            // 检查缓存是否已存在
            if !self.cache_repo.exists_for_day(symbol, &date_str).await? {
                // 从数据库查询该日期的数据
                let start_time = current.and_hms_opt(0, 0, 0).unwrap().and_utc();
                let end_time = current.and_hms_opt(23, 59, 59).unwrap().and_utc();

                let market_query = repository::timescale::MarketDataQuery {
                    symbol: symbol.to_string(),
                    start_time,
                    end_time,
                    interval_type: interval.to_string(),
                    limit: None,
                };

                if let Ok(market_data) = self.kline_repo.query(market_query).await {
                    if !market_data.is_empty() {
                        let klines: Vec<KlineEntity> = market_data.into_iter().map(|data| KlineEntity {
                            time: data.timestamp,
                            symbol: data.symbol,
                            interval: data.interval_type,
                            open_price: data.open,
                            high_price: data.high,
                            low_price: data.low,
                            close_price: data.close,
                            volume: data.volume,
                            quote_asset_volume: Decimal::ZERO,
                            number_of_trades: 0,
                            taker_buy_base_asset_volume: Decimal::ZERO,
                            taker_buy_quote_asset_volume: Decimal::ZERO,
                            close_time: data.timestamp,
                        }).collect();

                        // 写入缓存
                        if let Err(e) = self.handle_new_klines_to_cache(symbol, interval, &klines).await {
                            log::warn!("预热缓存失败 {} {}: {}", symbol, date_str, e);
                        } else {
                            warmed_count += 1;
                        }
                    }
                }
            }

            current = current.succ_opt().unwrap_or(current);
        }

        log::info!("预热缓存完成，为 {} {} 预热了 {} 天的数据", symbol, interval, warmed_count);
        Ok(warmed_count)
    }

    /// 运行缓存维护任务
    pub async fn run_cache_maintenance(&self, symbols: &[String]) -> Result<MaintenanceReport> {
        let mut report = MaintenanceReport::new();
        
        log::info!("开始缓存维护任务，处理 {} 个交易对", symbols.len());

        for symbol in symbols {
            report.processed_symbols += 1;

            // 更新TTL
            match self.refresh_ttl_for_symbol(symbol, 366).await {
                Ok(updated) => {
                    report.ttl_updates += updated;
                }
                Err(e) => {
                    let error_msg = format!("更新 {} TTL失败: {}", symbol, e);
                    log::error!("{}", error_msg);
                    report.errors.push(error_msg);
                }
            }

            // 清理过期缓存
            match self.cleanup_expired_cache(symbol).await {
                Ok(deleted) => {
                    report.deleted_keys += deleted;
                }
                Err(e) => {
                    let error_msg = format!("清理 {} 过期缓存失败: {}", symbol, e);
                    log::error!("{}", error_msg);
                    report.errors.push(error_msg);
                }
            }
        }

        log::info!(
            "缓存维护任务完成: 处理{}个交易对, 更新{}个TTL, 删除{}个过期键, {}个错误",
            report.processed_symbols,
            report.ttl_updates,
            report.deleted_keys,
            report.errors.len()
        );

        Ok(report)
    }

    /// 将KlineEntity按日期分组转换为KlineDto
    fn group_klines_by_date(&self, klines: &[KlineEntity]) -> HashMap<String, Vec<KlineDto>> {
        let mut klines_by_date: HashMap<String, Vec<KlineDto>> = HashMap::new();
        
        for kline in klines {
            let date = KlineDto::timestamp_to_date_string(kline.time.timestamp());
            let dto = KlineDto {
                time: kline.time,
                symbol: kline.symbol.clone(),
                interval: kline.interval.clone(),
                open_time: kline.time,
                close_time: kline.close_time,
                open_price: kline.open_price,
                high_price: kline.high_price,
                low_price: kline.low_price,
                close_price: kline.close_price,
                volume: kline.volume,
                quote_asset_volume: kline.quote_asset_volume,
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            };
            
            klines_by_date.entry(date).or_insert_with(Vec::new).push(dto);
        }

        klines_by_date
    }

    /// 基于时间桶的精确缺口检测（优化版本）
    /// 
    /// 使用TimescaleDB的time_bucket函数按指定时间级别检测数据完整性
    pub async fn detect_gaps_with_time_buckets(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<GapDetectionReport> {
        log::info!("🔍 开始基于时间桶的精确缺口检测: {} {} ({} 到 {})", 
                  symbol, interval,
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        let bucket_interval = self.get_postgres_interval(interval)?;
        let expected_records_per_bucket = self.get_expected_records_per_bucket(interval);

        // 使用时间桶查询统计每个桶的数据量
        let bucket_counts = self.query_time_bucket_counts(
            symbol, 
            interval, 
            &bucket_interval, 
            start_time, 
            end_time
        ).await?;

        // 计算预期的总桶数
        let bucket_duration = self.parse_interval_to_duration(&bucket_interval)?;
        let aligned_start = self.align_to_bucket_start(start_time, &bucket_interval)?;
        let total_expected_buckets = ((end_time - aligned_start).num_minutes() / bucket_duration.num_minutes()) as i64;

        let mut gaps_found = Vec::new();
        let mut missing_ranges = Vec::new();

        // 检查现有桶的数据完整性
        for bucket_count in &bucket_counts {
            if bucket_count.actual_count < expected_records_per_bucket {
                gaps_found.push(TimeBucketGap {
                    interval: interval.to_string(),
                    time_bucket: bucket_count.time_bucket,
                    expected_count: expected_records_per_bucket,
                    actual_count: bucket_count.actual_count,
                });

                // 如果完全缺失数据，添加到缺失范围
                if bucket_count.actual_count == 0 {
                    let bucket_end = self.calculate_bucket_end_time(
                        bucket_count.time_bucket, 
                        interval
                    )?;
                    
                    missing_ranges.push(DataGap {
                        start: bucket_count.time_bucket,
                        end: bucket_end,
                    });
                }
            }
        }

        // 检测完全缺失的时间范围（连续的缺失桶）
        if bucket_counts.len() < total_expected_buckets as usize {
            log::info!("🔍 检测连续缺失的时间范围...");
            let continuous_gaps = self.detect_continuous_missing_ranges(
                &bucket_counts, 
                &bucket_interval, 
                aligned_start, 
                end_time
            ).await?;
            missing_ranges.extend(continuous_gaps);
        }

        // 合并相邻的缺失范围
        let merged_ranges = self.merge_adjacent_gaps(missing_ranges)?;

        let report = GapDetectionReport {
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            total_buckets_checked: total_expected_buckets,
            gaps_found,
            missing_data_ranges: merged_ranges,
        };

        log::info!("✅ 缺口检测完成: {} {} - 预期{}个桶，实际{}个桶，发现{}个缺口，{}个缺失范围", 
                  symbol, interval,
                  total_expected_buckets,
                  bucket_counts.len(),
                  report.gaps_found.len(),
                  report.missing_data_ranges.len());

        Ok(report)
    }

    /// 检测连续缺失的时间范围
    async fn detect_continuous_missing_ranges(
        &self,
        existing_buckets: &[TimeBucketCount],
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<DataGap>> {
        let mut missing_ranges = Vec::new();
        let bucket_duration = self.parse_interval_to_duration(bucket_interval)?;
        
        // 创建现有桶的时间集合，用于快速查找
        let existing_times: std::collections::HashSet<DateTime<Utc>> = existing_buckets
            .iter()
            .map(|b| b.time_bucket)
            .collect();

        let mut current_time = start_time;
        let mut gap_start: Option<DateTime<Utc>> = None;

        // 以较大的步长检查，避免逐分钟遍历
        let check_interval = if bucket_duration.num_minutes() <= 60 {
            chrono::Duration::hours(1) // 对于小时间间隔，以小时为单位检查
        } else {
            bucket_duration * 10 // 对于大时间间隔，以10倍间隔检查
        };

        while current_time < end_time {
            let aligned_time = self.align_to_bucket_start(current_time, bucket_interval)?;
            
            if !existing_times.contains(&aligned_time) {
                // 发现缺失桶
                if gap_start.is_none() {
                    gap_start = Some(aligned_time);
                }
            } else {
                // 找到存在的桶，结束当前缺失范围
                if let Some(start) = gap_start {
                    missing_ranges.push(DataGap {
                        start,
                        end: aligned_time,
                    });
                    gap_start = None;
                }
            }

            current_time += check_interval;
        }

        // 处理最后一个缺失范围
        if let Some(start) = gap_start {
            missing_ranges.push(DataGap {
                start,
                end: end_time,
            });
        }

        log::info!("🔍 连续缺失范围检测完成: 发现{}个连续缺失范围", missing_ranges.len());
        Ok(missing_ranges)
    }

    /// 查询时间桶数据统计（优化版本 - 直接使用TimescaleDB聚合）
    async fn query_time_bucket_counts(
        &self,
        symbol: &str,
        interval: &str,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketCount>> {
        log::info!("📊 执行时间桶聚合查询: {} {} (桶大小: {})", symbol, interval, bucket_interval);
        
        // 使用KlineRepository的新方法来执行时间桶聚合查询
        let bucket_counts = self.kline_repo.query_time_bucket_aggregation(
            symbol,
            interval,
            bucket_interval,
            start_time,
            end_time,
        ).await?;

        log::info!("✅ 时间桶查询完成: 找到{}个非空桶", bucket_counts.len());

        // 填充缺失的时间桶（数据量为0的桶）
        self.fill_missing_buckets(bucket_counts, bucket_interval, start_time, end_time).await
    }

    /// 填充缺失的时间桶（优化版本 - 只检测缺口，不生成完整序列）
    async fn fill_missing_buckets(
        &self,
        existing_buckets: Vec<TimeBucketCount>,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketCount>> {
        log::info!("🔍 分析时间桶缺口: 已有{}个桶，时间范围 {} 到 {}", 
                  existing_buckets.len(),
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        // 对于数据完整性检查，我们不需要生成完整的时间序列
        // 只需要返回现有的桶，缺失的桶会在后续的缺口检测中被识别
        
        // 计算预期的总桶数（用于统计）
        let bucket_duration = self.parse_interval_to_duration(bucket_interval)?;
        let aligned_start = self.align_to_bucket_start(start_time, bucket_interval)?;
        let total_expected_buckets = ((end_time - aligned_start).num_minutes() / bucket_duration.num_minutes()) as usize;
        
        log::info!("📊 时间桶统计: 预期{}个桶，实际找到{}个桶，缺失{}个桶", 
                  total_expected_buckets,
                  existing_buckets.len(),
                  total_expected_buckets.saturating_sub(existing_buckets.len()));

        // 直接返回现有的桶，不生成完整序列
        // 缺口检测将基于现有桶与预期桶的对比来进行
        Ok(existing_buckets)
    }

    /// 获取PostgreSQL时间间隔字符串
    fn get_postgres_interval(&self, interval: &str) -> Result<String> {
        let pg_interval = match interval {
            "1m" => "1 minute",
            "5m" => "5 minutes", 
            "15m" => "15 minutes",
            "1h" => "1 hour",
            "4h" => "4 hours",
            "1d" => "1 day",
            _ => return Err(anyhow::anyhow!("不支持的时间间隔: {}", interval)),
        };
        Ok(pg_interval.to_string())
    }

    /// 获取每个时间桶期望的记录数量
    fn get_expected_records_per_bucket(&self, interval: &str) -> i64 {
        match interval {
            "1m" => 1,    // 1分钟桶期望1条记录
            "5m" => 1,    // 5分钟桶期望1条记录
            "15m" => 1,   // 15分钟桶期望1条记录
            "1h" => 1,    // 1小时桶期望1条记录
            "4h" => 1,    // 4小时桶期望1条记录
            "1d" => 1,    // 1天桶期望1条记录
            _ => 1,       // 默认期望1条记录
        }
    }

    /// 计算时间桶结束时间
    fn calculate_bucket_end_time(&self, bucket_start: DateTime<Utc>, interval: &str) -> Result<DateTime<Utc>> {
        let duration = match interval {
            "1m" => chrono::Duration::minutes(1),
            "5m" => chrono::Duration::minutes(5),
            "15m" => chrono::Duration::minutes(15),
            "1h" => chrono::Duration::hours(1),
            "4h" => chrono::Duration::hours(4),
            "1d" => chrono::Duration::days(1),
            _ => return Err(anyhow::anyhow!("不支持的时间间隔: {}", interval)),
        };
        Ok(bucket_start + duration)
    }

    /// 解析间隔字符串为Duration
    fn parse_interval_to_duration(&self, interval: &str) -> Result<chrono::Duration> {
        let duration = match interval {
            "1 minute" => chrono::Duration::minutes(1),
            "5 minutes" => chrono::Duration::minutes(5),
            "15 minutes" => chrono::Duration::minutes(15),
            "1 hour" => chrono::Duration::hours(1),
            "4 hours" => chrono::Duration::hours(4),
            "1 day" => chrono::Duration::days(1),
            _ => return Err(anyhow::anyhow!("无法解析时间间隔: {}", interval)),
        };
        Ok(duration)
    }

    /// 将时间对齐到时间桶的开始
    fn align_to_bucket_start(&self, time: DateTime<Utc>, interval: &str) -> Result<DateTime<Utc>> {
        use chrono::Timelike;
        
        let aligned = match interval {
            "1 minute" => time.with_second(0).unwrap().with_nanosecond(0).unwrap(),
            "5 minutes" => {
                let minute = (time.minute() / 5) * 5;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            },
            "15 minutes" => {
                let minute = (time.minute() / 15) * 15;
                time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            },
            "1 hour" => time.with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap(),
            "4 hours" => {
                let hour = (time.hour() / 4) * 4;
                time.with_hour(hour).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap()
            },
            "1 day" => time.with_hour(0).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap(),
            _ => return Err(anyhow::anyhow!("无法对齐时间间隔: {}", interval)),
        };
        Ok(aligned)
    }

    /// 多时间级别的数据完整性检查
    pub async fn check_multi_interval_data_integrity(
        &self,
        symbol: &str,
        intervals: &[String],
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<GapDetectionReport>> {
        log::info!("🔍 开始多时间级别数据完整性检查: {} ({} 到 {})", 
                  symbol,
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        let mut reports = Vec::new();
        
        for interval in intervals {
            log::info!("📊 检查时间级别: {} {}", symbol, interval);
            
            match self.detect_gaps_with_time_buckets(symbol, interval, start_time, end_time).await {
                Ok(report) => {
                    if !report.gaps_found.is_empty() {
                        log::warn!("⚠️  发现缺口: {} {} - {}个缺口", symbol, interval, report.gaps_found.len());
                    }
                    reports.push(report);
                }
                Err(e) => {
                    log::error!("❌ 检查 {} {} 时发生错误: {}", symbol, interval, e);
                    // 继续检查其他时间级别
                }
            }
        }

        log::info!("✅ 多时间级别检查完成: {} - 检查了{}个时间级别", symbol, reports.len());
        Ok(reports)
    }

    /// 修复检测到的数据缺口
    pub async fn fix_detected_gaps(
        &self,
        symbol: &str,
        reports: Vec<GapDetectionReport>,
    ) -> Result<usize> {
        log::info!("🔧 开始修复检测到的数据缺口: {}", symbol);
        
        let mut total_fixed = 0;
        
        for report in reports {
            if report.missing_data_ranges.is_empty() {
                continue;
            }
            
            log::info!("🔧 修复 {} {} 的{}个缺失范围", symbol, report.interval, report.missing_data_ranges.len());
            
            // 获取交易所客户端
            if let Some(client) = self.exchange_clients.get("binance") {
                for gap in report.missing_data_ranges {
                    // 创建Symbol结构体
                    let symbol_info = repository::timescale::Symbol {
                        symbol: symbol.to_string(),
                        is_active: true,
                        exchange: "binance".to_string(),
                        created_at: chrono::Utc::now(),
                        updated_at: chrono::Utc::now(),
                        deleted_at: None,
                    };
                    
                    match self.fetch_historical_klines_for_gap_with_interval(
                        client.as_ref(), 
                        &symbol_info, 
                        gap,
                        &report.interval
                    ).await {
                        Ok(count) => {
                            total_fixed += count;
                            log::info!("✅ 成功修复缺口: {} {} - {}条记录", symbol, report.interval, count);
                        }
                        Err(e) => {
                            log::error!("❌ 修复缺口失败: {} {} - {}", symbol, report.interval, e);
                        }
                    }
                }
            }
        }
        
        log::info!("🎉 缺口修复完成: {} - 总共修复{}条记录", symbol, total_fixed);
        Ok(total_fixed)
    }

    /// 支持指定时间级别的历史数据获取
    async fn fetch_historical_klines_for_gap_with_interval(
        &self, 
        client: &dyn ExchangeClient,
        symbol: &repository::timescale::Symbol, 
        gap: DataGap,
        interval: &str,
    ) -> Result<usize> {
        log::info!("📥 获取历史数据: {} {} ({} 到 {})", 
                   symbol.symbol, interval,
                   gap.start.format("%Y-%m-%d %H:%M:%S"),
                   gap.end.format("%Y-%m-%d %H:%M:%S"));

        let query = KlineQuery {
            symbol: symbol.symbol.clone(),
            interval: interval.to_string(),
            start_time: Some(gap.start.timestamp_millis()),
            end_time: Some(gap.end.timestamp_millis()),
            limit: Some(1000),
        };

        let klines = client.get_klines(query).await?;
        
        let records_count = klines.len();
        if !klines.is_empty() {
            let requests = self.convert_klines_to_requests_with_interval(&symbol.symbol, &klines, interval);
            log::info!("💾 准备插入 {} {} 的{}条记录", symbol.symbol, interval, requests.len());
            
            if let Some(ref storage) = self.storage {
                match storage.kline_manager().batch_insert(requests).await {
                    Ok(inserted) => {
                        log::info!("✅ 成功插入 {} {} 的{}条记录（实际受影响: {}）", 
                                  symbol.symbol, interval, records_count, inserted);
                    }
                    Err(e) => {
                        log::warn!("⚠️  插入 {} {} 数据时出现警告: {}（可能是重复数据，已通过UPSERT处理）", 
                                 symbol.symbol, interval, e);
                    }
                }
            }
            
            // 添加延迟避免API限制
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        } else {
            log::warn!("⚠️  未获取到数据: {} {} ({} 到 {})", 
                       symbol.symbol, interval,
                       gap.start.format("%Y-%m-%d %H:%M:%S"),
                       gap.end.format("%Y-%m-%d %H:%M:%S"));
        }

        Ok(records_count)
    }

    /// 转换K线数据为数据库请求（支持指定时间级别）
    fn convert_klines_to_requests_with_interval(
        &self, 
        symbol: &str, 
        klines: &[UniversalKline], 
        interval: &str
    ) -> Vec<CreateMarketDataRequest> {
        klines.iter().map(|kline| {
            let open_time = DateTime::from_timestamp_millis(kline.open_time)
                .unwrap_or_else(Utc::now);
            let close_time = DateTime::from_timestamp_millis(kline.close_time)
                .unwrap_or_else(|| open_time + chrono::Duration::minutes(1));
                
            CreateMarketDataRequest {
                symbol: symbol.to_string(),
                timestamp: open_time,
                open: kline.open_price.parse().unwrap_or_default(),
                high: kline.high_price.parse().unwrap_or_default(),
                low: kline.low_price.parse().unwrap_or_default(),
                close: kline.close_price.parse().unwrap_or_default(),
                volume: kline.volume.parse().unwrap_or_default(),
                interval_type: interval.to_string(), // 使用指定的时间级别
                quote_asset_volume: kline.quote_asset_volume.parse().unwrap_or_default(),
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse().unwrap_or_default(),
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse().unwrap_or_default(),
                close_time,
            }
        }).collect()
    }

    /// 公共API: 手动触发多时间级别数据完整性检查
    /// 
    /// 这是一个公共方法，可以被外部调用来检查和修复特定交易对的数据完整性
    pub async fn manual_data_integrity_check(
        &self,
        symbol: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        intervals: Option<Vec<String>>,
    ) -> Result<Vec<GapDetectionReport>> {
        let start_time = start_time.unwrap_or_else(|| {
            chrono::DateTime::parse_from_rfc3339("2024-06-01T00:00:00Z")
                .unwrap()
                .with_timezone(&chrono::Utc)
        });
        
        let end_time = end_time.unwrap_or_else(|| chrono::Utc::now());
        
        let intervals = intervals.unwrap_or_else(|| vec![
            "1m".to_string(),
            "5m".to_string(), 
            "15m".to_string(),
            "1h".to_string(),
            "4h".to_string(),
            "1d".to_string(),
        ]);

        log::info!("🔍 手动触发数据完整性检查: {} ({} 到 {})", 
                  symbol,
                  start_time.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        self.check_multi_interval_data_integrity(symbol, &intervals, start_time, end_time).await
    }

    /// 公共API: 手动修复检测到的数据缺口
    /// 
    /// 这是一个公共方法，可以被外部调用来修复特定交易对的数据缺口
    pub async fn manual_fix_data_gaps(
        &self,
        symbol: &str,
        reports: Vec<GapDetectionReport>,
    ) -> Result<usize> {
        log::info!("🔧 手动触发数据缺口修复: {}", symbol);
        self.fix_detected_gaps(symbol, reports).await
    }

    /// 公共API: 一键检查并修复数据完整性
    /// 
    /// 结合检查和修复功能的便捷方法
    pub async fn check_and_fix_data_integrity(
        &self,
        symbol: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        intervals: Option<Vec<String>>,
    ) -> Result<(Vec<GapDetectionReport>, usize)> {
        log::info!("🚀 一键检查并修复数据完整性: {}", symbol);
        
        // 第一步：检查数据完整性
        let reports = self.manual_data_integrity_check(symbol, start_time, end_time, intervals).await?;
        
        // 第二步：修复发现的缺口
        let fixed_count = if reports.iter().any(|r| !r.gaps_found.is_empty()) {
            self.manual_fix_data_gaps(symbol, reports.clone()).await?
        } else {
            0
        };
        
        Ok((reports, fixed_count))
    }

    /// 获取数据完整性统计报告
    /// 
    /// 返回各个时间级别的数据完整性统计信息
    pub async fn get_data_integrity_stats(
        &self,
        symbol: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> Result<DataIntegrityStats> {
        let start_time = start_time.unwrap_or_else(|| {
            chrono::DateTime::parse_from_rfc3339("2024-06-01T00:00:00Z")
                .unwrap()
                .with_timezone(&chrono::Utc)
        });
        
        let end_time = end_time.unwrap_or_else(|| chrono::Utc::now());
        
        let intervals = vec![
            "1m".to_string(),
            "5m".to_string(), 
            "15m".to_string(),
            "1h".to_string(),
            "4h".to_string(),
            "1d".to_string(),
        ];

        let reports = self.check_multi_interval_data_integrity(symbol, &intervals, start_time, end_time).await?;
        
        let mut stats = DataIntegrityStats {
            symbol: symbol.to_string(),
            check_time_range: (start_time, end_time),
            interval_stats: Vec::new(),
            total_gaps: 0,
            total_missing_ranges: 0,
        };

        for report in reports {
            stats.interval_stats.push(IntervalIntegrityStats {
                interval: report.interval.clone(),
                total_buckets_checked: report.total_buckets_checked,
                gaps_found: report.gaps_found.len() as i64,
                missing_ranges: report.missing_data_ranges.len() as i64,
                integrity_percentage: if report.total_buckets_checked > 0 {
                    ((report.total_buckets_checked - report.gaps_found.len() as i64) as f64 
                     / report.total_buckets_checked as f64 * 100.0)
                } else {
                    100.0
                },
            });
            
            stats.total_gaps += report.gaps_found.len();
            stats.total_missing_ranges += report.missing_data_ranges.len();
        }

        Ok(stats)
    }

    /// Redis缓存重整 - 全量扫描完成后执行
    /// 
    /// 使用新的存储格式：交易所:symbol:日期为键，ZSET存储，score为时间戳
    /// 值格式：O:open:H:high:L:low:C:close:V:volume
    async fn reorganize_redis_cache(&self, symbols: &[Symbol]) -> Result<()> {
        log::info!("🔄 开始Redis缓存重整 - 使用新的存储格式");
        let start_time = Instant::now();

        if symbols.is_empty() {
            log::warn!("⚠️  没有交易对需要重整缓存");
            return Ok(());
        }

        // 定义需要缓存的时间级别
        let intervals = vec!["1m", "5m", "15m", "1h", "4h", "1d"];
        
        // 定义缓存的时间范围（最近30天）
        let end_time = Utc::now();
        let start_time_cache = end_time - chrono::Duration::days(30);

        log::info!("📅 缓存重整时间范围: {} 到 {}", 
                  start_time_cache.format("%Y-%m-%d %H:%M:%S"),
                  end_time.format("%Y-%m-%d %H:%M:%S"));

        let mut total_processed = 0;
        let mut total_cached_records = 0;
        let mut total_deleted_keys = 0;

        for symbol in symbols {
            log::info!("🔧 重整缓存: {} ({})", symbol.symbol, symbol.exchange);
            
            // 为每个时间级别重整缓存
            for interval in &intervals {
                match self.reorganize_symbol_interval_cache(
                    &symbol.symbol,
                    &symbol.exchange,
                    interval,
                    start_time_cache,
                    end_time,
                ).await {
                    Ok((cached_count, deleted_count)) => {
                        total_cached_records += cached_count;
                        total_deleted_keys += deleted_count;
                        log::debug!("✅ {} {} - 缓存{}条记录，删除{}个旧键", 
                                   symbol.symbol, interval, cached_count, deleted_count);
                    }
                    Err(e) => {
                        log::error!("❌ 重整 {} {} 缓存失败: {}", symbol.symbol, interval, e);
                    }
                }
            }
            
            total_processed += 1;
            
            // 每处理10个交易对输出一次进度
            if total_processed % 10 == 0 {
                log::info!("📊 缓存重整进度: {}/{} 交易对完成", total_processed, symbols.len());
            }
        }

        log::info!("✅ Redis缓存重整完成！");
        log::info!("📊 重整统计:");
        log::info!("  - 处理交易对: {} 个", total_processed);
        log::info!("  - 缓存记录数: {} 条", total_cached_records);
        log::info!("  - 删除旧键数: {} 个", total_deleted_keys);
        log::info!("  - 总耗时: {:?}", start_time.elapsed());

        Ok(())
    }

    /// 重整单个交易对单个时间级别的缓存
    async fn reorganize_symbol_interval_cache(
        &self,
        symbol: &str,
        exchange: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<(usize, usize)> {
        // 1. 删除旧的缓存键
        let deleted_count = self.delete_old_cache_keys(symbol, start_time, end_time).await?;

        // 2. 从数据库查询数据
        let klines = self.query_klines_for_cache(symbol, interval, start_time, end_time).await?;
        
        if klines.is_empty() {
            return Ok((0, deleted_count));
        }

        // 3. 按日期分组数据并存储到新格式
        let mut cached_count = 0;
        let grouped_klines = self.group_klines_by_date(&klines);

        for (date, day_klines) in grouped_klines {
            let cache_count = self.store_klines_with_new_format(
                exchange,
                symbol,
                &date,
                &day_klines,
            ).await?;
            cached_count += cache_count;
        }

        Ok((cached_count, deleted_count))
    }

    /// 删除旧的缓存键
    async fn delete_old_cache_keys(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<usize> {
        let mut deleted_count = 0;
        let mut current_date = start_time.date_naive();
        let end_date = end_time.date_naive();

        while current_date <= end_date {
            let date_str = current_date.format("%Y%m%d").to_string();
            
            // 删除旧格式的键 (kline:symbol:date)
            match self.cache_repo.delete_day_cache(symbol, &date_str).await {
                Ok(_) => {
                    deleted_count += 1;
                    log::debug!("🗑️  删除旧缓存键: kline:{}:{}", symbol.to_lowercase(), date_str);
                }
                Err(e) => {
                    log::warn!("⚠️  删除旧缓存键失败: {}", e);
                }
            }

            current_date = current_date.succ_opt().unwrap_or(current_date);
        }

        Ok(deleted_count)
    }

    /// 从数据库查询K线数据用于缓存
    async fn query_klines_for_cache(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<KlineEntity>> {
        let query = KlineQueryDto {
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            start_time: Some(start_time),
            end_time: Some(end_time),
            limit: None,
        };

        self.get_kline_data(query).await
    }

    /// 使用新格式存储K线数据到Redis
    /// 键格式: 交易所:symbol:日期
    /// 数据类型: ZSET，score为时间戳，value为压缩字符串
    async fn store_klines_with_new_format(
        &self,
        exchange: &str,
        symbol: &str,
        date: &str,
        klines: &[KlineDto],
    ) -> Result<usize> {
        if klines.is_empty() {
            return Ok(0);
        }

        // 生成新格式的键: 交易所:symbol:日期
        let cache_key = format!("{}:{}:{}", exchange.to_lowercase(), symbol.to_uppercase(), date);
        
        log::debug!("💾 存储到新缓存键: {} ({} 条记录)", cache_key, klines.len());

        // 检查cache_repo是否是RedisCacheRepository类型
        // 如果是，使用新格式方法；否则使用兼容方法
        let stored_count = if let Some(redis_cache) = self.cache_repo.as_any().downcast_ref::<repository::RedisCacheRepository>() {
            // 使用新格式的专用方法
            redis_cache.add_klines_for_day_new_format(
                exchange,
                symbol,
                "", // interval在新格式中不需要
                date,
                klines,
            ).await?
        } else {
            // 兼容旧格式
            self.cache_repo.add_klines_for_day(
                &cache_key,  // 使用新格式的键
                "",          // interval不需要，因为已经在键中体现
                date,
                klines,
            ).await?
        };

        // 设置TTL (30天)
        let ttl_seconds = 30 * 24 * 60 * 60; // 30天
        if let Err(e) = self.cache_repo.set_expiry_for_day(&cache_key, date, ttl_seconds).await {
            log::warn!("⚠️  设置缓存TTL失败: {}", e);
        }

        log::debug!("✅ 成功存储 {} 条记录到 {}", stored_count, cache_key);
        Ok(stored_count)
    }
}

/// 维护服务状态
#[derive(Debug)]
pub struct MaintenanceStatus {
    pub is_running: bool,
    pub last_full_scan: Option<DateTime<Utc>>,
    pub last_incremental_scan: Option<DateTime<Utc>>,
}

// 保持向后兼容性的类型别名
pub type MarketDataServiceImpl = MarketDataService;

#[cfg(test)]
mod time_bucket_tests {
    use super::*;
    use chrono::{TimeZone, Utc, Timelike};

    #[test]
    fn test_postgres_interval_mapping() {
        // 测试时间间隔映射逻辑
        let test_cases = vec![
            ("1m", "1 minute"),
            ("5m", "5 minutes"),
            ("15m", "15 minutes"),
            ("1h", "1 hour"),
            ("4h", "4 hours"),
            ("1d", "1 day"),
        ];
        
        for (input, expected) in test_cases {
            let result = match input {
                "1m" => "1 minute",
                "5m" => "5 minutes", 
                "15m" => "15 minutes",
                "1h" => "1 hour",
                "4h" => "4 hours",
                "1d" => "1 day",
                _ => panic!("不支持的时间间隔: {}", input),
            };
            assert_eq!(result, expected);
        }
    }

    #[test]
    fn test_expected_records_per_bucket() {
        // 测试每个时间桶期望的记录数量
        let intervals = vec!["1m", "5m", "15m", "1h", "4h", "1d"];
        
        for interval in intervals {
            let expected = 1; // 每个桶期望1条记录
            assert_eq!(expected, 1);
        }
    }

    #[test]
    fn test_time_bucket_alignment() {
        // 测试时间桶对齐逻辑
        let test_time = Utc.with_ymd_and_hms(2024, 6, 1, 12, 33, 45).unwrap();
        
        // 1分钟对齐：应该对齐到12:33:00
        let minute_aligned = test_time.with_second(0).unwrap().with_nanosecond(0).unwrap();
        assert_eq!(minute_aligned, Utc.with_ymd_and_hms(2024, 6, 1, 12, 33, 0).unwrap());
        
        // 5分钟对齐：应该对齐到12:30:00
        let minute = (test_time.minute() / 5) * 5;
        let five_min_aligned = test_time.with_minute(minute).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();
        assert_eq!(five_min_aligned, Utc.with_ymd_and_hms(2024, 6, 1, 12, 30, 0).unwrap());
        
        // 1小时对齐：应该对齐到12:00:00
        let hour_aligned = test_time.with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();
        assert_eq!(hour_aligned, Utc.with_ymd_and_hms(2024, 6, 1, 12, 0, 0).unwrap());
        
        // 1天对齐：应该对齐到00:00:00
        let day_aligned = test_time.with_hour(0).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();
        assert_eq!(day_aligned, Utc.with_ymd_and_hms(2024, 6, 1, 0, 0, 0).unwrap());
    }

    #[test]
    fn test_duration_calculation() {
        // 测试时间间隔计算
        let start_time = Utc.with_ymd_and_hms(2024, 6, 1, 12, 0, 0).unwrap();
        
        assert_eq!(start_time + chrono::Duration::minutes(1), 
                   Utc.with_ymd_and_hms(2024, 6, 1, 12, 1, 0).unwrap());
        
        assert_eq!(start_time + chrono::Duration::minutes(5), 
                   Utc.with_ymd_and_hms(2024, 6, 1, 12, 5, 0).unwrap());
        
        assert_eq!(start_time + chrono::Duration::hours(1), 
                   Utc.with_ymd_and_hms(2024, 6, 1, 13, 0, 0).unwrap());
        
        assert_eq!(start_time + chrono::Duration::days(1), 
                   Utc.with_ymd_and_hms(2024, 6, 2, 12, 0, 0).unwrap());
    }
}