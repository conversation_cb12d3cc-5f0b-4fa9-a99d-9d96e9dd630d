use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use serde::{Deserialize, Serialize};

use crate::domain::entity::OrderBookEntity;

/// 深度数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct OrderBookDto {
    /// 交易对符号
    pub symbol: String,
    /// 最后更新ID
    pub last_update_id: i64,
    /// 买单深度
    pub bids: Vec<OrderBookEntry>,
    /// 卖单深度
    pub asks: Vec<OrderBookEntry>,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
}

/// 深度条目 - 单个价格档位
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct OrderBookEntry {
    /// 价格
    pub price: Decimal,
    /// 数量
    pub quantity: Decimal,
}

impl From<OrderBookEntity> for OrderBookDto {
    fn from(entity: OrderBookEntity) -> Self {
        let bids: Vec<OrderBookEntry> = entity.bids.0.iter()
            .map(|(price, qty)| OrderBookEntry {
                price: *price,
                quantity: *qty,
            })
            .collect();

        let asks: Vec<OrderBookEntry> = entity.asks.0.iter()
            .map(|(price, qty)| OrderBookEntry {
                price: *price,
                quantity: *qty,
            })
            .collect();

        Self {
            symbol: entity.symbol,
            last_update_id: entity.last_update_id,
            bids,
            asks,
            timestamp: entity.time,
        }
    }
}

impl From<OrderBookDto> for OrderBookEntity {
    fn from(dto: OrderBookDto) -> Self {
        let bids: Vec<(Decimal, Decimal)> = dto.bids.iter()
            .map(|entry| (entry.price, entry.quantity))
            .collect();

        let asks: Vec<(Decimal, Decimal)> = dto.asks.iter()
            .map(|entry| (entry.price, entry.quantity))
            .collect();

        Self {
            time: dto.timestamp,
            symbol: dto.symbol,
            last_update_id: dto.last_update_id,
            bids: sqlx::types::Json(bids),
            asks: sqlx::types::Json(asks),
        }
    }
} 