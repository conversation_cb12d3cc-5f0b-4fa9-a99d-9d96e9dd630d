use anyhow::Result;
use chrono::{DateTime, Utc, Duration, Timelike};
use std::collections::{HashMap, HashSet};
use tracing::{info, warn, debug, error};

use repository::timescale::{KlineRepository, TimeBucketCount};

/// 优化的时间桶缺口检测器
/// 
/// 使用TimescaleDB的time_bucket功能进行高效的数据完整性检测
pub struct GapDetector {
    kline_repo: KlineRepository,
}

/// 缺口检测配置
#[derive(Debug, Clone)]
pub struct GapDetectionConfig {
    /// 检测的时间间隔
    pub intervals: Vec<String>,
    /// 批量检测的时间窗口大小（天）
    pub batch_window_days: i64,
    /// 并发检测的最大符号数
    pub max_concurrent_symbols: usize,
    /// 是否启用智能采样（对于大时间范围）
    pub enable_smart_sampling: bool,
    /// 采样率（当启用智能采样时）
    pub sampling_rate: f64,
}

/// 缺口检测结果
#[derive(Debug, Clone)]
pub struct GapDetectionResult {
    pub symbol: String,
    pub interval: String,
    pub total_buckets_expected: i64,
    pub total_buckets_found: i64,
    pub gaps: Vec<TimeBucketGap>,
    pub missing_ranges: Vec<DataGap>,
    pub detection_duration_ms: u64,
}

/// 时间桶缺口
#[derive(Debug, Clone)]
pub struct TimeBucketGap {
    pub time_bucket: DateTime<Utc>,
    pub expected_count: i64,
    pub actual_count: i64,
    pub gap_severity: GapSeverity,
}

/// 数据缺口
#[derive(Debug, Clone)]
pub struct DataGap {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
    pub gap_type: GapType,
}

/// 缺口严重程度
#[derive(Debug, Clone, PartialEq)]
pub enum GapSeverity {
    /// 轻微缺失（缺少少量数据）
    Minor,
    /// 中等缺失（缺少一半以上数据）
    Moderate,
    /// 严重缺失（完全没有数据）
    Severe,
}

/// 缺口类型
#[derive(Debug, Clone, PartialEq)]
pub enum GapType {
    /// 单个时间桶缺失
    SingleBucket,
    /// 连续时间桶缺失
    ContinuousRange,
    /// 稀疏数据（数据存在但不完整）
    SparseData,
}

impl Default for GapDetectionConfig {
    fn default() -> Self {
        Self {
            intervals: vec!["1m".to_string(), "5m".to_string(), "1h".to_string()],
            batch_window_days: 7,
            max_concurrent_symbols: 10,
            enable_smart_sampling: true,
            sampling_rate: 0.1, // 10%采样
        }
    }
}

impl GapDetector {
    /// 创建新的缺口检测器
    pub fn new(kline_repo: KlineRepository) -> Self {
        Self { kline_repo }
    }

    /// 执行优化的缺口检测
    pub async fn detect_gaps_optimized(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        config: &GapDetectionConfig,
    ) -> Result<GapDetectionResult> {
        let detection_start = std::time::Instant::now();
        
        info!("🔍 开始优化缺口检测: {} {} ({} 到 {})", 
              symbol, interval,
              start_time.format("%Y-%m-%d %H:%M:%S"),
              end_time.format("%Y-%m-%d %H:%M:%S"));

        // 1. 计算时间桶参数
        let bucket_interval = self.get_postgres_interval(interval)?;
        let bucket_duration = self.parse_interval_to_duration(interval)?;
        let aligned_start = self.align_to_bucket_start(start_time, &bucket_interval)?;
        
        // 2. 计算预期的桶数量
        let total_expected_buckets = self.calculate_expected_buckets(aligned_start, end_time, &bucket_duration);
        
        // 3. 智能采样策略（对于大时间范围）
        let (actual_start, actual_end, is_sampled) = if config.enable_smart_sampling && total_expected_buckets > 10000 {
            self.apply_smart_sampling(aligned_start, end_time, config.sampling_rate)
        } else {
            (aligned_start, end_time, false)
        };

        // 4. 执行时间桶聚合查询
        let bucket_counts = self.kline_repo.query_time_bucket_aggregation(
            symbol,
            interval,
            &bucket_interval,
            actual_start,
            actual_end,
        ).await?;

        // 5. 分析缺口
        let gaps = self.analyze_bucket_gaps(&bucket_counts, interval, actual_start, actual_end)?;
        let missing_ranges = self.detect_missing_ranges(&bucket_counts, &bucket_duration, actual_start, actual_end)?;

        let detection_duration = detection_start.elapsed().as_millis() as u64;
        
        let result = GapDetectionResult {
            symbol: symbol.to_string(),
            interval: interval.to_string(),
            total_buckets_expected: if is_sampled { 
                (total_expected_buckets as f64 * config.sampling_rate) as i64 
            } else { 
                total_expected_buckets 
            },
            total_buckets_found: bucket_counts.len() as i64,
            gaps,
            missing_ranges,
            detection_duration_ms: detection_duration,
        };

        info!("✅ 缺口检测完成: {} {} - 预期{}桶，找到{}桶，{}个缺口，{}ms", 
              symbol, interval,
              result.total_buckets_expected,
              result.total_buckets_found,
              result.gaps.len(),
              detection_duration);

        Ok(result)
    }

    /// 批量检测多个符号的缺口
    pub async fn batch_detect_gaps(
        &self,
        symbols: &[String],
        config: &GapDetectionConfig,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<HashMap<String, Vec<GapDetectionResult>>> {
        info!("🔍 开始批量缺口检测: {}个符号，{}个时间间隔", symbols.len(), config.intervals.len());
        
        let mut results = HashMap::new();
        
        // 使用信号量控制并发数
        let semaphore = std::sync::Arc::new(tokio::sync::Semaphore::new(config.max_concurrent_symbols));
        let mut tasks = Vec::new();

        for symbol in symbols {
            for interval in &config.intervals {
                let symbol_clone = symbol.clone();
                let interval_clone = interval.clone();
                let config_clone = config.clone();
                let detector = self.clone_detector();
                let permit = semaphore.clone().acquire_owned().await?;

                let task = tokio::spawn(async move {
                    let _permit = permit; // 持有许可证直到任务完成
                    detector.detect_gaps_optimized(&symbol_clone, &interval_clone, start_time, end_time, &config_clone).await
                });

                tasks.push((symbol.clone(), task));
            }
        }

        // 收集结果
        for (symbol, task) in tasks {
            match task.await? {
                Ok(result) => {
                    results.entry(symbol).or_insert_with(Vec::new).push(result);
                }
                Err(e) => {
                    error!("符号 {} 缺口检测失败: {}", symbol, e);
                }
            }
        }

        info!("✅ 批量缺口检测完成: 处理了{}个符号", results.len());
        Ok(results)
    }

    /// 分析时间桶缺口
    fn analyze_bucket_gaps(
        &self,
        bucket_counts: &[TimeBucketCount],
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<TimeBucketGap>> {
        let expected_records_per_bucket = self.get_expected_records_per_bucket(interval);
        let mut gaps = Vec::new();

        for bucket in bucket_counts {
            if bucket.actual_count < expected_records_per_bucket {
                let severity = self.calculate_gap_severity(bucket.actual_count, expected_records_per_bucket);
                
                gaps.push(TimeBucketGap {
                    time_bucket: bucket.time_bucket,
                    expected_count: expected_records_per_bucket,
                    actual_count: bucket.actual_count,
                    gap_severity: severity,
                });
            }
        }

        Ok(gaps)
    }

    /// 检测缺失的时间范围
    fn detect_missing_ranges(
        &self,
        bucket_counts: &[TimeBucketCount],
        bucket_duration: &Duration,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<DataGap>> {
        let mut missing_ranges = Vec::new();
        
        // 创建现有桶的时间集合
        let existing_times: HashSet<DateTime<Utc>> = bucket_counts
            .iter()
            .map(|b| b.time_bucket)
            .collect();

        let mut current_time = start_time;
        let mut gap_start: Option<DateTime<Utc>> = None;

        while current_time < end_time {
            let aligned_time = self.align_to_bucket_start(current_time, &self.get_postgres_interval_from_duration(bucket_duration)?)?;
            
            if !existing_times.contains(&aligned_time) {
                // 发现缺失的桶
                if gap_start.is_none() {
                    gap_start = Some(aligned_time);
                }
            } else {
                // 找到数据，结束当前缺口
                if let Some(start) = gap_start {
                    let gap_type = if aligned_time - start <= *bucket_duration {
                        GapType::SingleBucket
                    } else {
                        GapType::ContinuousRange
                    };
                    
                    missing_ranges.push(DataGap {
                        start,
                        end: aligned_time,
                        gap_type,
                    });
                    gap_start = None;
                }
            }
            
            current_time = aligned_time + *bucket_duration;
        }

        // 处理最后一个缺口
        if let Some(start) = gap_start {
            missing_ranges.push(DataGap {
                start,
                end: end_time,
                gap_type: GapType::ContinuousRange,
            });
        }

        Ok(missing_ranges)
    }

    /// 计算缺口严重程度
    fn calculate_gap_severity(&self, actual_count: i64, expected_count: i64) -> GapSeverity {
        if actual_count == 0 {
            GapSeverity::Severe
        } else if actual_count < expected_count / 2 {
            GapSeverity::Moderate
        } else {
            GapSeverity::Minor
        }
    }

    /// 智能采样策略
    fn apply_smart_sampling(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        sampling_rate: f64,
    ) -> (DateTime<Utc>, DateTime<Utc>, bool) {
        let total_duration = end_time - start_time;
        let sample_duration = Duration::milliseconds((total_duration.num_milliseconds() as f64 * sampling_rate) as i64);
        
        // 从中间开始采样，这样更有代表性
        let sample_start = start_time + (total_duration - sample_duration) / 2;
        let sample_end = sample_start + sample_duration;
        
        (sample_start, sample_end, true)
    }

    /// 克隆检测器（用于并发处理）
    fn clone_detector(&self) -> Self {
        Self {
            kline_repo: self.kline_repo.clone(),
        }
    }

    // 辅助方法（从原有代码中提取）
    fn get_postgres_interval(&self, interval: &str) -> Result<String> {
        match interval {
            "1m" => Ok("1 minute".to_string()),
            "5m" => Ok("5 minutes".to_string()),
            "15m" => Ok("15 minutes".to_string()),
            "1h" => Ok("1 hour".to_string()),
            "4h" => Ok("4 hours".to_string()),
            "1d" => Ok("1 day".to_string()),
            _ => Err(anyhow::anyhow!("不支持的时间间隔: {}", interval)),
        }
    }

    fn parse_interval_to_duration(&self, interval: &str) -> Result<Duration> {
        match interval {
            "1m" => Ok(Duration::minutes(1)),
            "5m" => Ok(Duration::minutes(5)),
            "15m" => Ok(Duration::minutes(15)),
            "1h" => Ok(Duration::hours(1)),
            "4h" => Ok(Duration::hours(4)),
            "1d" => Ok(Duration::days(1)),
            _ => Err(anyhow::anyhow!("不支持的时间间隔: {}", interval)),
        }
    }

    fn get_expected_records_per_bucket(&self, interval: &str) -> i64 {
        match interval {
            "1m" => 1,
            "5m" => 5,
            "15m" => 15,
            "1h" => 60,
            "4h" => 240,
            "1d" => 1440,
            _ => 1,
        }
    }

    fn calculate_expected_buckets(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>, bucket_duration: &Duration) -> i64 {
        ((end_time - start_time).num_minutes() / bucket_duration.num_minutes()).max(0)
    }

    fn align_to_bucket_start(&self, time: DateTime<Utc>, _bucket_interval: &str) -> Result<DateTime<Utc>> {
        // 简化实现，对齐到分钟边界
        Ok(time.with_second(0).unwrap().with_nanosecond(0).unwrap())
    }

    fn get_postgres_interval_from_duration(&self, duration: &Duration) -> Result<String> {
        if duration.num_minutes() == 1 {
            Ok("1 minute".to_string())
        } else if duration.num_minutes() == 5 {
            Ok("5 minutes".to_string())
        } else if duration.num_hours() == 1 {
            Ok("1 hour".to_string())
        } else {
            Ok(format!("{} minutes", duration.num_minutes()))
        }
    }
}
