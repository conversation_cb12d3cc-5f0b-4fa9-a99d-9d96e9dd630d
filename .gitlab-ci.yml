image: rust:latest

variables:
  CARGO_HOME: $CI_PROJECT_DIR/.cargo
  APP_NAME: data_grpc

# 定义stages
stages:
  - pro
  - dev
  - acr
# 缓存依赖
cache:
  paths:
    - target/
    - .cargo/
# 编译
dev:
  stage: dev
  tags:
    - exchange
  when: manual
  script:
    - cargo --version
    - cargo build --release --timings
    - docker compose up --force-recreate --build -d
  artifacts:
    paths:
      - target/release/data_grpc
    expire_in: 1 week

# 构建Docker镜像并启动服务
pro:
  stage: pro
  tags:
    - release
  when: manual
  script:
    - cargo --version
    - cargo build --release --timings
    - docker compose -f docker-compose-test.yaml up --force-recreate --build -d
  artifacts:
    paths:
      - target/release/data_grpc
    expire_in: 1 week

acr:
  stage: acr
  tags:
    - release
  when: manual
  script:
    - make acr