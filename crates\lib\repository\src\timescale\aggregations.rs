use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::{Result, RepositoryError};

/// 时间聚合间隔
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TimeInterval {
    OneMinute,
    FiveMinutes,
    FifteenMinutes,
    OneHour,
    FourHours,
    OneDay,
    OneWeek,
}

impl TimeInterval {
    pub fn to_pg_interval(&self) -> &'static str {
        match self {
            Self::OneMinute => "1 minute",
            Self::FiveMinutes => "5 minutes",
            Self::FifteenMinutes => "15 minutes",
            Self::OneHour => "1 hour",
            Self::FourHours => "4 hours",
            Self::OneDay => "1 day",
            Self::OneWeek => "1 week",
        }
    }
}

/// 聚合OHLCV数据
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct AggregatedData {
    pub symbol: String,
    pub time_bucket: DateTime<Utc>,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub volume: Decimal,
    pub count: i64,
}

/// 价格统计数据
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct PriceStats {
    pub symbol: String,
    pub time_bucket: DateTime<Utc>,
    pub avg_price: Decimal,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub price_change: Option<Decimal>,
    pub price_change_percent: Option<Decimal>,
}

/// 交易量统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct VolumeStats {
    pub symbol: String,
    pub time_bucket: DateTime<Utc>,
    pub total_volume: Decimal,
    pub avg_volume: Decimal,
    pub max_volume: Decimal,
    pub trade_count: i64,
}

/// TimescaleDB聚合查询Repository
pub struct AggregationRepository {
    pool: PgPool,
}

impl AggregationRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取OHLCV聚合数据
    pub async fn get_ohlcv_aggregation(
        &self, 
        symbol: &str, 
        interval: TimeInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Vec<AggregatedData>> {
        let data = sqlx::query_as::<_, AggregatedData>(
            r#"
            SELECT 
                $1 as symbol,
                time_bucket($2::interval, time) as time_bucket,
                FIRST(open_price, time) as open,
                MAX(high_price) as high,
                MIN(low_price) as low,
                LAST(close_price, time) as close,
                SUM(volume) as volume,
                COUNT(*) as count
            FROM klines
            WHERE symbol = $1 
                AND time >= $3 
                AND time <= $4
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
        )
        .bind(symbol)
        .bind(interval.to_pg_interval())
        .bind(start_time)
        .bind(end_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(data)
    }

    /// 获取价格统计信息
    pub async fn get_price_stats(
        &self,
        symbol: &str,
        interval: TimeInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Vec<PriceStats>> {
        let stats = sqlx::query_as::<_, PriceStats>(
            r#"
            SELECT 
                $1 as symbol,
                time_bucket($2::interval, time) as time_bucket,
                AVG(close_price) as avg_price,
                MIN(low_price) as min_price,
                MAX(high_price) as max_price,
                LAST(close_price, time) - FIRST(open_price, time) as price_change,
                CASE 
                    WHEN FIRST(open_price, time) > 0 THEN
                        (LAST(close_price, time) - FIRST(open_price, time)) / FIRST(open_price, time) * 100
                    ELSE NULL
                END as price_change_percent
            FROM klines
            WHERE symbol = $1 
                AND time >= $3 
                AND time <= $4
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
        )
        .bind(symbol)
        .bind(interval.to_pg_interval())
        .bind(start_time)
        .bind(end_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取交易量统计
    pub async fn get_volume_stats(
        &self,
        symbol: &str,
        interval: TimeInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Vec<VolumeStats>> {
        let stats = sqlx::query_as::<_, VolumeStats>(
            r#"
            SELECT 
                $1 as symbol,
                time_bucket($2::interval, time) as time_bucket,
                SUM(volume) as total_volume,
                AVG(volume) as avg_volume,
                MAX(volume) as max_volume,
                COUNT(*) as trade_count
            FROM klines
            WHERE symbol = $1 
                AND time >= $3 
                AND time <= $4
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
        )
        .bind(symbol)
        .bind(interval.to_pg_interval())
        .bind(start_time)
        .bind(end_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取连续聚合视图数据 (如果配置了连续聚合)
    pub async fn get_continuous_aggregate(
        &self,
        view_name: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Vec<serde_json::Value>> {
        let query = format!(
            "SELECT * FROM {} WHERE time_bucket >= $1 AND time_bucket <= $2 ORDER BY time_bucket ASC",
            view_name
        );
        
        let rows: Vec<serde_json::Value> = sqlx::query_scalar(&query)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(&self.pool)
            .await?;

        Ok(rows)
    }

    /// 创建连续聚合视图
    pub async fn create_continuous_aggregate(
        &self,
        view_name: &str,
        interval: TimeInterval,
        table_name: &str
    ) -> Result<()> {
        let sql = format!(
            r#"
            SELECT add_continuous_aggregate_policy('{}',
                start_offset => INTERVAL '3 hours',
                end_offset => INTERVAL '1 hour',
                schedule_interval => INTERVAL '{}'
            )
            "#,
            view_name,
            interval.to_pg_interval()
        );

        sqlx::query(&sql)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    /// 获取最近N个时间间隔的数据
    pub async fn get_recent_aggregates(
        &self,
        symbol: &str,
        interval: TimeInterval,
        count: u32
    ) -> Result<Vec<AggregatedData>> {
        let data = sqlx::query_as::<_, AggregatedData>(
            r#"
            SELECT 
                $1 as symbol,
                time_bucket($2::interval, time) as time_bucket,
                FIRST(open_price, time) as open,
                MAX(high_price) as high,
                MIN(low_price) as low,
                LAST(close_price, time) as close,
                SUM(volume) as volume,
                COUNT(*) as count
            FROM klines
            WHERE symbol = $1 
                AND time >= NOW() - ($2::interval * $3)
            GROUP BY time_bucket
            ORDER BY time_bucket DESC
            LIMIT $3
            "#,
        )
        .bind(symbol)
        .bind(interval.to_pg_interval())
        .bind(count as i64)
        .fetch_all(&self.pool)
        .await?;

        Ok(data)
    }
} 