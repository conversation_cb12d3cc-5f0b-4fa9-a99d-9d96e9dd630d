use axum::extract::State;
use axum::{
    http::StatusCode,
    response::{IntoResponse, Json},
    routing::{Router, post, get},
};
use chrono::{DateTime, Utc, Duration};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;
use common::domain::dto::user::{LoginUserData, RegisterUserData, UserInfoData};
use common::domain::entity::user::User;
use common::domain::r#enum::error::AppError;

use crate::router::AppState;

// =============================================================================
// 通用响应格式 - 适配前端期望的 {success: boolean, data: ...} 格式
// =============================================================================

/// 通用成功响应格式
#[derive(Debug, Serialize, ToSchema)]
pub struct ApiResponse<T> {
    /// 操作是否成功
    pub success: bool,
    /// 响应数据
    pub data: T,
}

/// 通用错误响应格式
#[derive(Debug, Serialize, ToSchema)]
pub struct ApiErrorResponse {
    /// 操作是否成功
    pub success: bool,
    /// 错误消息
    pub message: String,
}

// =============================================================================
// 请求结构体
// =============================================================================

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct RegisterRequest {
    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: String,

    /// 密码（至少6位）
    #[validate(length(min = 6, message = "密码长度不能小于6位"))]
    pub password: String,

    /// 昵称（至少2位）
    #[validate(length(min = 2, message = "昵称长度不能小于2位"))]
    pub nickname: String,

    /// 验证码（6位数字）
    #[validate(length(equal = 6, message = "验证码长度必须为6位"))]
    pub verification_code: String,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct LoginRequest {
    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: String,

    /// 密码（至少6位）
    #[validate(length(min = 6, message = "密码长度不能小于6位"))]
    pub password: String,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SendVerificationCodeRequest {
    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: String,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct RefreshTokenRequest {
    /// 刷新令牌
    pub refresh_token: String,
}


/// 刷新令牌响应数据 - 适配前端 RefreshTokenResult.data 接口
#[derive(Debug, Serialize, ToSchema)]
pub struct RefreshTokenData {
    /// 新的访问令牌
    #[serde(rename = "accessToken")]
    pub access_token: String,
    /// 新的刷新令牌
    #[serde(rename = "refreshToken")]
    pub refresh_token: String,
    /// 令牌过期时间
    pub expires: DateTime<Utc>,
}


/// 验证码发送响应数据
#[derive(Debug, Serialize, ToSchema)]
pub struct SendCodeData {
    /// 消息
    pub message: String,
}


// =============================================================================
// API 处理函数
// =============================================================================

/// 发送验证码接口
#[utoipa::path(
    post,
    path = "/api/auth/verification-code",
    request_body = SendVerificationCodeRequest,
    responses(
        (status = 200, description = "验证码发送成功", body = ApiResponse<SendCodeData>),
        (status = 400, description = "请求参数错误"),
        (status = 429, description = "请求过于频繁"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "auth"
)]
pub async fn send_verification_code(
    State(state): State<AppState>, 
    Json(payload): Json<SendVerificationCodeRequest>
) -> Result<impl IntoResponse, AppError> {
    // 验证请求参数
    payload.validate().map_err(|e| AppError::BadRequest(format!("参数验证失败: {}", e.to_string())))?;

    state.auth_service().send_verification_code(payload.email).await?;

    let response = ApiResponse {
        success: true,
        data: SendCodeData {
            message: "验证码已发送到您的邮箱，请查收".to_string(),
        },
    };
    
    Ok((StatusCode::OK, Json(response)))
}

/// 用户注册接口
#[utoipa::path(
    post,
    path = "/api/auth/register",
    request_body = RegisterRequest,
    responses(
        (status = 201, description = "注册成功", body = ApiResponse<RegisterUserData>),
        (status = 400, description = "请求参数错误或用户已存在"),
        (status = 422, description = "验证码错误或已过期"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "auth"
)]
pub async fn register(
    State(state): State<AppState>, 
    Json(payload): Json<RegisterRequest>
) -> Result<impl IntoResponse, AppError> {
    // 验证请求参数
    payload.validate().map_err(|e| AppError::BadRequest(format!("参数验证失败: {}", e.to_string())))?;

    let user = state
        .auth_service()
        .register(payload.email, payload.password, payload.nickname, payload.verification_code)
        .await?;

    let response = ApiResponse {
        success: true,
        data: RegisterUserData::from(user),
    };

    Ok((StatusCode::CREATED, Json(response)))
}

/// 用户登录接口 - 适配前端 UserResult 接口
#[utoipa::path(
    post,
    path = "/api/auth/login",
    request_body = LoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResponse<LoginUserData>),
        (status = 400, description = "请求参数错误"),
        (status = 401, description = "邮箱或密码错误"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "auth"
)]
pub async fn login(
    State(state): State<AppState>, 
    Json(payload): Json<LoginRequest>
) -> Result<impl IntoResponse, AppError> {
    // 验证请求参数
    payload.validate().map_err(|e| AppError::BadRequest(format!("参数验证失败: {}", e.to_string())))?;

    let (access_token, refresh_token, user) = state.auth_service().login(payload.email, payload.password).await?;

    let mut user_data = LoginUserData::from(user);
    user_data.access_token = access_token;
    user_data.refresh_token = refresh_token;

    let response = ApiResponse {
        success: true,
        data: user_data,
    };

    Ok((StatusCode::OK, Json(response)))
}

/// 刷新令牌接口 - 适配前端 RefreshTokenResult 接口
#[utoipa::path(
    post,
    path = "/refresh-token",
    request_body = RefreshTokenRequest,
    responses(
        (status = 200, description = "令牌刷新成功", body = ApiResponse<RefreshTokenData>),
        (status = 400, description = "请求参数错误"),
        (status = 401, description = "刷新令牌无效或已过期"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "auth"
)]
pub async fn refresh_token(
    State(_state): State<AppState>, 
    Json(payload): Json<RefreshTokenRequest>
) -> Result<impl IntoResponse, AppError> {
    // TODO: 实现真实的令牌刷新逻辑
    // 目前返回模拟数据
    
    // 验证刷新令牌
    if payload.refresh_token.is_empty() {
        return Err(AppError::Unauthorized("刷新令牌不能为空".to_string()));
    }

    // 生成新的令牌
    let new_access_token = format!("new_access_token_{}", Utc::now().timestamp());
    let new_refresh_token = format!("new_refresh_token_{}", Utc::now().timestamp());
    let expires = Utc::now() + Duration::hours(24);

    let response = ApiResponse {
        success: true,
        data: RefreshTokenData {
            access_token: new_access_token,
            refresh_token: new_refresh_token,
            expires,
        },
    };

    Ok((StatusCode::OK, Json(response)))
}

/// 获取当前用户信息接口 - 适配前端 UserInfoResult 接口
#[utoipa::path(
    get,
    path = "/mine",
    responses(
        (status = 200, description = "获取用户信息成功", body = ApiResponse<UserInfoData>),
        (status = 401, description = "未授权访问"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "user",
    security(
        ("Bearer" = [])
    )
)]
pub async fn get_mine(
    State(_state): State<AppState>
    // TODO: 添加身份验证中间件来提取当前用户
) -> Result<impl IntoResponse, AppError> {
    // TODO: 从JWT令牌中提取用户信息
    // 目前返回模拟数据
    
    let user_info = UserInfoData {
        avatar: "https://via.placeholder.com/150".to_string(),
        username: "<EMAIL>".to_string(),
        nickname: "示例用户".to_string(),
        email: "<EMAIL>".to_string(),
        phone: "138****8888".to_string(),
        description: "这是一个示例用户描述".to_string(),
    };

    let response = ApiResponse {
        success: true,
        data: user_info,
    };

    Ok((StatusCode::OK, Json(response)))
}

/// 分页数据结构
#[derive(Debug, Serialize, ToSchema)]
pub struct PaginationData<T> {
    /// 列表数据
    pub list: Vec<T>,
    /// 总条目数
    pub total: Option<u64>,
    /// 每页显示条目个数
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    /// 当前页数
    #[serde(rename = "currentPage")]
    pub current_page: Option<u32>,
}

/// 安全日志条目
#[derive(Debug, Serialize, ToSchema)]
pub struct SecurityLogEntry {
    /// 日志ID
    pub id: String,
    /// 操作类型
    pub action: String,
    /// IP地址
    pub ip_address: String,
    /// 用户代理
    pub user_agent: String,
    /// 操作时间
    pub created_at: String,
    /// 操作结果
    pub result: String,
}

/// 获取用户安全日志接口 - 适配前端 getMineLogs 接口
#[utoipa::path(
    get,
    path = "/mine-logs",
    responses(
        (status = 200, description = "获取安全日志成功", body = ApiResponse<PaginationData<SecurityLogEntry>>),
        (status = 401, description = "未授权访问"),
        (status = 500, description = "服务器内部错误")
    ),
    tag = "user",
    security(
        ("Bearer" = [])
    )
)]
pub async fn get_mine_logs(
    State(_state): State<AppState>
    // TODO: 添加查询参数支持分页
    // TODO: 添加身份验证中间件来提取当前用户
) -> Result<impl IntoResponse, AppError> {
    // TODO: 实现真实的安全日志查询
    // 目前返回模拟数据
    
    let logs = vec![
        SecurityLogEntry {
            id: "1".to_string(),
            action: "登录".to_string(),
            ip_address: "*************".to_string(),
            user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
            created_at: Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
            result: "成功".to_string(),
        },
        SecurityLogEntry {
            id: "2".to_string(),
            action: "修改密码".to_string(),
            ip_address: "*************".to_string(),
            user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
            created_at: (Utc::now() - Duration::hours(1)).format("%Y-%m-%d %H:%M:%S").to_string(),
            result: "成功".to_string(),
        },
    ];

    let response = ApiResponse {
        success: true,
        data: PaginationData {
            list: logs,
            total: Some(2),
            page_size: Some(10),
            current_page: Some(1),
        },
    };

    Ok((StatusCode::OK, Json(response)))
}

/// 创建认证和用户路由
pub fn auth_routes() -> Router<AppState> {
    Router::new()
        // 认证相关路由
        .route("/verification-code", post(send_verification_code))
        .route("/register", post(register))
        
        // 直接路由（适配前端请求路径）
        .route("/login", post(login))
        .route("/refresh-token", post(refresh_token))
        .route("/mine", get(get_mine))
        .route("/mine-logs", get(get_mine_logs))
}
