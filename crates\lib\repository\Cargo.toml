[package]
name = "repository"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true

[dependencies]
common.workspace = true
nacos.workspace = true
exchange.workspace = true

tokio.workspace = true
anyhow.workspace = true
log.workspace = true
lazy_static.workspace = true
serde.workspace = true
serde_json.workspace = true
async-trait.workspace = true
chrono.workspace = true
sqlx.workspace = true
rust_decimal.workspace = true

r2d2_redis.workspace = true

bigdecimal.workspace = true
validator.workspace = true
dashmap.workspace = true
uuid.workspace = true
tonic.workspace = true
# 使用优先队列库
redis.workspace = true
once_cell.workspace = true
fred.workspace = true

tracing.workspace = true

reqwest.workspace = true

rdkafka.workspace = true

futures.workspace = true

nacos-sdk.workspace = true
taos.workspace = true
time = "0.3.37"
r2d2.workspace = true


retry = "2.1.0"
prometheus = "0.14.0"
rand_core = "0.6.4"
rand = "0.8.5"

argon2 = "0.5.3"
thiserror = "2.0.12"


[target.'cfg(windows)'.dependencies]
# 在 Windows 平台下，重新定义 rdkafka 依赖，并添加 cmake-build 特性。
# Cargo 会合并或覆盖全局 dependencies 中的配置，确保在 Windows 上启用这个特性。
rdkafka = { version = "0.37.0", features = ["cmake-build"] }

[dev-dependencies]
# Testing
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports", "async_tokio"] }
