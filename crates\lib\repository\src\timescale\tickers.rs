use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 价格数据实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct TickerEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub price: Decimal,
}

/// 价格数据插入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTickerRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub price: Decimal,
}

/// 价格数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickerQuery {
    pub symbol: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub limit: Option<u32>,
}

/// 价格统计
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct TickerStats {
    pub symbol: String,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub avg_price: Option<Decimal>,
    pub first_price: Option<Decimal>,
    pub last_price: Option<Decimal>,
    pub price_change: Option<Decimal>,
    pub price_change_percent: Option<Decimal>,
    pub count: i64,
}

/// 价格数据Repository
#[derive(Clone)]
pub struct TickerRepository {
    pool: PgPool,
}

impl TickerRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化价格表结构
    pub async fn initialize_table(&self) -> Result<()> {
        // 创建价格数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tickers (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                price DECIMAL NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将价格表转换为超级表
        log::info!("正在创建tickers超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("tickers")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建tickers超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("tickers超级表创建成功");

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_tickers_symbol_time
             ON tickers (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("价格表结构初始化完成");
        Ok(())
    }

    /// 插入单条价格数据
    pub async fn insert(&self, request: CreateTickerRequest) -> Result<TickerEntity> {
        let ticker = sqlx::query_as::<_, TickerEntity>(
            r#"
            INSERT INTO tickers (time, symbol, price)
            VALUES ($1, $2, $3)
            RETURNING time, symbol, price
            "#,
        )
        .bind(&request.timestamp)
        .bind(&request.symbol)
        .bind(&request.price)
        .fetch_one(&self.pool)
        .await?;

        Ok(ticker)
    }

    /// 批量插入价格数据
    pub async fn batch_insert(&self, requests: Vec<CreateTickerRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO tickers (time, symbol, price) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.timestamp)
                .push_bind(&request.symbol)
                .push_bind(&request.price);
        });

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询价格数据
    pub async fn query(&self, params: TickerQuery) -> Result<Vec<TickerEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT time, symbol, price FROM tickers WHERE symbol = "
        );
        query.push_bind(&params.symbol);
        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        query.push(" ORDER BY time ASC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let tickers: Vec<TickerEntity> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(tickers)
    }

    /// 获取最新价格
    pub async fn get_latest_price(&self, symbol: &str) -> Result<Option<TickerEntity>> {
        let ticker = sqlx::query_as::<_, TickerEntity>(
            "SELECT time, symbol, price FROM tickers WHERE symbol = $1 ORDER BY time DESC LIMIT 1"
        )
        .bind(symbol)
        .fetch_optional(&self.pool)
        .await?;

        Ok(ticker)
    }

    /// 获取多个交易对的最新价格
    pub async fn get_latest_prices(&self, symbols: &[String]) -> Result<Vec<TickerEntity>> {
        if symbols.is_empty() {
            return Ok(vec![]);
        }

        let mut query = sqlx::QueryBuilder::new(
            "SELECT DISTINCT ON (symbol) time, symbol, price FROM tickers WHERE symbol = ANY("
        );
        query.push_bind(symbols);
        query.push(") ORDER BY symbol, time DESC");

        let tickers: Vec<TickerEntity> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(tickers)
    }

    /// 获取价格统计
    pub async fn get_stats(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<TickerStats> {
        let stats = sqlx::query_as::<_, TickerStats>(
            r#"
            SELECT 
                $1 as symbol,
                MIN(price) as min_price,
                MAX(price) as max_price,
                AVG(price) as avg_price,
                (array_agg(price ORDER BY time ASC))[1] as first_price,
                (array_agg(price ORDER BY time DESC))[1] as last_price,
                (array_agg(price ORDER BY time DESC))[1] - (array_agg(price ORDER BY time ASC))[1] as price_change,
                CASE 
                    WHEN (array_agg(price ORDER BY time ASC))[1] != 0 THEN
                        (((array_agg(price ORDER BY time DESC))[1] - (array_agg(price ORDER BY time ASC))[1]) / (array_agg(price ORDER BY time ASC))[1]) * 100
                    ELSE 0
                END as price_change_percent,
                COUNT(*) as count
            FROM tickers 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取价格变化百分比
    pub async fn get_price_change_percent(
        &self,
        symbol: &str,
        duration: chrono::Duration,
    ) -> Result<Option<Decimal>> {
        let end_time = Utc::now();
        let start_time = end_time - duration;

        let result = sqlx::query_scalar::<_, Option<Decimal>>(
            r#"
            WITH first_last AS (
                SELECT 
                    (array_agg(price ORDER BY time ASC))[1] as first_price,
                    (array_agg(price ORDER BY time DESC))[1] as last_price
                FROM tickers 
                WHERE symbol = $1 AND time >= $2 AND time <= $3
            )
            SELECT 
                CASE 
                    WHEN first_price IS NOT NULL AND first_price != 0 THEN
                        ((last_price - first_price) / first_price) * 100
                    ELSE NULL
                END as price_change_percent
            FROM first_last
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    /// 获取价格历史（按时间间隔聚合）
    pub async fn get_price_history(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        interval: &str, // 例如: '5 minutes', '1 hour', '1 day'
    ) -> Result<Vec<TickerEntity>> {
        let query = format!(
            r#"
            SELECT 
                time_bucket('{}', time) as time,
                symbol,
                AVG(price) as price
            FROM tickers 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            GROUP BY time_bucket('{}', time), symbol
            ORDER BY time ASC
            "#,
            interval, interval
        );

        let tickers = sqlx::query_as::<_, TickerEntity>(&query)
            .bind(symbol)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(&self.pool)
            .await?;

        Ok(tickers)
    }

    /// 删除旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM tickers WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 获取所有活跃交易对
    pub async fn get_active_symbols(&self, within_duration: chrono::Duration) -> Result<Vec<String>> {
        let cutoff_time = Utc::now() - within_duration;

        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT symbol FROM tickers WHERE time >= $1 ORDER BY symbol"
        )
        .bind(cutoff_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(symbols)
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&self.pool).await?;
        Ok(())
    }
} 