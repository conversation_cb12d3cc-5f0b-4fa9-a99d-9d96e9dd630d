use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use serde::{Deserialize, Serialize};

use crate::domain::entity::TickerEntity;

/// 价格数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TickerDto {
    /// 时间戳
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 价格
    pub price: Decimal,
}

impl From<TickerEntity> for TickerDto {
    fn from(entity: TickerEntity) -> Self {
        Self {
            time: entity.time,
            symbol: entity.symbol,
            price: entity.price,
        }
    }
}

impl From<TickerDto> for TickerEntity {
    fn from(dto: TickerDto) -> Self {
        Self {
            time: dto.time,
            symbol: dto.symbol,
            price: dto.price,
        }
    }
} 