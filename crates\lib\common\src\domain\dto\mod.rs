pub mod kline;
pub mod ticker;
pub mod trade;
pub mod order_book;
pub mod stats_24hr;
pub mod query;
pub mod verification_code;
pub mod user;
pub mod token;
pub mod response;

// 重新导出DTO
pub use kline::{KlineDto, SimpleKline, KlineCacheError};
pub use ticker::TickerDto;
pub use trade::TradeDto;
pub use order_book::{OrderBookDto, OrderBookEntry};
pub use stats_24hr::Stats24hrDto;
pub use token::{Claims, RefreshClaims, AuthError, constants};
pub use user::{UserInfoData, LoginUserData, RegisterUserData, UserStatus};
pub use response::{
    ApiResponse, ApiErrorResponse, PaginationData, HealthData, 
    JwtUserInfo, ProtectedResourceData, OperationResult, SystemStats
};

// 重新导出查询DTO
pub use query::{
    KlineQueryDto,
    OrderBookQueryDto,
    StatsQueryDto,
    TickerQueryDto,
    TradeHistoryQueryDto,
};

