// =====================================================
// 集成重构的K线维护服务到user-server
// =====================================================

use anyhow::Result;
use sqlx::PgPool;
use tracing::{info, error};

use repository::timescale::kline_repository_v2::KlineRepositoryV2;
use repository::cache::kline_cache::KlineCache;
use service::market_data::{KlineMaintenanceServiceV2, MaintenanceConfig};
use exchange::binance::BinanceClient;

/// 初始化并启动重构的K线维护服务
pub async fn initialize_kline_maintenance_v2(
    timescale_pool: PgPool,
    redis_url: &str,
) -> Result<()> {
    info!("🚀 初始化K线维护服务 v2.0");

    // 1. 创建Redis缓存
    let cache = KlineCache::new(redis_url, "binance".to_string())?;
    
    // 2. 创建Repository (带缓存)
    let repository = KlineRepositoryV2::with_cache(timescale_pool, cache);

    // 3. 创建交易所客户端
    let exchange_client = Box::new(BinanceClient::new());

    // 4. 配置维护服务
    let config = MaintenanceConfig {
        exchange_name: "binance".to_string(),
        symbols: get_active_symbols(),
        update_interval_minutes: 5,
        integrity_check_interval_hours: 6,
        retention_days: 730,
        enable_auto_fix: true,
        enable_cache: true,
    };

    // 5. 创建维护服务
    let maintenance_service = KlineMaintenanceServiceV2::new(
        config,
        repository,
        exchange_client,
    );

    // 6. 启动服务 (在后台运行)
    tokio::spawn(async move {
        if let Err(e) = maintenance_service.start().await {
            error!("❌ K线维护服务启动失败: {}", e);
        }
    });

    info!("✅ K线维护服务 v2.0 启动成功");
    Ok(())
}

/// 获取活跃交易对列表
fn get_active_symbols() -> Vec<String> {
    vec![
        "BTCUSDT".to_string(),
        "ETHUSDT".to_string(),
        "BNBUSDT".to_string(),
        "ADAUSDT".to_string(),
        "SOLUSDT".to_string(),
        "XRPUSDT".to_string(),
        "DOTUSDT".to_string(),
        "DOGEUSDT".to_string(),
        "AVAXUSDT".to_string(),
        "MATICUSDT".to_string(),
    ]
}

/// 创建用于测试的简化版本
pub async fn create_test_maintenance_service(
    timescale_pool: PgPool,
) -> Result<KlineMaintenanceServiceV2> {
    info!("🧪 创建测试用K线维护服务");

    // 创建Repository (不带缓存)
    let repository = KlineRepositoryV2::new(timescale_pool);

    // 创建交易所客户端
    let exchange_client = Box::new(BinanceClient::new());

    // 测试配置
    let config = MaintenanceConfig {
        exchange_name: "binance".to_string(),
        symbols: vec!["BTCUSDT".to_string(), "ETHUSDT".to_string()],
        update_interval_minutes: 1, // 更频繁的更新用于测试
        integrity_check_interval_hours: 1,
        retention_days: 30,
        enable_auto_fix: true,
        enable_cache: false, // 测试时不使用缓存
    };

    let service = KlineMaintenanceServiceV2::new(
        config,
        repository,
        exchange_client,
    );

    info!("✅ 测试用K线维护服务创建成功");
    Ok(service)
}

/// 执行一次性的数据完整性检查和修复
pub async fn perform_one_time_integrity_check(
    timescale_pool: PgPool,
    symbols: Vec<String>,
    hours_back: i32,
) -> Result<()> {
    info!("🔍 执行一次性数据完整性检查");

    let repository = KlineRepositoryV2::new(timescale_pool);
    
    // 批量检查所有交易对
    let results = repository.check_all_symbols_integrity("binance", hours_back).await?;
    
    info!("📊 完整性检查结果:");
    for result in &results {
        info!("   {} - 完整性: {:.2}%, 缺失: {} 分钟, 延迟: {:?} 分钟", 
            result.symbol, 
            result.completeness_percentage,
            result.missing_count,
            result.data_lag_minutes
        );
    }

    // 统计总体情况
    let total_symbols = results.len();
    let healthy_symbols = results.iter().filter(|r| r.completeness_percentage >= 95.0).count();
    let warning_symbols = results.iter().filter(|r| r.completeness_percentage >= 90.0 && r.completeness_percentage < 95.0).count();
    let critical_symbols = results.iter().filter(|r| r.completeness_percentage < 90.0).count();

    info!("📈 总体状况:");
    info!("   总交易对: {}", total_symbols);
    info!("   健康 (≥95%): {}", healthy_symbols);
    info!("   警告 (90-95%): {}", warning_symbols);
    info!("   严重 (<90%): {}", critical_symbols);

    Ok(())
}

/// 获取数据延迟监控信息
pub async fn get_data_lag_monitoring(
    timescale_pool: PgPool,
) -> Result<()> {
    info!("⏰ 获取数据延迟监控信息");

    let repository = KlineRepositoryV2::new(timescale_pool);
    let lag_statuses = repository.get_data_lag_status().await?;

    info!("📊 数据延迟状态:");
    for status in &lag_statuses {
        let status_emoji = match status.status {
            repository::timescale::kline_repository_v2::LagStatus::Healthy => "✅",
            repository::timescale::kline_repository_v2::LagStatus::Warning => "⚠️",
            repository::timescale::kline_repository_v2::LagStatus::Critical => "❌",
        };
        
        info!("   {} {} - 延迟: {} 分钟, 最新数据: {}", 
            status_emoji,
            status.symbol,
            status.lag_minutes,
            status.latest_data_time.format("%Y-%m-%d %H:%M:%S")
        );
    }

    Ok(())
}

/// 执行数据质量检查
pub async fn perform_data_quality_check(
    timescale_pool: PgPool,
    symbol: &str,
    hours_back: i32,
) -> Result<()> {
    info!("🔍 执行数据质量检查: {}", symbol);

    let repository = KlineRepositoryV2::new(timescale_pool);
    let quality_issues = repository.check_data_quality(symbol, "binance", hours_back).await?;

    if quality_issues.is_empty() {
        info!("✅ {} 数据质量良好，未发现问题", symbol);
    } else {
        info!("⚠️ {} 发现 {} 个数据质量问题:", symbol, quality_issues.len());
        for issue in &quality_issues {
            info!("   {} - {}: {}", 
                issue.time.format("%Y-%m-%d %H:%M:%S"),
                issue.issue_type,
                issue.description
            );
        }
    }

    Ok(())
}

/// 检查聚合数据一致性
pub async fn check_aggregation_consistency(
    timescale_pool: PgPool,
    symbol: &str,
    date: chrono::NaiveDate,
) -> Result<()> {
    info!("🔍 检查聚合数据一致性: {} ({})", symbol, date);

    let repository = KlineRepositoryV2::new(timescale_pool);
    let consistency_results = repository.check_aggregation_consistency(symbol, "binance", date).await?;

    info!("📊 聚合一致性检查结果:");
    for result in &consistency_results {
        let status_emoji = if result.is_consistent { "✅" } else { "❌" };
        info!("   {} {} - 源数据: {}, 聚合数据: {}, 差异: {:.2}%", 
            status_emoji,
            result.time_period,
            result.source_count,
            result.aggregate_count,
            result.variance_percentage
        );
    }

    Ok(())
}

/// 清理过期数据
pub async fn cleanup_expired_data(
    timescale_pool: PgPool,
    retention_days: i32,
) -> Result<()> {
    info!("🧹 清理过期数据 (保留 {} 天)", retention_days);

    let repository = KlineRepositoryV2::new(timescale_pool);
    let deleted_count = repository.cleanup_expired_data(retention_days).await?;

    if deleted_count > 0 {
        info!("🗑️ 清理了 {} 条过期数据", deleted_count);
    } else {
        info!("✅ 没有需要清理的过期数据");
    }

    Ok(())
}

/// 获取活跃交易对列表
pub async fn get_active_symbols_from_db(
    timescale_pool: PgPool,
    hours_back: i32,
) -> Result<Vec<String>> {
    let repository = KlineRepositoryV2::new(timescale_pool);
    let symbols = repository.get_active_symbols("binance", hours_back).await?;
    
    info!("📋 发现 {} 个活跃交易对 (最近 {} 小时)", symbols.len(), hours_back);
    for symbol in &symbols {
        info!("   - {}", symbol);
    }

    Ok(symbols)
}
