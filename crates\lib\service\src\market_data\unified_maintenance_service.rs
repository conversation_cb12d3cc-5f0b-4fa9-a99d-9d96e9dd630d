// 统一的市场数据维护服务
// 整合完整性检查、增量更新和缓存同步功能

use chrono::{DateTime, Utc, Duration};
use repository::{
    timescale::klines::KlineRepository,
};
use exchange::ExchangeClient;
use anyhow::Result;
use tracing::{info, warn, error, debug};
use tokio::time::{interval, Duration as TokioDuration};
use std::collections::HashMap;
use crate::market_data::improved_integrity_checker::{ImprovedIntegrityChecker, SimpleIntegrityResult};

/// 维护服务配置
#[derive(Debug, Clone)]
pub struct MaintenanceConfig {
    /// 活跃交易对
    pub symbols: Vec<String>,
    /// 增量更新间隔（分钟）
    pub update_interval_minutes: u64,
    /// 完整性检查间隔（小时）
    pub integrity_check_interval_hours: u64,
    /// 缓存预热数据天数
    pub cache_warmup_days: i64,
    /// 是否启用自动修复
    pub enable_auto_fix: bool,
}

impl Default for MaintenanceConfig {
    fn default() -> Self {
        Self {
            symbols: vec!["BTCUSDT".to_string(), "ETHUSDT".to_string()],
            update_interval_minutes: 5,
            integrity_check_interval_hours: 24,
            cache_warmup_days: 365,
            enable_auto_fix: true,
        }
    }
}

/// 统一的市场数据维护服务
pub struct UnifiedMaintenanceService {
    config: MaintenanceConfig,
    kline_repo: KlineRepository,
    exchange_client: Box<dyn ExchangeClient>,
    integrity_checker: ImprovedIntegrityChecker,
}

impl UnifiedMaintenanceService {
    pub fn new(
        config: MaintenanceConfig,
        kline_repo: KlineRepository,
        exchange_client: Box<dyn ExchangeClient>,
    ) -> Self {
        let integrity_checker = ImprovedIntegrityChecker::new(
            kline_repo.clone(),
            exchange_client.clone_box(),
        );

        Self {
            config,
            kline_repo,
            exchange_client,
            integrity_checker,
        }
    }

    /// 启动维护服务
    pub async fn start(&self) -> Result<()> {
        info!("🚀 启动统一市场数据维护服务");

        // 1. 初始化：完整性检查和修复
        self.perform_initial_setup().await?;

        // 2. 启动定时任务
        self.start_scheduled_tasks().await?;

        Ok(())
    }

    /// 执行初始设置
    async fn perform_initial_setup(&self) -> Result<()> {
        info!("📋 执行初始设置...");

        // 1. 完整性检查
        let integrity_results = self.check_all_symbols_integrity().await?;
        
        // 2. 自动修复（如果启用）
        if self.config.enable_auto_fix {
            self.fix_integrity_issues(&integrity_results).await?;
        }

        // 3. 缓存预热（暂时跳过）
        info!("📦 缓存预热功能暂时跳过");

        info!("✅ 初始设置完成");
        Ok(())
    }

    /// 检查所有交易对的完整性
    async fn check_all_symbols_integrity(&self) -> Result<Vec<SimpleIntegrityResult>> {
        info!("🔍 检查所有交易对的数据完整性");

        let end_time = Utc::now();
        let start_time = end_time - Duration::days(self.config.cache_warmup_days);
        let mut results = Vec::new();

        for symbol in &self.config.symbols {
            info!("检查 {} 的完整性", symbol);
            
            match self.integrity_checker.check_integrity(symbol, start_time, end_time).await {
                Ok(result) => {
                    info!("✅ {} 完整性: {:.2}% (缺失 {} 分钟)", 
                        symbol, result.completeness_percentage, result.total_missing_minutes);
                    results.push(result);
                }
                Err(e) => {
                    error!("❌ {} 完整性检查失败: {}", symbol, e);
                }
            }
        }

        Ok(results)
    }

    /// 修复完整性问题
    async fn fix_integrity_issues(&self, results: &[SimpleIntegrityResult]) -> Result<()> {
        info!("🔧 开始修复数据完整性问题");

        let mut total_fixed = 0u64;

        for result in results {
            if !result.missing_ranges.is_empty() {
                info!("修复 {} 的 {} 个缺失时间段", result.symbol, result.missing_ranges.len());
                
                match self.integrity_checker.fix_missing_data_batch(
                    &result.symbol, 
                    &result.missing_ranges
                ).await {
                    Ok(fixed_count) => {
                        total_fixed += fixed_count;
                        info!("✅ {} 修复完成，修复了 {} 条数据", result.symbol, fixed_count);
                    }
                    Err(e) => {
                        error!("❌ {} 修复失败: {}", result.symbol, e);
                    }
                }
            }
        }

        info!("🎉 数据修复完成，总共修复了 {} 条数据", total_fixed);
        Ok(())
    }

    /// 缓存预热（暂时跳过）
    async fn warmup_cache(&self) -> Result<()> {
        info!("🔥 缓存预热功能暂时跳过");
        Ok(())
    }



    /// 启动定时任务
    async fn start_scheduled_tasks(&self) -> Result<()> {
        info!("⏰ 启动定时任务");

        // 增量更新任务
        let update_interval = TokioDuration::from_secs(self.config.update_interval_minutes * 60);
        let mut update_timer = interval(update_interval);

        // 完整性检查任务
        let integrity_interval = TokioDuration::from_secs(self.config.integrity_check_interval_hours * 3600);
        let mut integrity_timer = interval(integrity_interval);

        loop {
            tokio::select! {
                _ = update_timer.tick() => {
                    if let Err(e) = self.perform_incremental_update().await {
                        error!("增量更新失败: {}", e);
                    }
                }
                _ = integrity_timer.tick() => {
                    if let Err(e) = self.perform_scheduled_integrity_check().await {
                        error!("定时完整性检查失败: {}", e);
                    }
                }
            }
        }
    }

    /// 执行增量更新
    async fn perform_incremental_update(&self) -> Result<()> {
        debug!("🔄 执行增量更新");

        let end_time = Utc::now();
        let start_time = end_time - Duration::hours(1); // 获取最近1小时的数据

        for symbol in &self.config.symbols {
            if let Err(e) = self.update_symbol_data(symbol, start_time, end_time).await {
                warn!("更新 {} 数据失败: {}", symbol, e);
            }
        }

        Ok(())
    }

    /// 更新单个交易对数据
    async fn update_symbol_data(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<()> {
        // 1. 从交易所获取最新数据
        let query = exchange::KlineQuery {
            symbol: symbol.to_string(),
            interval: "1m".to_string(),
            start_time: Some(start_time.timestamp_millis()),
            end_time: Some(end_time.timestamp_millis()),
            limit: Some(100),
        };

        let klines = self.exchange_client.get_klines(query).await?;
        if klines.is_empty() {
            return Ok(());
        }

        // 2. 转换并保存到数据库
        let entities = self.integrity_checker.convert_to_entities(symbol, &klines)?;
        let saved_count = self.kline_repo.batch_insert_klines(&entities).await?;

        // 3. 更新缓存（暂时跳过）
        if saved_count > 0 {
            debug!("缓存更新功能暂时跳过");
        }

        debug!("✅ {} 增量更新完成，处理了 {} 条数据", symbol, saved_count);
        Ok(())
    }

    /// 定时完整性检查
    async fn perform_scheduled_integrity_check(&self) -> Result<()> {
        info!("🔍 执行定时完整性检查");

        let results = self.check_all_symbols_integrity().await?;
        
        if self.config.enable_auto_fix {
            self.fix_integrity_issues(&results).await?;
        }

        Ok(())
    }





    /// 获取服务状态
    pub async fn get_status(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut status = HashMap::new();
        
        status.insert("symbols_count".to_string(), 
            serde_json::Value::Number(serde_json::Number::from(self.config.symbols.len())));
        status.insert("update_interval_minutes".to_string(), 
            serde_json::Value::Number(serde_json::Number::from(self.config.update_interval_minutes)));
        status.insert("auto_fix_enabled".to_string(), 
            serde_json::Value::Bool(self.config.enable_auto_fix));

        Ok(status)
    }
}
