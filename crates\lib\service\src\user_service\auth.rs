use argon2::{
    Argon2,
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString, rand_core::OsRng},
};
use common::domain::entity::user::User;
use common::domain::r#enum::error::AppError;
use common::domain::dto::{Claims, RefreshClaims, constants};
use super::token::JwtService;
use rand::Rng;
use uuid::Uuid;
use common::domain::dto::user::UserStatus;
use repository::postgres::user::{UserRepository, CreateUserRequest};
use repository::connection::redis::{RedisPoolManager, RedisOperations};
use std::time::{SystemTime, UNIX_EPOCH};
use anyhow::{anyhow, Result};
use log::{error, info, warn};


pub struct AuthService {
    user_repo: UserRepository,
    redis_ops: RedisOperations,
}

impl AuthService {
    /// 创建AuthService实例，注入Repository和RedisOperations
    pub fn new(user_repo: UserRepository, redis_ops: RedisOperations) -> Self {
        Self { user_repo, redis_ops }
    }

    /// 将repository User转换为service User
    fn convert_user(repo_user: repository::postgres::user::User) -> User {
        User {
            id: repo_user.id,
            email: repo_user.email,
            password: repo_user.password_hash,
            nickname: repo_user.username,
            status: if repo_user.is_active { UserStatus::Active } else { UserStatus::Frozen },
            created_at: repo_user.created_at,
            updated_at: repo_user.updated_at,
        }
    }

    /// 生成验证码
    pub fn generate_verification_code() -> String {
        let mut rng = rand::thread_rng();
        let code: String = (0..6).map(|_| rng.gen_range(0..10).to_string()).collect();
        code
    }

    /// 发送验证码
    pub async fn send_verification_code(&self, email: String) -> Result<(), AppError> {
        // 生成6位数字验证码
        let verification_code = Self::generate_verification_code();
        
        // 将验证码存储到Redis，设置5分钟过期
        let redis_key = format!("verification_code:{}", email);
        self.redis_ops.set_ex(&redis_key, &verification_code, 300)
            .map_err(|e| AppError::Internal(format!("Failed to store verification code in Redis: {}", e)))?;
        
        // TODO: 这里应该调用邮件服务发送验证码
        info!("Verification code for {}: {}", email, verification_code);
        
        Ok(())
    }

    /// 注册用户
    pub async fn register(&self, email: String, password: String, nickname: String, verification_code: String) -> Result<User, AppError> {
        // 验证验证码
        self.verify_code(&email, &verification_code).await
            .map_err(|e| AppError::BadRequest(e.to_string()))?;
        
        // 检查邮箱是否已注册
        if let Ok(Some(_)) = self.user_repo.find_by_email(&email).await {
            return Err(AppError::BadRequest("邮箱已被注册".into()));
        }

        // 创建用户
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|_| AppError::Internal("密码加密失败".into()))?
            .to_string();

        let create_request = CreateUserRequest {
            email: email.clone(),
            username: nickname.clone(),
            password_hash,
        };

        // 保存用户
        let repo_user = self.user_repo.create(create_request).await
            .map_err(|e| AppError::Internal(format!("用户创建失败: {}", e)))?;

        // 删除已使用的验证码
        let redis_key = format!("verification_code:{}", email);
        let _ = self.redis_ops.del(&redis_key);

        Ok(Self::convert_user(repo_user))
    }

    /// 用户登录 - 返回访问令牌、刷新令牌和用户信息
    pub async fn login(&self, email: String, password: String) -> Result<(String, String, User), AppError> {
        // 查找用户
        let repo_user = self.user_repo.find_by_email(&email).await
            .map_err(|e| AppError::Internal(format!("查询用户失败: {}", e)))?
            .ok_or_else(|| AppError::BadRequest("用户不存在".into()))?;

        // 验证密码
        let parsed_hash = PasswordHash::new(&repo_user.password_hash).map_err(|_| AppError::Internal("密码解析失败".into()))?;
        let argon2 = Argon2::default();

        if argon2.verify_password(password.as_bytes(), &parsed_hash).is_err() {
            return Err(AppError::BadRequest("密码错误".into()));
        }

        let user = Self::convert_user(repo_user);

        // 检查用户状态
        if user.status != UserStatus::Active {
            return Err(AppError::BadRequest("用户账户已被禁用".into()));
        }

        // 生成访问令牌和刷新令牌
        let access_token = JwtService::generate_access_token(&user)
            .map_err(|e| AppError::Internal(e.to_string()))?;
        
        let refresh_token = JwtService::generate_refresh_token(&user)
            .map_err(|e| AppError::Internal(e.to_string()))?;

        // 将访问令牌存储到Redis，设置过期时间
        let access_token_key = format!("access_token:{}", user.id);
        self.redis_ops.set_ex(&access_token_key, &access_token, constants::ACCESS_TOKEN_EXPIRY as usize)
            .map_err(|e| AppError::Internal(format!("访问令牌存储失败: {}", e)))?;

        // 将刷新令牌存储到Redis，设置过期时间
        let refresh_token_key = format!("refresh_token:{}", user.id);
        self.redis_ops.set_ex(&refresh_token_key, &refresh_token, constants::REFRESH_TOKEN_EXPIRY as usize)
            .map_err(|e| AppError::Internal(format!("刷新令牌存储失败: {}", e)))?;

        // 为了兼容现有接口，先返回access_token
        // 后续可以改为返回(access_token, refresh_token, user)的元组
        Ok((access_token, refresh_token, user))
    }


    /// 验证验证码
    async fn verify_code(&self, email: &str, code: &str) -> Result<()> {
        let redis_key = format!("verification_code:{}", email);
        
        let stored_code: String = self.redis_ops.get(&redis_key)
            .map_err(|_| anyhow!("验证码已过期或不存在"))?;
        
        if stored_code != code {
            return Err(anyhow!("验证码错误"));
        }
        
        Ok(())
    }



    /// 验证访问令牌（包含Redis检查）
    pub async fn verify_token(&self, token: &str) -> Result<User> {
        // 首先进行JWT验证
        let claims = JwtService::verify_access_token(token)
            .map_err(|e| anyhow!("JWT验证失败: {}", e))?;
        
        // 检查Redis中是否存在该令牌（防止令牌被吊销）
        let redis_key = format!("access_token:{}", claims.sub);
        let stored_token: Result<String, _> = self.redis_ops.get(&redis_key);
        
        match stored_token {
            Ok(stored) if stored == token => {
                // 令牌在Redis中存在且匹配，从数据库获取最新用户信息
                let repo_user = self.user_repo.find_by_id(&claims.sub).await
                    .map_err(|_| anyhow!("用户查询失败"))?
                    .ok_or_else(|| anyhow!("用户不存在"))?;
                
                let user = Self::convert_user(repo_user);
                
                // 检查用户状态
                if user.status != UserStatus::Active {
                    return Err(anyhow!("用户账户已被禁用"));
                }
                
                Ok(user)
            },
            Ok(_) => Err(anyhow!("令牌已失效")),
            Err(_) => Err(anyhow!("令牌已过期或被吊销")),
        }
    }

    /// 验证刷新令牌（包含Redis检查）
    pub async fn verify_refresh_token(&self, refresh_token: &str) -> Result<User> {
        // 首先进行JWT验证
        let claims = JwtService::verify_refresh_token(refresh_token)
            .map_err(|e| anyhow!("刷新令牌验证失败: {}", e))?;

        // 检查Redis中是否存在该刷新令牌
        let redis_key = format!("refresh_token:{}", claims.sub);
        let stored_token: String = self.redis_ops.get(&redis_key)
            .map_err(|_| anyhow!("刷新令牌已过期或被吊销"))?;
        
        if stored_token != refresh_token {
            return Err(anyhow!("刷新令牌已失效"));
        }

        // 获取用户信息
        let repo_user = self.user_repo.find_by_id(&claims.sub).await
            .map_err(|_| anyhow!("用户查询失败"))?
            .ok_or_else(|| anyhow!("用户不存在"))?;
        
        let user = Self::convert_user(repo_user);
        
        // 检查用户状态
        if user.status != UserStatus::Active {
            return Err(anyhow!("用户账户已被禁用"));
        }
        
        Ok(user)
    }

    /// 使用刷新令牌生成新的访问令牌
    pub async fn refresh_access_token(&self, refresh_token: &str) -> Result<String, AppError> {
        // 验证刷新令牌
        let user = self.verify_refresh_token(refresh_token).await
            .map_err(|e| AppError::Unauthorized(e.to_string()))?;

        // 生成新的访问令牌
        let new_access_token = JwtService::generate_access_token(&user)
            .map_err(|e| AppError::Internal(e.to_string()))?;

        // 更新Redis中的访问令牌
        let access_token_key = format!("access_token:{}", user.id);
        self.redis_ops.set_ex(&access_token_key, &new_access_token, constants::ACCESS_TOKEN_EXPIRY as usize)
            .map_err(|e| AppError::Internal(format!("访问令牌存储失败: {}", e)))?;

        Ok(new_access_token)
    }

    /// 用户登出 - 删除所有令牌
    pub async fn logout(&self, user_id: Uuid) -> Result<()> {
        // 删除访问令牌
        let access_token_key = format!("access_token:{}", user_id);
        let _ = self.redis_ops.del(&access_token_key);
        
        // 删除刷新令牌
        let refresh_token_key = format!("refresh_token:{}", user_id);
        let _ = self.redis_ops.del(&refresh_token_key);
        
        info!("用户 {} 已成功登出", user_id);
        Ok(())
    }

    /// 吊销指定的访问令牌
    pub async fn revoke_access_token(&self, user_id: &str) -> Result<()> {
        let access_token_key = format!("access_token:{}", user_id);
        self.redis_ops.del(&access_token_key)
            .map_err(|e| anyhow!("访问令牌吊销失败: {}", e))?;
        
        Ok(())
    }

    /// 吊销指定的刷新令牌
    pub async fn revoke_refresh_token(&self, user_id: &str) -> Result<()> {
        let refresh_token_key = format!("refresh_token:{}", user_id);
        self.redis_ops.del(&refresh_token_key)
            .map_err(|e| anyhow!("刷新令牌吊销失败: {}", e))?;
        
        Ok(())
    }

    /// 获取JWT Claims信息（用于中间件等）
    pub fn decode_token_claims(&self, token: &str) -> Result<Claims> {
        JwtService::decode_token_claims(token)
    }
}


