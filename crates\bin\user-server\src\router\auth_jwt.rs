use axum::{
    body::Body,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use headers::{authorization::Bearer, Authorization};
use tracing::log;
use common::domain::dto::{Claims, AuthError, constants};
use service::user_service::token::JwtService;


/// JWT 认证中间件
pub async fn auth_middleware(
    mut req: Request<Body>,
    next: Next,
) -> Result<Response, AuthError> {
    // 从请求头中提取 Bearer token
    let auth_header = req
        .headers()
        .get("Authorization")
        .and_then(|value| value.to_str().ok())
        .ok_or(AuthError::MissingToken)?;


    let token = JwtService::extract_bearer_token(auth_header)?;

    log::info!("auth_header : {:?}, token: {}", auth_header, &token[..std::cmp::min(token.len(), 20)]);
    
    // 验证JWT并获取Claims
    let claims = JwtService::verify_access_token(token)?;

    // 将 claims 添加到请求扩展中
    req.extensions_mut().insert(claims);

    // 继续处理请求
    Ok(next.run(req).await)
}




