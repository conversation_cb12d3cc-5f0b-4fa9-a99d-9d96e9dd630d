use super::{<PERSON><PERSON><PERSON><PERSON>r, CacheRepository, CacheStats, TtlCalculator};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use common::domain::dto::KlineDto;
use fred::prelude::*;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use fred::{clients::RedisClient, types::RedisConfig};
use crate::connection::redis::RedisOperations;
use redis::{Commands, Connection, ConnectionLike};
use r2d2::{PooledConnection};
use r2d2_redis::RedisConnectionManager;

/// Redis缓存仓库实现
/// 
/// 使用RedisOperations统一管理Redis连接池
pub struct RedisCacheRepository {
    /// Redis操作工具
    redis_ops: RedisOperations,
    /// 键前缀
    key_prefix: String,
}

impl RedisCacheRepository {
    /// 从RedisOperations创建Redis缓存仓库实例
    pub fn new(redis_ops: RedisOperations) -> Self {
        Self {
            redis_ops,
            key_prefix: "kline".to_string(),
        }
    }

    /// 创建带自定义前缀的Redis缓存仓库实例
    pub fn with_prefix(redis_ops: RedisOperations, key_prefix: String) -> Self {
        Self {
            redis_ops,
            key_prefix,
        }
    }

    /// 生成Redis键名（旧格式）
    fn generate_key(&self, symbol: &str, date: &str) -> String {
        format!("{}:{}:{}", self.key_prefix, symbol.to_lowercase(), date)
    }

    /// 生成新格式的Redis键名
    /// 格式: 交易所:symbol:日期
    fn generate_new_key(&self, exchange: &str, symbol: &str, date: &str) -> String {
        format!("{}:{}:{}", exchange.to_lowercase(), symbol.to_uppercase(), date)
    }

    /// 将时间戳转换为日期字符串
    fn timestamp_to_date(&self, timestamp: i64) -> String {
        KlineDto::timestamp_to_date_string(timestamp)
    }

    /// 获取日期范围内的所有日期
    fn get_date_range(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> Vec<String> {
        let mut dates = Vec::new();
        let mut current = start_time.date_naive();
        let end_date = end_time.date_naive();

        while current <= end_date {
            dates.push(current.format("%Y%m%d").to_string());
            current = current.succ_opt().unwrap_or(current);
        }

        dates
    }

    /// 使用RedisOperations执行ZRANGEBYSCORE操作
    fn zrangebyscore(&self, key: &str, min: f64, max: f64) -> Result<Vec<String>, CacheError> {
        self.redis_ops.zrangebyscore(key, min, max)
            .map_err(|e| CacheError::OperationFailed(format!("ZRANGEBYSCORE failed: {}", e)))
    }

    /// 使用RedisOperations执行ZADD操作
    fn zadd(&self, key: &str, member: &str, score: f64) -> Result<i64, CacheError> {
        self.redis_ops.zadd(key, member, score)
            .map_err(|e| CacheError::OperationFailed(format!("ZADD failed: {}", e)))
    }

    /// 使用RedisOperations执行ZRANGE操作
    fn zrange(&self, key: &str, start: isize, stop: isize) -> Result<Vec<String>, CacheError> {
        self.redis_ops.zrange(key, start, stop)
            .map_err(|e| CacheError::OperationFailed(format!("ZRANGE failed: {}", e)))
    }

    /// 使用新格式查询K线数据
    /// 键格式: 交易所:symbol:日期
    pub async fn get_klines_by_range_new_format(
        &self,
        exchange: &str,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<usize>,
    ) -> Result<Vec<KlineDto>, CacheError> {
        debug!(
            "Getting klines (new format) for {} {} {} from {} to {} (limit: {:?})",
            exchange, symbol, interval, start_time, end_time, limit
        );

        let dates = self.get_date_range(start_time, end_time);
        let mut all_klines = Vec::new();

        for date in dates {
            let key = self.generate_new_key(exchange, symbol, &date);
            
            // 使用ZRANGEBYSCORE获取时间范围内的数据
            let values: Vec<String> = self.zrangebyscore(&key, start_time.timestamp() as f64, end_time.timestamp() as f64)?;

            for value in values {
                // 从Redis获取的数据需要包含score（时间戳）
                // 这里我们需要使用ZRANGEBYSCORE WITH SCORES来获取时间戳
                // 暂时使用解析方法，实际应该从score获取时间戳
                match KlineDto::from_cache_json(&value, symbol, interval) {
                    Ok(kline) => {
                        // JSON格式包含时间戳，无需额外处理
                        if kline.time >= start_time && kline.time <= end_time {
                            all_klines.push(kline);
                        }
                    }
                    Err(e) => {
                        warn!("Failed to parse kline data (new format) '{}': {}", value, e);
                    }
                }
            }
        }

        all_klines.sort_by(|a, b| a.time.cmp(&b.time));

        if let Some(limit) = limit {
            all_klines.truncate(limit);
        }

        debug!("Retrieved {} klines from cache (new format)", all_klines.len());
        Ok(all_klines)
    }

    /// 使用新格式添加K线数据
    /// 键格式: 交易所:symbol:日期
    pub async fn add_klines_for_day_new_format(
        &self,
        exchange: &str,
        symbol: &str,
        interval: &str,
        date: &str,
        klines: &[KlineDto],
    ) -> Result<usize, CacheError> {
        if klines.is_empty() {
            return Ok(0);
        }

        debug!("Adding {} klines (new format) for {} {} on {}", klines.len(), exchange, symbol, date);

        let key = self.generate_new_key(exchange, symbol, date);
        let mut added_count = 0;

        for kline in klines {
            let score = kline.get_redis_score();
            let value = kline.to_cache_json().map_err(|e| CacheError::OperationFailed(format!("Failed to serialize kline to JSON: {}", e)))?; // 使用JSON格式
            
            let result: i64 = self.zadd(&key, &value, score)?;
            added_count += result as usize;
        }

        info!("Successfully added {} klines (new format) for {} {} on {}", added_count, exchange, symbol, date);
        Ok(added_count)
    }
}

#[async_trait]
impl CacheRepository for RedisCacheRepository {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    async fn get_klines_by_range(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<usize>,
    ) -> Result<Vec<KlineDto>, CacheError> {
        debug!(
            "Getting klines for {} {} from {} to {} (limit: {:?})",
            symbol, interval, start_time, end_time, limit
        );

        let dates = self.get_date_range(start_time, end_time);
        let mut all_klines = Vec::new();

        for date in dates {
            let key = self.generate_key(symbol, &date);
            
            let values: Vec<String> = self.zrangebyscore(&key, start_time.timestamp() as f64, end_time.timestamp() as f64)?;

            for value in values {
                match KlineDto::from_cache_json(&value, symbol, interval) {
                    Ok(kline) => {
                        if kline.time >= start_time && kline.time <= end_time {
                            all_klines.push(kline);
                        }
                    }
                    Err(e) => {
                        warn!("Failed to parse kline data '{}': {}", value, e);
                    }
                }
            }
        }

        all_klines.sort_by(|a, b| a.time.cmp(&b.time));

        if let Some(limit) = limit {
            all_klines.truncate(limit);
        }

        debug!("Retrieved {} klines from cache", all_klines.len());
        Ok(all_klines)
    }

    async fn add_klines_for_day(
        &self,
        symbol: &str,
        interval: &str,
        date: &str,
        klines: &[KlineDto],
    ) -> Result<usize, CacheError> {
        if klines.is_empty() {
            return Ok(0);
        }

        debug!("Adding {} klines for {} on {}", klines.len(), symbol, date);

        let key = self.generate_key(symbol, date);
        let mut added_count = 0;

        for kline in klines {
            let score = kline.get_redis_score();
            let value = kline.to_cache_json().map_err(|e| CacheError::OperationFailed(format!("Failed to serialize kline to JSON: {}", e)))?;
            
            let result: i64 = self.zadd(&key, &value, score)?;
            added_count += result as usize;
        }

        info!("Successfully added {} klines for {} on {}", added_count, symbol, date);
        Ok(added_count)
    }

    async fn set_expiry_for_day(
        &self,
        symbol: &str,
        date: &str,
        ttl_seconds: u64,
    ) -> Result<(), CacheError> {
        let key = self.generate_key(symbol, date);
        
        debug!("Setting TTL {} seconds for key {}", ttl_seconds, key);

        let _: bool = self.redis_ops.expire(&key, ttl_seconds as usize)
            .map_err(|e| CacheError::OperationFailed(format!("Failed to set TTL: {}", e)))?;

        Ok(())
    }

    async fn exists_for_day(
        &self,
        symbol: &str,
        date: &str,
    ) -> Result<bool, CacheError> {
        let key = self.generate_key(symbol, date);
        
        let exists: bool = self.redis_ops.exists(&key)
            .map_err(|e| CacheError::OperationFailed(format!("EXISTS command failed: {}", e)))?;
        
        Ok(exists)
    }

    async fn get_ttl_for_day(
        &self,
        symbol: &str,
        date: &str,
    ) -> Result<i64, CacheError> {
        let key = self.generate_key(symbol, date);
        
        let ttl: i32 = self.redis_ops.ttl(&key)
            .map_err(|e| CacheError::OperationFailed(format!("TTL command failed: {}", e)))?;
        
        Ok(ttl as i64)
    }

    async fn get_klines_for_dates(
        &self,
        symbol: &str,
        interval: &str,
        dates: &[String],
    ) -> Result<HashMap<String, Vec<KlineDto>>, CacheError> {
        let mut result = HashMap::new();

        for date in dates {
            let key = self.generate_key(symbol, date);
            let values: Vec<String> = self.zrange(&key, 0, -1)?;

            let mut klines = Vec::new();
            for value in values {
                match KlineDto::from_cache_json(&value, symbol, interval) {
                    Ok(kline) => klines.push(kline),
                    Err(e) => warn!("Failed to parse kline data: {}", e),
                }
            }

            klines.sort_by(|a, b| a.time.cmp(&b.time));
            result.insert(date.clone(), klines);
        }

        Ok(result)
    }

    async fn delete_day_cache(&self, symbol: &str, date: &str) -> Result<(), CacheError> {
        let key = self.generate_key(symbol, date);
        
        self.redis_ops.del(&key)
            .map_err(|e| CacheError::OperationFailed(format!("DEL command failed: {}", e)))?;
        
        Ok(())
    }

    async fn get_stats(&self, symbol: &str) -> Result<CacheStats, CacheError> {
        let mut additional_info = HashMap::new();
        additional_info.insert("symbol".to_string(), symbol.to_string());
        additional_info.insert("note".to_string(), "Simplified implementation".to_string());
        
        Ok(CacheStats {
            total_keys: 0,
            memory_usage: 0,
            connections: 1,
            hit_rate: 1.0,
            additional_info,
        })
    }

    async fn get_cache_stats(&self) -> Result<CacheStats, CacheError> {
        let mut additional_info = HashMap::new();
        additional_info.insert("key_prefix".to_string(), self.key_prefix.clone());
        additional_info.insert("note".to_string(), "Simplified implementation".to_string());
        
        Ok(CacheStats {
            total_keys: 0,
            memory_usage: 0,
            connections: 1,
            hit_rate: 1.0,
            additional_info,
        })
    }

    async fn health_check(&self) -> Result<bool, CacheError> {
        let response: String = self.redis_ops.ping()
            .map_err(|e| CacheError::ConnectionError(format!("Health check failed: {}", e)))?;
        
                  Ok(response == "PONG")
      }
}

/// 用于测试的Mock实现
#[cfg(test)]
pub struct MockCacheRepository {
    data: std::sync::Mutex<HashMap<String, Vec<(f64, String)>>>,
    ttls: std::sync::Mutex<HashMap<String, i64>>,
}

#[cfg(test)]
impl MockCacheRepository {
    pub fn new() -> Self {
        Self {
            data: std::sync::Mutex::new(HashMap::new()),
            ttls: std::sync::Mutex::new(HashMap::new()),
        }
    }

    fn generate_key(&self, symbol: &str, date: &str) -> String {
        format!("kline:{}:{}", symbol.to_lowercase(), date)
    }
}

#[cfg(test)]
#[async_trait]
impl CacheRepository for MockCacheRepository {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    async fn get_klines_by_range(
        &self,
        symbol: &str,
        interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<usize>,
    ) -> Result<Vec<KlineDto>, CacheError> {
        let data = self.data.lock().unwrap();
        let mut all_klines = Vec::new();

        for (key, values) in data.iter() {
            if key.contains(&symbol.to_lowercase()) {
                for (score, value) in values {
                    let timestamp = *score as i64;
                    if timestamp >= start_time.timestamp() && timestamp <= end_time.timestamp() {
                        if let Ok(kline) = KlineDto::from_cache_json(value, symbol, interval) {
                            all_klines.push(kline);
                        }
                    }
                }
            }
        }

        all_klines.sort_by(|a, b| a.time.cmp(&b.time));
        
        if let Some(limit) = limit {
            all_klines.truncate(limit);
        }

        Ok(all_klines)
    }

    async fn add_klines_for_day(
        &self,
        symbol: &str,
        _interval: &str,
        date: &str,
        klines: &[KlineDto],
    ) -> Result<usize, CacheError> {
        let key = self.generate_key(symbol, date);
        let mut data = self.data.lock().unwrap();
        
        let entry = data.entry(key).or_insert_with(Vec::new);
        
        for kline in klines {
            let score = kline.get_redis_score();
            let value = kline.to_cache_json().map_err(|e| CacheError::OperationFailed(format!("Failed to serialize kline to JSON: {}", e)))?;
            entry.push((score, value));
        }

        Ok(klines.len())
    }

    async fn set_expiry_for_day(
        &self,
        symbol: &str,
        date: &str,
        ttl_seconds: u64,
    ) -> Result<(), CacheError> {
        let key = self.generate_key(symbol, date);
        let mut ttls = self.ttls.lock().unwrap();
        ttls.insert(key, ttl_seconds as i64);
        Ok(())
    }

    async fn exists_for_day(&self, symbol: &str, date: &str) -> Result<bool, CacheError> {
        let key = self.generate_key(symbol, date);
        let data = self.data.lock().unwrap();
        Ok(data.contains_key(&key))
    }

    async fn get_ttl_for_day(&self, symbol: &str, date: &str) -> Result<i64, CacheError> {
        let key = self.generate_key(symbol, date);
        let ttls = self.ttls.lock().unwrap();
        Ok(ttls.get(&key).copied().unwrap_or(-2))
    }

    async fn get_klines_for_dates(
        &self,
        symbol: &str,
        interval: &str,
        dates: &[String],
    ) -> Result<HashMap<String, Vec<KlineDto>>, CacheError> {
        let mut result = HashMap::new();
        
        for date in dates {
            let key = self.generate_key(symbol, date);
            let data = self.data.lock().unwrap();
            
            if let Some(values) = data.get(&key) {
                let mut klines = Vec::new();
                for (_, value) in values {
                    if let Ok(kline) = KlineDto::from_cache_json(value, symbol, interval) {
                        klines.push(kline);
                    }
                }
                klines.sort_by(|a, b| a.time.cmp(&b.time));
                result.insert(date.clone(), klines);
            }
        }

        Ok(result)
    }

    async fn delete_day_cache(&self, symbol: &str, date: &str) -> Result<(), CacheError> {
        let key = self.generate_key(symbol, date);
        let mut data = self.data.lock().unwrap();
        data.remove(&key);
        Ok(())
    }

    async fn get_stats(&self, symbol: &str) -> Result<CacheStats, CacheError> {
        let data = self.data.lock().unwrap();
        let symbol_keys: Vec<_> = data.keys()
            .filter(|key| key.contains(&symbol.to_lowercase()))
            .collect();
        
        let mut additional_info = HashMap::new();
        additional_info.insert("symbol".to_string(), symbol.to_string());
        
        Ok(CacheStats {
            total_keys: symbol_keys.len() as u64,
            memory_usage: 0,
            connections: 1,
            hit_rate: 1.0,
            additional_info,
        })
    }

    async fn get_cache_stats(&self) -> Result<CacheStats, CacheError> {
        let data = self.data.lock().unwrap();
        Ok(CacheStats {
            total_keys: data.len() as u64,
            memory_usage: 0,
            connections: 1,
            hit_rate: 1.0,
            additional_info: HashMap::new(),
        })
    }

    async fn health_check(&self) -> Result<bool, CacheError> {
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::TimeZone;
    use rust_decimal::Decimal;
    use std::str::FromStr;

    #[tokio::test]
    async fn test_mock_cache_repository() {
        let cache = MockCacheRepository::new();
        
        let kline = KlineDto {
            time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            open_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 30, 0).unwrap(),
            close_time: Utc.with_ymd_and_hms(2024, 6, 15, 10, 31, 0).unwrap(),
            open_price: Decimal::from_str("50000.00").unwrap(),
            high_price: Decimal::from_str("50100.00").unwrap(),
            low_price: Decimal::from_str("49900.00").unwrap(),
            close_price: Decimal::from_str("50050.00").unwrap(),
            volume: Decimal::from_str("1.5").unwrap(),
            quote_asset_volume: Decimal::ZERO,
            number_of_trades: 0,
            taker_buy_base_asset_volume: Decimal::ZERO,
            taker_buy_quote_asset_volume: Decimal::ZERO,
        };

        let result = cache.add_klines_for_day("BTCUSDT", "1m", "20240615", &[kline.clone()]).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 1);

        let exists = cache.exists_for_day("BTCUSDT", "20240615").await.unwrap();
        assert!(exists);

        let ttl_result = cache.set_expiry_for_day("BTCUSDT", "20240615", 3600).await;
        assert!(ttl_result.is_ok());

        let ttl = cache.get_ttl_for_day("BTCUSDT", "20240615").await.unwrap();
        assert_eq!(ttl, 3600);

        let start_time = Utc.with_ymd_and_hms(2024, 6, 15, 10, 0, 0).unwrap();
        let end_time = Utc.with_ymd_and_hms(2024, 6, 15, 11, 0, 0).unwrap();
        
        let klines = cache.get_klines_by_range("BTCUSDT", "1m", start_time, end_time, None).await.unwrap();
        assert_eq!(klines.len(), 1);
        assert_eq!(klines[0].symbol, "BTCUSDT");

        let health = cache.health_check().await.unwrap();
        assert!(health);
    }
} 