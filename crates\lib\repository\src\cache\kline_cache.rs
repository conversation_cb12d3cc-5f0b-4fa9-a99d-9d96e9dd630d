// =====================================================
// Redis K线数据缓存层实现
// 核心设计：ZSET存储，按日期分Key，TTL递减策略
// =====================================================

use chrono::{DateTime, Utc, Datelike, Duration};
use redis::{Commands, Connection};
use serde::{Serialize, Deserialize};
use anyhow::Result;
use std::collections::HashMap;
use rust_decimal::Decimal;

/// K线缓存数据结构 - 紧凑型JSON格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KlineCacheData {
    /// 时间戳 (Unix timestamp in milliseconds)
    #[serde(rename = "t")]
    pub timestamp: i64,
    /// 开盘价
    #[serde(rename = "o")]
    pub open: String,
    /// 最高价
    #[serde(rename = "h")]
    pub high: String,
    /// 最低价
    #[serde(rename = "l")]
    pub low: String,
    /// 收盘价
    #[serde(rename = "c")]
    pub close: String,
    /// 成交量
    #[serde(rename = "v")]
    pub volume: String,
    /// 报价成交量
    #[serde(rename = "q")]
    pub quote_volume: String,
    /// 成交笔数
    #[serde(rename = "n")]
    pub trades: i32,
}

/// Redis K线缓存管理器
pub struct KlineCache {
    redis_client: redis::Client,
    exchange_name: String,
}

impl KlineCache {
    /// 创建新的K线缓存管理器
    pub fn new(redis_url: &str, exchange_name: String) -> Result<Self> {
        let redis_client = redis::Client::open(redis_url)?;
        Ok(Self {
            redis_client,
            exchange_name,
        })
    }

    /// 生成Redis Key
    /// 格式: [交易所名称]:[交易对]:[日期YYYYMMDD]
    /// 例如: binance:BTCUSDT:20250617
    fn generate_key(&self, symbol: &str, date: DateTime<Utc>) -> String {
        format!("{}:{}:{}", 
            self.exchange_name, 
            symbol, 
            date.format("%Y%m%d")
        )
    }

    /// 计算TTL - 该Key在其日期之后的第365天过期，按天递减
    fn calculate_ttl(&self, date: DateTime<Utc>) -> i64 {
        let now = Utc::now();
        let expiry_date = date + Duration::days(365);
        
        if expiry_date <= now {
            // 已过期，设置很短的TTL
            60 // 1分钟
        } else {
            // 计算到过期时间的秒数
            (expiry_date - now).num_seconds()
        }
    }

    /// 写入K线数据到Redis (双写操作的Redis部分)
    pub async fn write_kline(
        &self,
        symbol: &str,
        kline_data: &KlineCacheData,
    ) -> Result<()> {
        let mut conn = self.redis_client.get_connection()?;
        
        // 从时间戳获取日期
        let datetime = DateTime::from_timestamp_millis(kline_data.timestamp)
            .ok_or_else(|| anyhow::anyhow!("Invalid timestamp: {}", kline_data.timestamp))?;
        
        let key = self.generate_key(symbol, datetime);
        let score = kline_data.timestamp as f64;
        let member = serde_json::to_string(kline_data)?;
        
        // 执行ZADD命令
        let _: () = conn.zadd(&key, member, score)?;
        
        // 设置TTL
        let ttl = self.calculate_ttl(datetime);
        let _: () = conn.expire(&key, ttl)?;
        
        Ok(())
    }

    /// 批量写入K线数据
    pub async fn write_klines_batch(
        &self,
        symbol: &str,
        klines: &[KlineCacheData],
    ) -> Result<()> {
        if klines.is_empty() {
            return Ok(());
        }

        let mut conn = self.redis_client.get_connection()?;
        
        // 按日期分组
        let mut daily_groups: HashMap<String, Vec<&KlineCacheData>> = HashMap::new();
        
        for kline in klines {
            let datetime = DateTime::from_timestamp_millis(kline.timestamp)
                .ok_or_else(|| anyhow::anyhow!("Invalid timestamp: {}", kline.timestamp))?;
            let key = self.generate_key(symbol, datetime);
            daily_groups.entry(key).or_insert_with(Vec::new).push(kline);
        }

        // 批量写入每个日期的数据
        for (key, day_klines) in daily_groups {
            // 准备ZADD的参数
            let mut zadd_args = Vec::new();
            for kline in &day_klines {
                let score = kline.timestamp as f64;
                let member = serde_json::to_string(kline)?;
                zadd_args.push((member, score));
            }

            // 批量ZADD
            for (member, score) in zadd_args {
                let _: () = conn.zadd(&key, member, score)?;
            }

            // 设置TTL (使用第一个K线的时间计算)
            if let Some(first_kline) = day_klines.first() {
                let datetime = DateTime::from_timestamp_millis(first_kline.timestamp)
                    .ok_or_else(|| anyhow::anyhow!("Invalid timestamp"))?;
                let ttl = self.calculate_ttl(datetime);
                let _: () = conn.expire(&key, ttl)?;
            }
        }

        Ok(())
    }

    /// 读取指定时间范围的K线数据 (Cache-Aside模式)
    pub async fn read_klines(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<KlineCacheData>> {
        let mut conn = self.redis_client.get_connection()?;
        let mut all_klines = Vec::new();

        // 计算需要查询的日期范围
        let mut current_date = start_time.date_naive();
        let end_date = end_time.date_naive();

        while current_date <= end_date {
            let date_time = current_date.and_hms_opt(0, 0, 0)
                .unwrap()
                .and_utc();
            let key = self.generate_key(symbol, date_time);

            // 查询该日期的数据
            let start_score = start_time.timestamp_millis() as f64;
            let end_score = end_time.timestamp_millis() as f64;

            let members: Vec<String> = conn.zrangebyscore(&key, start_score, end_score)?;

            // 解析JSON数据
            for member in members {
                match serde_json::from_str::<KlineCacheData>(&member) {
                    Ok(kline) => all_klines.push(kline),
                    Err(e) => {
                        tracing::warn!("Failed to parse cached kline data: {}", e);
                    }
                }
            }

            current_date = current_date.succ_opt()
                .ok_or_else(|| anyhow::anyhow!("Date overflow"))?;
        }

        // 按时间戳排序
        all_klines.sort_by_key(|k| k.timestamp);

        Ok(all_klines)
    }

    /// 检查缓存中是否存在指定时间范围的数据
    pub async fn check_cache_coverage(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<CacheCoverage> {
        let mut conn = self.redis_client.get_connection()?;
        
        let expected_minutes = (end_time - start_time).num_minutes();
        let mut cached_count = 0i64;
        let mut missing_ranges = Vec::new();

        // 按日期检查缓存覆盖率
        let mut current_date = start_time.date_naive();
        let end_date = end_time.date_naive();

        while current_date <= end_date {
            let date_time = current_date.and_hms_opt(0, 0, 0)
                .unwrap()
                .and_utc();
            let key = self.generate_key(symbol, date_time);

            let start_score = if current_date == start_time.date_naive() {
                start_time.timestamp_millis() as f64
            } else {
                date_time.timestamp_millis() as f64
            };

            let end_score = if current_date == end_date {
                end_time.timestamp_millis() as f64
            } else {
                (date_time + Duration::days(1)).timestamp_millis() as f64
            };

            let count: i64 = conn.zcount(&key, start_score, end_score)?;
            cached_count += count;

            current_date = current_date.succ_opt()
                .ok_or_else(|| anyhow::anyhow!("Date overflow"))?;
        }

        let coverage_percentage = if expected_minutes > 0 {
            (cached_count as f64 / expected_minutes as f64) * 100.0
        } else {
            100.0
        };

        Ok(CacheCoverage {
            expected_count: expected_minutes,
            cached_count,
            coverage_percentage,
            missing_ranges,
        })
    }

    /// 删除过期的缓存数据
    pub async fn cleanup_expired_cache(&self) -> Result<i64> {
        let mut conn = self.redis_client.get_connection()?;
        
        // 获取所有匹配的key
        let pattern = format!("{}:*", self.exchange_name);
        let keys: Vec<String> = conn.keys(&pattern)?;
        
        let mut deleted_count = 0i64;
        let now = Utc::now();

        for key in keys {
            // 解析key中的日期
            if let Some(date_str) = key.split(':').nth(2) {
                if let Ok(date) = chrono::NaiveDate::parse_from_str(date_str, "%Y%m%d") {
                    let date_time = date.and_hms_opt(0, 0, 0).unwrap().and_utc();
                    let expiry_date = date_time + Duration::days(365);
                    
                    if expiry_date <= now {
                        let _: () = conn.del(&key)?;
                        deleted_count += 1;
                    }
                }
            }
        }

        Ok(deleted_count)
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self, symbol: &str) -> Result<CacheStats> {
        let mut conn = self.redis_client.get_connection()?;
        
        let pattern = format!("{}:{}:*", self.exchange_name, symbol);
        let keys: Vec<String> = conn.keys(&pattern)?;
        
        let mut total_entries = 0i64;
        let mut total_memory = 0i64;
        let mut oldest_date: Option<DateTime<Utc>> = None;
        let mut newest_date: Option<DateTime<Utc>> = None;

        for key in &keys {
            let count: i64 = conn.zcard(&key)?;
            total_entries += count;

            // 估算内存使用 (粗略估算)
            // Redis Connection 没有 memory_usage 方法，使用简单估算
            let estimated_memory = count * 100; // 每条记录估算100字节
            total_memory += estimated_memory;

            // 解析日期
            if let Some(date_str) = key.split(':').nth(2) {
                if let Ok(date) = chrono::NaiveDate::parse_from_str(date_str, "%Y%m%d") {
                    let date_time = date.and_hms_opt(0, 0, 0).unwrap().and_utc();
                    
                    if oldest_date.is_none() || Some(date_time) < oldest_date {
                        oldest_date = Some(date_time);
                    }
                    if newest_date.is_none() || Some(date_time) > newest_date {
                        newest_date = Some(date_time);
                    }
                }
            }
        }

        Ok(CacheStats {
            symbol: symbol.to_string(),
            total_keys: keys.len() as i64,
            total_entries,
            estimated_memory_bytes: total_memory,
            oldest_date,
            newest_date,
        })
    }
}

/// 缓存覆盖率信息
#[derive(Debug, Clone)]
pub struct CacheCoverage {
    pub expected_count: i64,
    pub cached_count: i64,
    pub coverage_percentage: f64,
    pub missing_ranges: Vec<(DateTime<Utc>, DateTime<Utc>)>,
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub symbol: String,
    pub total_keys: i64,
    pub total_entries: i64,
    pub estimated_memory_bytes: i64,
    pub oldest_date: Option<DateTime<Utc>>,
    pub newest_date: Option<DateTime<Utc>>,
}
