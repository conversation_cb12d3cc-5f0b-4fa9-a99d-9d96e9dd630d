use sqlx::{PgPool, Row};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 24小时统计数据实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct Stats24hrEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub price_change: Decimal,
    pub price_change_percent: Decimal,
    pub weighted_avg_price: Decimal,
    pub prev_close_price: Decimal,
    pub last_price: Decimal,
    pub last_qty: Decimal,
    pub bid_price: Decimal,
    pub ask_price: Decimal,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub volume: Decimal,
    pub quote_volume: Decimal,
    pub open_time: DateTime<Utc>,
    pub close_time: DateTime<Utc>,
    pub first_id: i64,
    pub last_id: i64,
    pub count: i64,
}

/// 24小时统计数据插入请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateStats24hrRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub price_change: Decimal,
    pub price_change_percent: Decimal,
    pub weighted_avg_price: Decimal,
    pub prev_close_price: Decimal,
    pub last_price: Decimal,
    pub last_qty: Decimal,
    pub bid_price: Decimal,
    pub ask_price: Decimal,
    pub open_price: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
    pub volume: Decimal,
    pub quote_volume: Decimal,
    pub open_time: DateTime<Utc>,
    pub close_time: DateTime<Utc>,
    pub first_id: i64,
    pub last_id: i64,
    pub count: i64,
}

/// 24小时统计查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Stats24hrQuery {
    pub symbol: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub limit: Option<u32>,
}

/// 简化的24小时统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimpleStats24hr {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub price_change_percent: Decimal,
    pub last_price: Decimal,
    pub volume: Decimal,
    pub high_price: Decimal,
    pub low_price: Decimal,
}

/// 市场概览统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct MarketOverview {
    pub total_symbols: i64,
    pub avg_price_change_percent: Option<Decimal>,
    pub gainers_count: i64,
    pub losers_count: i64,
    pub total_volume: Option<Decimal>,
    pub total_quote_volume: Option<Decimal>,
}

/// 24小时统计Repository
#[derive(Clone)]
pub struct Stats24hrRepository {
    pool: PgPool,
}

impl Stats24hrRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化24小时统计表结构
    pub async fn initialize_table(&self) -> Result<()> {
        // 创建24小时统计表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS stats_24hr (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                price_change DECIMAL NOT NULL,
                price_change_percent DECIMAL NOT NULL,
                weighted_avg_price DECIMAL NOT NULL,
                prev_close_price DECIMAL NOT NULL,
                last_price DECIMAL NOT NULL,
                last_qty DECIMAL NOT NULL,
                bid_price DECIMAL NOT NULL,
                ask_price DECIMAL NOT NULL,
                open_price DECIMAL NOT NULL,
                high_price DECIMAL NOT NULL,
                low_price DECIMAL NOT NULL,
                volume DECIMAL NOT NULL,
                quote_volume DECIMAL NOT NULL,
                open_time TIMESTAMPTZ NOT NULL,
                close_time TIMESTAMPTZ NOT NULL,
                first_id BIGINT NOT NULL,
                last_id BIGINT NOT NULL,
                count BIGINT NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将统计表转换为超级表
        log::info!("正在创建stats_24hr超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("stats_24hr")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建stats_24hr超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("stats_24hr超级表创建成功");

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_stats_24hr_symbol_time
             ON stats_24hr (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_stats_24hr_price_change_percent
             ON stats_24hr (price_change_percent DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("24小时统计表结构初始化完成");
        Ok(())
    }

    /// 插入单条24小时统计数据
    pub async fn insert(&self, request: CreateStats24hrRequest) -> Result<Stats24hrEntity> {
        let stats = sqlx::query_as::<_, Stats24hrEntity>(
            r#"
            INSERT INTO stats_24hr (time, symbol, price_change, price_change_percent, weighted_avg_price,
                                   prev_close_price, last_price, last_qty, bid_price, ask_price,
                                   open_price, high_price, low_price, volume, quote_volume,
                                   open_time, close_time, first_id, last_id, count)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
            RETURNING *
            "#,
        )
        .bind(&request.timestamp)
        .bind(&request.symbol)
        .bind(&request.price_change)
        .bind(&request.price_change_percent)
        .bind(&request.weighted_avg_price)
        .bind(&request.prev_close_price)
        .bind(&request.last_price)
        .bind(&request.last_qty)
        .bind(&request.bid_price)
        .bind(&request.ask_price)
        .bind(&request.open_price)
        .bind(&request.high_price)
        .bind(&request.low_price)
        .bind(&request.volume)
        .bind(&request.quote_volume)
        .bind(&request.open_time)
        .bind(&request.close_time)
        .bind(&request.first_id)
        .bind(&request.last_id)
        .bind(&request.count)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 批量插入24小时统计数据
    pub async fn batch_insert(&self, requests: Vec<CreateStats24hrRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO stats_24hr (time, symbol, price_change, price_change_percent, weighted_avg_price, prev_close_price, last_price, last_qty, bid_price, ask_price, open_price, high_price, low_price, volume, quote_volume, open_time, close_time, first_id, last_id, count) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.timestamp)
                .push_bind(&request.symbol)
                .push_bind(&request.price_change)
                .push_bind(&request.price_change_percent)
                .push_bind(&request.weighted_avg_price)
                .push_bind(&request.prev_close_price)
                .push_bind(&request.last_price)
                .push_bind(&request.last_qty)
                .push_bind(&request.bid_price)
                .push_bind(&request.ask_price)
                .push_bind(&request.open_price)
                .push_bind(&request.high_price)
                .push_bind(&request.low_price)
                .push_bind(&request.volume)
                .push_bind(&request.quote_volume)
                .push_bind(&request.open_time)
                .push_bind(&request.close_time)
                .push_bind(&request.first_id)
                .push_bind(&request.last_id)
                .push_bind(&request.count);
        });

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询24小时统计数据
    pub async fn query(&self, params: Stats24hrQuery) -> Result<Vec<Stats24hrEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT * FROM stats_24hr WHERE 1=1"
        );

        if let Some(symbol) = &params.symbol {
            query.push(" AND symbol = ").push_bind(symbol);
        }

        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        query.push(" ORDER BY time DESC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let stats: Vec<Stats24hrEntity> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(stats)
    }

    /// 获取最新的24小时统计
    pub async fn get_latest(&self, symbol: &str) -> Result<Option<Stats24hrEntity>> {
        let stats = sqlx::query_as::<_, Stats24hrEntity>(
            "SELECT * FROM stats_24hr WHERE symbol = $1 ORDER BY time DESC LIMIT 1"
        )
        .bind(symbol)
        .fetch_optional(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取所有交易对的最新24小时统计
    pub async fn get_all_latest(&self) -> Result<Vec<Stats24hrEntity>> {
        let stats = sqlx::query_as::<_, Stats24hrEntity>(
            r#"
            SELECT DISTINCT ON (symbol) * 
            FROM stats_24hr 
            ORDER BY symbol, time DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取简化的24小时统计
    pub async fn get_simple_stats(&self, symbols: Option<Vec<String>>) -> Result<Vec<SimpleStats24hr>> {
        let mut query = sqlx::QueryBuilder::new(
            r#"
            SELECT DISTINCT ON (symbol) 
                symbol, time as timestamp, price_change_percent, 
                last_price, volume, high_price, low_price
            FROM stats_24hr 
            "#
        );

        if let Some(symbols) = &symbols {
            if !symbols.is_empty() {
                query.push(" WHERE symbol = ANY(").push_bind(symbols).push(")");
            }
        }

        query.push(" ORDER BY symbol, time DESC");

        let rows = query.build().fetch_all(&self.pool).await?;

        let mut simple_stats = Vec::new();
        for row in rows {
            simple_stats.push(SimpleStats24hr {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                price_change_percent: row.get("price_change_percent"),
                last_price: row.get("last_price"),
                volume: row.get("volume"),
                high_price: row.get("high_price"),
                low_price: row.get("low_price"),
            });
        }

        Ok(simple_stats)
    }

    /// 获取涨跌榜
    pub async fn get_price_change_ranking(
        &self,
        limit: u32,
        order: &str, // "ASC" 跌幅榜, "DESC" 涨幅榜
    ) -> Result<Vec<SimpleStats24hr>> {
        let order_clause = if order.to_uppercase() == "ASC" { "ASC" } else { "DESC" };
        
        let query = format!(
            r#"
            SELECT DISTINCT ON (symbol) 
                symbol, time as timestamp, price_change_percent, 
                last_price, volume, high_price, low_price
            FROM stats_24hr 
            ORDER BY symbol, time DESC
            "#
        );

        let mut all_stats = sqlx::query(&query).fetch_all(&self.pool).await?;

        // 按涨跌幅排序
        all_stats.sort_by(|a, b| {
            let change_a: Decimal = a.get("price_change_percent");
            let change_b: Decimal = b.get("price_change_percent");
            
            if order_clause == "DESC" {
                change_b.cmp(&change_a)
            } else {
                change_a.cmp(&change_b)
            }
        });

        let mut ranking = Vec::new();
        for row in all_stats.into_iter().take(limit as usize) {
            ranking.push(SimpleStats24hr {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                price_change_percent: row.get("price_change_percent"),
                last_price: row.get("last_price"),
                volume: row.get("volume"),
                high_price: row.get("high_price"),
                low_price: row.get("low_price"),
            });
        }

        Ok(ranking)
    }

    /// 获取市场概览
    pub async fn get_market_overview(&self) -> Result<MarketOverview> {
        let overview = sqlx::query_as::<_, MarketOverview>(
            r#"
            WITH latest_stats AS (
                SELECT DISTINCT ON (symbol) *
                FROM stats_24hr 
                ORDER BY symbol, time DESC
            )
            SELECT 
                COUNT(*) as total_symbols,
                AVG(price_change_percent) as avg_price_change_percent,
                COUNT(CASE WHEN price_change_percent > 0 THEN 1 END) as gainers_count,
                COUNT(CASE WHEN price_change_percent < 0 THEN 1 END) as losers_count,
                SUM(volume) as total_volume,
                SUM(quote_volume) as total_quote_volume
            FROM latest_stats
            "#,
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(overview)
    }

    /// 获取成交量排行榜
    pub async fn get_volume_ranking(&self, limit: u32) -> Result<Vec<SimpleStats24hr>> {
        let stats = sqlx::query(
            r#"
            SELECT DISTINCT ON (symbol) 
                symbol, time as timestamp, price_change_percent, 
                last_price, volume, high_price, low_price
            FROM stats_24hr 
            ORDER BY symbol, time DESC, volume DESC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;

        let mut ranking = Vec::new();
        for row in stats {
            ranking.push(SimpleStats24hr {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                price_change_percent: row.get("price_change_percent"),
                last_price: row.get("last_price"),
                volume: row.get("volume"),
                high_price: row.get("high_price"),
                low_price: row.get("low_price"),
            });
        }

        // 按成交量排序
        ranking.sort_by(|a, b| b.volume.cmp(&a.volume));
        ranking.truncate(limit as usize);

        Ok(ranking)
    }

    /// 删除旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM stats_24hr WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 获取所有活跃交易对
    pub async fn get_active_symbols(&self, within_duration: chrono::Duration) -> Result<Vec<String>> {
        let cutoff_time = Utc::now() - within_duration;

        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT symbol FROM stats_24hr WHERE time >= $1 ORDER BY symbol"
        )
        .bind(cutoff_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(symbols)
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&self.pool).await?;
        Ok(())
    }
} 