# 构建所有项目
build:
	cargo build

# 构建发布版本
re:
	cargo build --release --timings

# 运行测试
test:
	cargo test

# 清理构建文件
clean:
	cargo clean

# 运行gRPC服务器
run-server:
	cargo run --bin data-grpc

# Docker构建
docker-build:
	docker-compose build

# 启动Docker服务
docker-up:
	docker-compose up -d

# 停止Docker服务
docker-down:
	docker-compose down

# 查看Docker日志
docker-logs:
	docker-compose logs -f
tests:
	cargo test --all -- --nocapture

linux:
	cargo build --release --target x86_64-unknown-linux-musl

up:
	docker-compose up -d
prune:
	docker system prune -a #docker清理命令
force:
	docker-compose up --force-recreate --build -d

