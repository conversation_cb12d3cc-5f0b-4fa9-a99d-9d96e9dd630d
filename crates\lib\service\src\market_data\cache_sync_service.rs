use anyhow::Result;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::sync::Arc;
use std::collections::HashMap;
use tokio::time::{sleep, Duration, Instant};
use tracing::{info, warn, debug, error};

use repository::{
    timescale::{KlineRepository, Symbol, MarketDataQuery},
    CacheRepository, CacheError, TtlCalculator,
};

use common::domain::entity::KlineEntity;
use common::domain::dto::KlineDto;
use super::shared_state::ServiceStateManager;

/// 缓存同步服务
/// 
/// 专门负责将最新的1分钟级别数据同步到Redis缓存
/// 确保缓存数据的实时性，支持回测需求
pub struct CacheSyncService {
    /// K线数据仓库
    kline_repo: KlineRepository,
    /// 缓存仓库
    cache_repo: Arc<dyn CacheRepository>,
    /// 状态管理器
    state_manager: ServiceStateManager,
    /// 同步间隔（秒）
    sync_interval_seconds: u64,
    /// 是否运行中
    is_running: Arc<std::sync::atomic::AtomicBool>,
}

impl CacheSyncService {
    /// 创建新的缓存同步服务
    pub fn new(
        kline_repo: KlineRepository,
        cache_repo: Arc<dyn CacheRepository>,
        state_manager: ServiceStateManager,
        sync_interval_seconds: Option<u64>,
    ) -> Self {
        Self {
            kline_repo,
            cache_repo,
            state_manager,
            sync_interval_seconds: sync_interval_seconds.unwrap_or(60), // 默认60秒
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
        }
    }

    /// 启动缓存同步服务
    pub async fn start(&self, symbols: Vec<Symbol>) -> Result<()> {
        info!("🚀 启动缓存同步服务，同步间隔: {}秒", self.sync_interval_seconds);
        
        self.is_running.store(true, std::sync::atomic::Ordering::Relaxed);
        self.state_manager.set_cache_sync_running(true).await;

        let mut sync_count = 0;
        
        while self.is_running.load(std::sync::atomic::Ordering::Relaxed) {
            let sync_start = Instant::now();
            sync_count += 1;
            
            debug!("开始第 {} 次缓存同步", sync_count);
            
            match self.sync_latest_data(&symbols).await {
                Ok((synced_records, synced_symbols)) => {
                    let duration_ms = sync_start.elapsed().as_millis() as u64;
                    
                    // 更新状态
                    self.state_manager.update_sync_stats(
                        synced_records,
                        synced_symbols,
                        duration_ms
                    ).await;
                    
                    info!(
                        "✅ 缓存同步完成 #{}: 同步了{}个符号的{}条记录，耗时: {}ms",
                        sync_count, synced_symbols, synced_records, duration_ms
                    );
                }
                Err(e) => {
                    error!("❌ 缓存同步失败 #{}: {}", sync_count, e);
                    self.state_manager.set_cache_sync_error(Some(e.to_string())).await;
                }
            }

            // 等待下一次同步
            sleep(Duration::from_secs(self.sync_interval_seconds)).await;
        }

        self.state_manager.set_cache_sync_running(false).await;
        info!("缓存同步服务已停止");
        Ok(())
    }

    /// 停止缓存同步服务
    pub fn stop(&self) {
        info!("正在停止缓存同步服务...");
        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);
    }

    /// 同步最新数据到缓存
    async fn sync_latest_data(&self, symbols: &[Symbol]) -> Result<(u64, u32)> {
        let mut total_synced_records = 0u64;
        let mut synced_symbols = 0u32;
        
        // 计算同步时间范围（最近5分钟的数据）
        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::minutes(5);
        
        debug!("同步时间范围: {} 到 {}", start_time.format("%H:%M:%S"), end_time.format("%H:%M:%S"));

        for symbol in symbols {
            match self.sync_symbol_latest_data(&symbol.symbol, start_time, end_time).await {
                Ok(records_synced) => {
                    if records_synced > 0 {
                        total_synced_records += records_synced;
                        synced_symbols += 1;
                        debug!("✅ {} 同步了 {} 条记录", symbol.symbol, records_synced);
                    }
                }
                Err(e) => {
                    warn!("⚠️  同步 {} 失败: {}", symbol.symbol, e);
                    // 继续处理其他符号，不中断整个同步过程
                }
            }
        }

        Ok((total_synced_records, synced_symbols))
    }

    /// 同步单个符号的最新数据
    async fn sync_symbol_latest_data(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<u64> {
        // 只同步1分钟级别的数据
        let interval = "1m";
        
        // 查询最新的1分钟数据
        let market_query = MarketDataQuery {
            symbol: symbol.to_string(),
            start_time,
            end_time,
            interval_type: interval.to_string(),
            limit: None, // 获取所有数据
        };

        let market_data = self.kline_repo.query(market_query).await?;
        
        if market_data.is_empty() {
            return Ok(0);
        }

        // 转换为KlineEntity（MarketData只有基本OHLCV字段）
        let klines: Vec<KlineEntity> = market_data.into_iter().map(|data| KlineEntity {
            time: data.timestamp,
            symbol: data.symbol,
            interval: data.interval_type,
            open_price: data.open,
            high_price: data.high,
            low_price: data.low,
            close_price: data.close,
            volume: data.volume,
            // 这些字段在基本MarketData中不存在，使用默认值
            quote_asset_volume: Decimal::ZERO,
            number_of_trades: 0,
            taker_buy_base_asset_volume: Decimal::ZERO,
            taker_buy_quote_asset_volume: Decimal::ZERO,
            close_time: data.timestamp, // 使用相同的时间戳
        }).collect();

        // 同步到缓存
        let synced_count = self.sync_klines_to_cache(symbol, interval, &klines).await?;
        
        Ok(synced_count as u64)
    }

    /// 将K线数据同步到缓存
    async fn sync_klines_to_cache(
        &self,
        symbol: &str,
        interval: &str,
        klines: &[KlineEntity],
    ) -> Result<usize, CacheError> {
        if klines.is_empty() {
            return Ok(0);
        }

        // 按日期分组K线数据
        let klines_by_date = self.group_klines_by_date(klines);
        let mut total_synced = 0;

        // 为每个日期同步缓存
        for (date, date_klines) in klines_by_date {
            // 添加K线数据到缓存（使用ZADD，会自动覆盖相同时间的数据）
            let synced = self.cache_repo
                .add_klines_for_day(symbol, interval, &date, &date_klines)
                .await?;
            
            total_synced += synced;

            // 设置TTL
            let current_date = TtlCalculator::get_current_date_string();
            let ttl = if date == current_date {
                // 当天的数据使用固定TTL
                TtlCalculator::get_fixed_ttl()
            } else {
                // 历史数据使用动态TTL
                TtlCalculator::calculate_dynamic_ttl(&date, &current_date)?
            };
            
            self.cache_repo.set_expiry_for_day(symbol, &date, ttl).await?;
        }

        debug!("同步 {} {} 的 {} 条记录到缓存", symbol, interval, total_synced);
        Ok(total_synced)
    }

    /// 将KlineEntity按日期分组转换为KlineDto
    fn group_klines_by_date(&self, klines: &[KlineEntity]) -> HashMap<String, Vec<KlineDto>> {
        let mut klines_by_date: HashMap<String, Vec<KlineDto>> = HashMap::new();
        
        for kline in klines {
            let date = KlineDto::timestamp_to_date_string(kline.time.timestamp());
            let dto = KlineDto {
                time: kline.time,
                symbol: kline.symbol.clone(),
                interval: kline.interval.clone(),
                open_time: kline.time,
                close_time: kline.close_time,
                open_price: kline.open_price,
                high_price: kline.high_price,
                low_price: kline.low_price,
                close_price: kline.close_price,
                volume: kline.volume,
                quote_asset_volume: kline.quote_asset_volume,
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            };
            
            klines_by_date.entry(date).or_insert_with(Vec::new).push(dto);
        }

        klines_by_date
    }

    /// 手动触发同步指定符号的数据
    pub async fn manual_sync_symbol(
        &self,
        symbol: &str,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> Result<u64> {
        let start_time = start_time.unwrap_or_else(|| Utc::now() - chrono::Duration::hours(1));
        let end_time = end_time.unwrap_or_else(|| Utc::now());
        
        info!("手动同步 {} 的缓存数据，时间范围: {} 到 {}", 
              symbol, 
              start_time.format("%Y-%m-%d %H:%M:%S"),
              end_time.format("%Y-%m-%d %H:%M:%S"));
        
        self.sync_symbol_latest_data(symbol, start_time, end_time).await
    }

    /// 预热指定符号的缓存
    pub async fn warmup_symbol_cache(
        &self,
        symbol: &str,
        days: u32,
    ) -> Result<u64> {
        info!("开始预热 {} 的缓存，天数: {}", symbol, days);
        
        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::days(days as i64);
        
        self.sync_symbol_latest_data(symbol, start_time, end_time).await
    }

    /// 获取服务状态
    pub fn is_running(&self) -> bool {
        self.is_running.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 批量同步多个符号的缓存
    pub async fn batch_sync_symbols(&self, symbols: &[String]) -> Result<u64> {
        let mut total_synced = 0u64;

        // 计算同步时间范围（最近1小时的数据）
        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::hours(1);

        for symbol in symbols {
            match self.sync_symbol_latest_data(symbol, start_time, end_time).await {
                Ok(synced_count) => {
                    total_synced += synced_count;
                    debug!("✅ 批量同步 {} 完成，同步了 {} 条记录", symbol, synced_count);
                }
                Err(e) => {
                    warn!("⚠️  批量同步 {} 失败: {}", symbol, e);
                }
            }
        }

        info!("🔄 批量缓存同步完成，总共同步了 {} 条记录", total_synced);
        Ok(total_synced)
    }

    /// 获取同步统计信息
    pub async fn get_sync_stats(&self) -> Result<(u64, u32, u64), CacheError> {
        let state = self.state_manager.get_state().await;
        let sync_stats = &state.cache_sync.sync_stats;
        Ok((sync_stats.synced_records, sync_stats.synced_symbols, sync_stats.sync_duration_ms))
    }
}
