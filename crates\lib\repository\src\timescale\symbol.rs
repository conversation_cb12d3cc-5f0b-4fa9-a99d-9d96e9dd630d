use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use crate::{Result, RepositoryError};

/// 交易符号实体 - TimescaleDB版本
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Symbol {
    /// 交易符号代码 (如: BTCUSDT)
    pub symbol: String,
    /// 是否启用维护
    pub is_active: bool,
    /// 交易所名称
    pub exchange: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 删除时间 (软删除)
    pub deleted_at: Option<DateTime<Utc>>,
}

/// 创建交易符号请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSymbolRequest {
    pub symbol: String,
    pub is_active: bool,
    pub exchange: Option<String>,
}

/// 更新交易符号请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateSymbolRequest {
    pub is_active: Option<bool>,
    pub exchange: Option<String>,
}

/// 符号查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SymbolQuery {
    pub is_active: Option<bool>,
    pub exchange: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

/// 符号统计信息
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SymbolStats {
    pub total_symbols: i64,
    pub active_symbols: i64,
    pub inactive_symbols: i64,
    pub exchanges_count: i64,
}

/// 符号创建统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct SymbolCreationStats {
    pub time_bucket: DateTime<Utc>,
    pub symbols_created: i64,
    pub binance_symbols: i64,
    pub active_symbols: i64,
}

/// TimescaleDB符号Repository
#[derive(Clone)]
pub struct SymbolRepository {
    pool: PgPool,
}

impl SymbolRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化TimescaleDB中的symbols表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        // 注意：TimescaleDB扩展已在连接管理器中初始化，这里直接创建表和超级表

        // 创建symbols表 - 在TimescaleDB中
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS symbols (
                id SERIAL,
                symbol VARCHAR(50) NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                exchange VARCHAR(50) NOT NULL DEFAULT 'binance',
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                deleted_at TIMESTAMPTZ NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将symbols表转换为超级表（以created_at为时间列）
        log::info!("正在创建symbols超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("symbols")
            .bind("created_at")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建symbols超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("symbols超级表创建成功");

        // 创建包含分区列的唯一约束
        // 注意：在TimescaleDB中，唯一约束必须包含分区列
        match sqlx::query(
            "ALTER TABLE symbols ADD CONSTRAINT unique_symbol_created_at 
             UNIQUE (symbol, created_at);"
        )
        .execute(&self.pool)
        .await {
            Ok(_) => log::info!("唯一约束创建成功"),
            Err(e) => {
                // 如果约束已存在，忽略错误
                if e.to_string().contains("already exists") {
                    log::warn!("唯一约束已存在，跳过创建");
                } else {
                    log::error!("创建唯一约束失败: {}", e);
                    return Err(RepositoryError::Database(e));
                }
            }
        }

        // 创建索引优化查询
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_symbols_symbol ON symbols(symbol);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_symbols_is_active ON symbols(is_active);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_symbols_exchange ON symbols(exchange);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_symbols_deleted_at ON symbols(deleted_at);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        // 创建复合索引
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_symbols_active_exchange 
             ON symbols(is_active, exchange) WHERE deleted_at IS NULL;"
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建更新时间触发器函数
        sqlx::query(
            r#"
            CREATE OR REPLACE FUNCTION update_symbols_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建触发器
        sqlx::query("DROP TRIGGER IF EXISTS trigger_symbols_updated_at ON symbols;")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query(
            r#"
            CREATE TRIGGER trigger_symbols_updated_at
            BEFORE UPDATE ON symbols
            FOR EACH ROW
            EXECUTE FUNCTION update_symbols_updated_at();
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 插入默认交易符号
        self.insert_default_symbols().await?;

        log::info!("TimescaleDB Symbols表结构初始化完成");
        Ok(())
    }

    /// 插入默认交易符号
    async fn insert_default_symbols(&self) -> Result<()> {
        let default_symbols = vec![
            ("BTCUSDT", "binance"),
            ("ETHUSDT", "binance"),
            ("SOLUSDT", "binance"),
            // ("XRPUSDT", "binance"),
            // ("BNBUSDT", "binance"),
            // ("DOGEUSDT", "binance"),
            // ("TRXUSDT", "binance"),
            // ("ADAUSDT", "binance"),
            // ("XLMUSDT", "binance"),
            // ("SUIUSDT", "binance"),
            // ("LINKUSDT", "binance"),
            // ("AVAXUSDT", "binance"),
            // ("HBARUSDT", "binance"),
            // ("BCHUSDT", "binance"),
            // ("UNIUSDT", "binance"),
            // ("TONUSDT", "binance"),
            // ("SHIBUSDT", "binance"),
            // ("LTCUSDT", "binance"),
            // ("DOTUSDT", "binance"),
            // ("PEPEUSDT", "binance"),
            // ("AAVEUSDT", "binance"),
            // ("ONDOUSDT", "binance"),
            // ("WLDUSDT", "binance"),
            // ("TAOUSDT", "binance"),
            // ("NEARUSDT", "binance"),
        ];

        for (symbol, exchange) in default_symbols {
            // 检查符号是否已存在
            let exists = sqlx::query_scalar::<_, bool>(
                "SELECT EXISTS(SELECT 1 FROM symbols WHERE symbol = $1 AND deleted_at IS NULL)"
            )
            .bind(symbol)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

            if !exists {
                sqlx::query(
                    r#"
                    INSERT INTO symbols (symbol, is_active, exchange, created_at, updated_at) 
                    VALUES ($1, TRUE, $2, NOW(), NOW())
                    "#
                )
                .bind(symbol)
                .bind(exchange)
                .execute(&self.pool)
                .await
                .map_err(|e| RepositoryError::Database(e))?;
                log::debug!("插入默认符号: {}", symbol);
            } else {
                log::debug!("符号已存在，跳过: {}", symbol);
            }
        }

        log::info!("默认交易符号插入完成");
        Ok(())
    }

    /// 创建交易符号
    pub async fn create(&self, request: CreateSymbolRequest) -> Result<Symbol> {
        let symbol = sqlx::query_as::<_, Symbol>(
            r#"
            INSERT INTO symbols (symbol, is_active, exchange, created_at, updated_at)
            VALUES ($1, $2, $3, NOW(), NOW())
            RETURNING *
            "#,
        )
        .bind(&request.symbol)
        .bind(&request.is_active)
        .bind(&request.exchange.unwrap_or_else(|| "binance".to_string()))
        .fetch_one(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbol)
    }

    /// 根据symbol查找
    pub async fn find_by_symbol(&self, symbol: &str) -> Result<Option<Symbol>> {
        let symbol = sqlx::query_as::<_, Symbol>(
            "SELECT * FROM symbols WHERE symbol = $1 AND deleted_at IS NULL"
        )
        .bind(symbol)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbol)
    }

    /// 获取所有活跃的交易符号
    pub async fn get_active_symbols(&self) -> Result<Vec<Symbol>> {
        let symbols = sqlx::query_as::<_, Symbol>(
            "SELECT * FROM symbols WHERE is_active = TRUE AND deleted_at IS NULL ORDER BY symbol"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbols)
    }

    /// 获取所有活跃的交易符号代码
    pub async fn get_active_symbol_codes(&self) -> Result<Vec<String>> {
        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT symbol FROM symbols WHERE is_active = TRUE AND deleted_at IS NULL ORDER BY symbol"
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbols)
    }

    /// 根据交易所获取活跃符号
    pub async fn get_active_symbols_by_exchange(&self, exchange: &str) -> Result<Vec<Symbol>> {
        let symbols = sqlx::query_as::<_, Symbol>(
            r#"
            SELECT * FROM symbols 
            WHERE is_active = TRUE 
                AND exchange = $1 
                AND deleted_at IS NULL 
            ORDER BY symbol
            "#
        )
        .bind(exchange)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbols)
    }

    /// 查询符号列表
    pub async fn query(&self, params: SymbolQuery) -> Result<Vec<Symbol>> {
        let mut query_builder = sqlx::QueryBuilder::new(
            "SELECT * FROM symbols WHERE deleted_at IS NULL"
        );

        if let Some(is_active) = params.is_active {
            query_builder.push(" AND is_active = ");
            query_builder.push_bind(is_active);
        }

        if let Some(exchange) = params.exchange {
            query_builder.push(" AND exchange = ");
            query_builder.push_bind(exchange);
        }

        query_builder.push(" ORDER BY symbol");

        if let Some(limit) = params.limit {
            query_builder.push(" LIMIT ");
            query_builder.push_bind(limit);
        }

        if let Some(offset) = params.offset {
            query_builder.push(" OFFSET ");
            query_builder.push_bind(offset);
        }

        let symbols = query_builder
            .build_query_as::<Symbol>()
            .fetch_all(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbols)
    }

    /// 更新交易符号
    pub async fn update(&self, symbol: &str, request: UpdateSymbolRequest) -> Result<Option<Symbol>> {
        let mut query_builder = sqlx::QueryBuilder::new(
            "UPDATE symbols SET updated_at = NOW()"
        );

        if let Some(is_active) = request.is_active {
            query_builder.push(", is_active = ");
            query_builder.push_bind(is_active);
        }

        if let Some(exchange) = request.exchange {
            query_builder.push(", exchange = ");
            query_builder.push_bind(exchange);
        }

        query_builder.push(" WHERE symbol = ");
        query_builder.push_bind(symbol);
        query_builder.push(" AND deleted_at IS NULL RETURNING *");

        let symbol = query_builder
            .build_query_as::<Symbol>()
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbol)
    }

    /// 软删除交易符号
    pub async fn soft_delete(&self, symbol: &str) -> Result<bool> {
        let result = sqlx::query(
            "UPDATE symbols SET deleted_at = NOW(), updated_at = NOW() WHERE symbol = $1 AND deleted_at IS NULL"
        )
        .bind(symbol)
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(result.rows_affected() > 0)
    }

    /// 恢复软删除的交易符号
    pub async fn restore(&self, symbol: &str) -> Result<bool> {
        let result = sqlx::query(
            "UPDATE symbols SET deleted_at = NULL, updated_at = NOW() WHERE symbol = $1 AND deleted_at IS NOT NULL"
        )
        .bind(symbol)
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(result.rows_affected() > 0)
    }

    /// 检查符号是否存在
    pub async fn exists(&self, symbol: &str) -> Result<bool> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM symbols WHERE symbol = $1 AND deleted_at IS NULL"
        )
        .bind(symbol)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(count > 0)
    }

    /// 批量创建符号
    pub async fn batch_create(&self, requests: Vec<CreateSymbolRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO symbols (symbol, is_active, exchange, created_at, updated_at) "
        );

        let default_exchange = "binance".to_string();
        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.symbol)
                .push_bind(&request.is_active)
                .push_bind(request.exchange.as_ref().unwrap_or(&default_exchange))
                .push_bind(Utc::now())
                .push_bind(Utc::now());
        });

        query_builder.push(" ON CONFLICT (symbol) DO NOTHING");

        let result = query_builder
            .build()
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        Ok(result.rows_affected())
    }

    /// 批量更新符号状态
    pub async fn batch_update_status(&self, symbols: Vec<String>, is_active: bool) -> Result<u64> {
        if symbols.is_empty() {
            return Ok(0);
        }

        let symbols_array = symbols;
        let result = sqlx::query(
            r#"
            UPDATE symbols 
            SET is_active = $1, updated_at = NOW() 
            WHERE symbol = ANY($2) AND deleted_at IS NULL
            "#
        )
        .bind(is_active)
        .bind(&symbols_array)
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(result.rows_affected())
    }

    /// 获取符号统计信息
    pub async fn get_stats(&self) -> Result<SymbolStats> {
        let stats = sqlx::query_as::<_, SymbolStats>(
            r#"
            SELECT 
                COUNT(*) as total_symbols,
                COUNT(*) FILTER (WHERE is_active = TRUE) as active_symbols,
                COUNT(*) FILTER (WHERE is_active = FALSE) as inactive_symbols,
                COUNT(DISTINCT exchange) as exchanges_count
            FROM symbols 
            WHERE deleted_at IS NULL
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(stats)
    }

    /// 清理旧的软删除记录
    pub async fn cleanup_deleted(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM symbols WHERE deleted_at IS NOT NULL AND deleted_at < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(result.rows_affected())
    }

    /// 获取最近更新的符号
    pub async fn get_recently_updated(&self, since: DateTime<Utc>) -> Result<Vec<Symbol>> {
        let symbols = sqlx::query_as::<_, Symbol>(
            r#"
            SELECT * FROM symbols 
            WHERE updated_at >= $1 AND deleted_at IS NULL 
            ORDER BY updated_at DESC
            "#
        )
        .bind(since)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(symbols)
    }

    /// 使用TimescaleDB的time_bucket进行时间聚合查询
    pub async fn get_symbol_creation_stats(&self, bucket_size: &str) -> Result<Vec<SymbolCreationStats>> {
        let stats = sqlx::query_as::<_, SymbolCreationStats>(
            &format!(
                r#"
                SELECT 
                    time_bucket('{}', created_at) as time_bucket,
                    COUNT(*) as symbols_created,
                    COUNT(*) FILTER (WHERE exchange = 'binance') as binance_symbols,
                    COUNT(*) FILTER (WHERE is_active = TRUE) as active_symbols
                FROM symbols 
                WHERE deleted_at IS NULL
                GROUP BY time_bucket
                ORDER BY time_bucket DESC
                "#,
                bucket_size
            )
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        Ok(stats)
    }
} 