use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 数据库健康检查实体
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct HealthCheckEntity {
    pub test_value: i32,
}

/// 扩展检查实体
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct ExtensionEntity {
    pub extname: String,
}

/// 表计数实体
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct TableCountEntity {
    pub table_count: i64,
}

/// 时间桶测试实体
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct TimeBucketTestEntity {
    pub test_bucket: DateTime<Utc>,
} 