use axum::{
    extract::{Query, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
use common::domain::r#enum::error::{AppError, ErrorResponse};
use crate::router::AppState;

/// 维护服务状态响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后全量扫描时间
    pub last_full_scan: Option<String>,
    /// 最后增量扫描时间  
    pub last_incremental_scan: Option<String>,
    /// 服务启动时间
    pub uptime_seconds: Option<i64>,
    /// 服务健康状态
    pub health_status: std::collections::HashMap<String, bool>,
}

/// 符号统计响应
#[derive(Serialize, ToSchema)]
pub struct SymbolStatsResponse {
    /// 总符号数
    pub total_symbols: i64,
    /// 活跃符号数
    pub active_symbols: i64,
    /// 非活跃符号数
    pub inactive_symbols: i64,
    /// 交易所数量
    pub exchanges_count: i64,
}

/// 存储统计响应
#[derive(Serialize, ToSchema)]
pub struct StorageStatsResponse {
    /// 符号统计
    pub symbol_stats: SymbolStatsResponse,
    /// K线数据总记录数
    pub total_kline_records: i64,
    /// 数据时间范围
    pub data_time_range: Option<DataTimeRange>,
    /// 活跃符号列表
    pub active_symbols: Vec<String>,
}

/// 数据时间范围
#[derive(Serialize, ToSchema)]
pub struct DataTimeRange {
    /// 最早数据时间
    pub earliest: Option<String>,
    /// 最新数据时间
    pub latest: Option<String>,
}

/// 维护操作请求参数
#[derive(Deserialize, IntoParams, ToSchema)]
pub struct MaintenanceActionParams {
    /// 操作类型: integrity_check, manual_update, cache_cleanup
    pub action: String,
}

/// 维护操作响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceActionResponse {
    /// 操作是否成功
    pub success: bool,
    /// 操作消息
    pub message: String,
    /// 操作详情
    pub details: Option<serde_json::Value>,
}

/// 符号管理请求
#[derive(Deserialize, ToSchema)]
pub struct SymbolManagementRequest {
    /// 符号列表
    pub symbols: Vec<String>,
    /// 是否激活
    pub is_active: bool,
}

/// 符号管理响应
#[derive(Serialize, ToSchema)]
pub struct SymbolManagementResponse {
    /// 更新的符号数量
    pub updated_count: u64,
    /// 更新的符号列表
    pub updated_symbols: Vec<String>,
}

/// 缓存同步状态响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后同步时间
    pub last_sync: Option<String>,
    /// 同步统计
    pub sync_stats: CacheSyncStatsResponse,
    /// 错误信息
    pub last_error: Option<String>,
}

/// 缓存同步统计响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatsResponse {
    /// 同步的记录数
    pub synced_records: u64,
    /// 同步的符号数
    pub synced_symbols: u32,
    /// 同步耗时（毫秒）
    pub sync_duration_ms: u64,
}

/// 维护进度响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceProgressResponse {
    /// 总符号数
    pub total_symbols: u32,
    /// 已处理符号数
    pub processed_symbols: u32,
    /// 当前阶段
    pub current_phase: String,
    /// 当前处理的符号
    pub current_symbol: Option<String>,
    /// 进度百分比
    pub progress_percentage: f64,
    /// 预计完成时间
    pub estimated_completion: Option<String>,
}

/// 获取维护服务状态
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/status",
    responses(
        (status = 200, description = "成功获取维护服务状态", body = MaintenanceStatusResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_status(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceStatusResponse>, AppError> {
    // 从市场数据管理器获取健康状态
    let health_status = state.market_data_manager().health_check().await
        .map_err(|e| AppError::Internal(format!("获取健康状态失败: {}", e)))?;
    
    // 获取服务统计信息
    let stats = state.market_data_manager().get_service_statistics().await
        .map_err(|e| AppError::Internal(format!("获取统计信息失败: {}", e)))?;

    let status = MaintenanceStatusResponse {
        is_running: health_status.get("background_task").copied().unwrap_or(false),
        last_full_scan: None, // 需要从统计信息中提取
        last_incremental_scan: None, // 需要从统计信息中提取
        uptime_seconds: None, // 可以从服务启动时间计算
        health_status,
    };

    Ok(Json(status))
}

/// 获取维护进度
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/progress",
    responses(
        (status = 200, description = "成功获取维护进度", body = MaintenanceProgressResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_progress(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceProgressResponse>, AppError> {
    let config = state.market_data_manager().get_config();
    let total_symbols = config.active_symbols.len() as u32;

    let progress = MaintenanceProgressResponse {
        total_symbols,
        processed_symbols: total_symbols, // 简化实现
        current_phase: "运行中".to_string(),
        current_symbol: None,
        progress_percentage: 100.0,
        estimated_completion: None,
    };

    Ok(Json(progress))
}

/// 获取符号统计
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/stats",
    responses(
        (status = 200, description = "成功获取符号统计", body = SymbolStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_symbol_stats(
    State(state): State<AppState>,
) -> Result<Json<SymbolStatsResponse>, AppError> {
    let config = state.market_data_manager().get_config();
    let active_count = config.active_symbols.len() as i64;

    let stats = SymbolStatsResponse {
        total_symbols: active_count,
        active_symbols: active_count,
        inactive_symbols: 0,
        exchanges_count: 1, // 目前只支持Binance
    };

    Ok(Json(stats))
}

/// 获取存储统计
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/storage/stats",
    responses(
        (status = 200, description = "成功获取存储统计", body = StorageStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_storage_stats(
    State(state): State<AppState>,
) -> Result<Json<StorageStatsResponse>, AppError> {
    let config = state.market_data_manager().get_config();
    let active_symbols = config.active_symbols.clone();

    let symbol_stats = SymbolStatsResponse {
        total_symbols: active_symbols.len() as i64,
        active_symbols: active_symbols.len() as i64,
        inactive_symbols: 0,
        exchanges_count: 1,
    };

    let stats = StorageStatsResponse {
        symbol_stats,
        total_kline_records: 0, // 需要从数据库查询
        data_time_range: Some(DataTimeRange {
            earliest: Some(config.integrity_check_start_date.to_rfc3339()),
            latest: Some(chrono::Utc::now().to_rfc3339()),
        }),
        active_symbols,
    };

    Ok(Json(stats))
}

/// 执行维护操作
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/action",
    params(MaintenanceActionParams),
    responses(
        (status = 200, description = "成功执行维护操作", body = MaintenanceActionResponse),
        (status = 400, description = "请求参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn execute_maintenance_action(
    State(state): State<AppState>,
    Query(params): Query<MaintenanceActionParams>,
) -> Result<Json<MaintenanceActionResponse>, AppError> {
    let result = match params.action.as_str() {
        "integrity_check" => {
            match state.market_data_manager().perform_full_integrity_check().await {
                Ok(results) => MaintenanceActionResponse {
                    success: true,
                    message: format!("完整性检查完成，检查了 {} 个结果", results.len()),
                    details: Some(serde_json::to_value(results).unwrap_or_default()),
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("完整性检查失败: {}", e),
                    details: None,
                },
            }
        },
        "manual_update" => {
            match state.market_data_manager().trigger_manual_update().await {
                Ok(results) => MaintenanceActionResponse {
                    success: true,
                    message: format!("手动更新完成，更新了 {} 个结果", results.len()),
                    details: Some(serde_json::to_value(results).unwrap_or_default()),
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("手动更新失败: {}", e),
                    details: None,
                },
            }
        },
        "cache_cleanup" => {
            match state.market_data_manager().cleanup_expired_cache().await {
                Ok(count) => MaintenanceActionResponse {
                    success: true,
                    message: format!("缓存清理完成，清理了 {} 个过期项", count),
                    details: None,
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("缓存清理失败: {}", e),
                    details: None,
                },
            }
        },
        _ => MaintenanceActionResponse {
            success: false,
            message: format!("不支持的操作: {}", params.action),
            details: None,
        },
    };

    Ok(Json(result))
}

/// 管理符号
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/symbols/manage",
    request_body = SymbolManagementRequest,
    responses(
        (status = 200, description = "成功管理符号", body = SymbolManagementResponse),
        (status = 400, description = "请求参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn manage_symbols(
    State(state): State<AppState>,
    Json(request): Json<SymbolManagementRequest>,
) -> Result<Json<SymbolManagementResponse>, AppError> {
    // 目前简化实现，实际应该更新配置并重启服务
    let response = SymbolManagementResponse {
        updated_count: request.symbols.len() as u64,
        updated_symbols: request.symbols,
    };

    Ok(Json(response))
}

/// 获取活跃符号列表
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/active",
    responses(
        (status = 200, description = "成功获取活跃符号列表", body = Vec<String>),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_active_symbols(
    State(state): State<AppState>,
) -> Result<Json<Vec<String>>, AppError> {
    let config = state.market_data_manager().get_config();
    Ok(Json(config.active_symbols.clone()))
}

/// 创建维护路由
pub fn maintenance_routes() -> Router<AppState> {
    Router::new()
        .route("/status", get(get_maintenance_status))
        .route("/progress", get(get_maintenance_progress))
        .route("/symbols/stats", get(get_symbol_stats))
        .route("/storage/stats", get(get_storage_stats))
        .route("/action", post(execute_maintenance_action))
        .route("/symbols/manage", post(manage_symbols))
        .route("/symbols/active", get(get_active_symbols))
}