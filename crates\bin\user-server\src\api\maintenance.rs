use axum::{
    extract::{Query, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
use common::domain::r#enum::error::{AppError, ErrorResponse};
use crate::router::AppState;

/// 维护服务状态响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后全量扫描时间
    pub last_full_scan: Option<String>,
    /// 最后增量扫描时间  
    pub last_incremental_scan: Option<String>,
    /// 服务启动时间
    pub uptime_seconds: Option<i64>,
    /// 服务健康状态
    pub health_status: std::collections::HashMap<String, bool>,
}

/// 符号统计响应
#[derive(Serialize, ToSchema)]
pub struct SymbolStatsResponse {
    /// 总符号数
    pub total_symbols: i64,
    /// 活跃符号数
    pub active_symbols: i64,
    /// 非活跃符号数
    pub inactive_symbols: i64,
    /// 交易所数量
    pub exchanges_count: i64,
}

/// 存储统计响应
#[derive(Serialize, ToSchema)]
pub struct StorageStatsResponse {
    /// 符号统计
    pub symbol_stats: SymbolStatsResponse,
    /// K线数据总记录数
    pub total_kline_records: i64,
    /// 数据时间范围
    pub data_time_range: Option<DataTimeRange>,
    /// 活跃符号列表
    pub active_symbols: Vec<String>,
}

/// 数据时间范围
#[derive(Serialize, ToSchema)]
pub struct DataTimeRange {
    /// 最早数据时间
    pub earliest: Option<String>,
    /// 最新数据时间
    pub latest: Option<String>,
}

/// 维护操作请求参数
#[derive(Deserialize, IntoParams, ToSchema)]
pub struct MaintenanceActionParams {
    /// 操作类型: integrity_check, manual_update, cache_cleanup
    pub action: String,
}

/// 维护操作响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceActionResponse {
    /// 操作是否成功
    pub success: bool,
    /// 操作消息
    pub message: String,
    /// 操作详情
    pub details: Option<serde_json::Value>,
}

/// 符号管理请求
#[derive(Deserialize, ToSchema)]
pub struct SymbolManagementRequest {
    /// 符号列表
    pub symbols: Vec<String>,
    /// 是否激活
    pub is_active: bool,
}

/// 符号管理响应
#[derive(Serialize, ToSchema)]
pub struct SymbolManagementResponse {
    /// 更新的符号数量
    pub updated_count: u64,
    /// 更新的符号列表
    pub updated_symbols: Vec<String>,
}

/// 缓存同步状态响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后同步时间
    pub last_sync: Option<String>,
    /// 同步统计
    pub sync_stats: CacheSyncStatsResponse,
    /// 错误信息
    pub last_error: Option<String>,
}

/// 缓存同步统计响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatsResponse {
    /// 同步的记录数
    pub synced_records: u64,
    /// 同步的符号数
    pub synced_symbols: u32,
    /// 同步耗时（毫秒）
    pub sync_duration_ms: u64,
}

/// 维护进度响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceProgressResponse {
    /// 总符号数
    pub total_symbols: u32,
    /// 已处理符号数
    pub processed_symbols: u32,
    /// 当前阶段
    pub current_phase: String,
    /// 当前处理的符号
    pub current_symbol: Option<String>,
    /// 进度百分比
    pub progress_percentage: f64,
    /// 预计完成时间
    pub estimated_completion: Option<String>,
}

/// 获取维护服务状态
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/status",
    responses(
        (status = 200, description = "成功获取维护服务状态", body = MaintenanceStatusResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_status(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceStatusResponse>, AppError> {
    // 从市场数据管理器获取服务状态
    let service_status = state.market_data_manager().get_service_status().await
        .map_err(|e| AppError::Internal(format!("获取服务状态失败: {}", e)))?;

    // 构建健康状态映射
    let mut health_status = std::collections::HashMap::new();
    health_status.insert("total_symbols".to_string(), service_status.total_symbols > 0);
    health_status.insert("healthy_symbols".to_string(), service_status.healthy_symbols > 0);
    health_status.insert("background_task".to_string(), true); // 假设服务正在运行

    let status = MaintenanceStatusResponse {
        is_running: service_status.total_symbols > 0,
        last_full_scan: None, // 可以从service_status.last_update获取
        last_incremental_scan: Some(service_status.last_update.to_rfc3339()),
        uptime_seconds: None, // 可以从服务启动时间计算
        health_status,
    };

    Ok(Json(status))
}

/// 获取维护进度
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/progress",
    responses(
        (status = 200, description = "成功获取维护进度", body = MaintenanceProgressResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_progress(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceProgressResponse>, AppError> {
    let service_status = state.market_data_manager().get_service_status().await
        .map_err(|e| AppError::Internal(format!("获取服务状态失败: {}", e)))?;
    let total_symbols = service_status.total_symbols as u32;

    let progress = MaintenanceProgressResponse {
        total_symbols,
        processed_symbols: total_symbols, // 简化实现
        current_phase: "运行中".to_string(),
        current_symbol: None,
        progress_percentage: 100.0,
        estimated_completion: None,
    };

    Ok(Json(progress))
}

/// 获取符号统计
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/stats",
    responses(
        (status = 200, description = "成功获取符号统计", body = SymbolStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_symbol_stats(
    State(state): State<AppState>,
) -> Result<Json<SymbolStatsResponse>, AppError> {
    let service_status = state.market_data_manager().get_service_status().await
        .map_err(|e| AppError::Internal(format!("获取服务状态失败: {}", e)))?;
    let active_count = service_status.total_symbols as i64;

    let stats = SymbolStatsResponse {
        total_symbols: active_count,
        active_symbols: active_count,
        inactive_symbols: 0,
        exchanges_count: 1, // 目前只支持Binance
    };

    Ok(Json(stats))
}

/// 获取存储统计
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/storage/stats",
    responses(
        (status = 200, description = "成功获取存储统计", body = StorageStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_storage_stats(
    State(state): State<AppState>,
) -> Result<Json<StorageStatsResponse>, AppError> {
    let service_status = state.market_data_manager().get_service_status().await
        .map_err(|e| AppError::Internal(format!("获取服务状态失败: {}", e)))?;

    let symbol_stats = SymbolStatsResponse {
        total_symbols: service_status.total_symbols as i64,
        active_symbols: service_status.healthy_symbols as i64,
        inactive_symbols: service_status.critical_symbols as i64,
        exchanges_count: 1,
    };

    let active_symbols = service_status.config.symbols.clone();

    let stats = StorageStatsResponse {
        symbol_stats,
        total_kline_records: 0, // 需要从数据库查询
        data_time_range: Some(DataTimeRange {
            earliest: Some(service_status.last_update.to_rfc3339()),
            latest: Some(chrono::Utc::now().to_rfc3339()),
        }),
        active_symbols,
    };

    Ok(Json(stats))
}

/// 执行维护操作
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/action",
    params(MaintenanceActionParams),
    responses(
        (status = 200, description = "成功执行维护操作", body = MaintenanceActionResponse),
        (status = 400, description = "请求参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn execute_maintenance_action(
    State(state): State<AppState>,
    Query(params): Query<MaintenanceActionParams>,
) -> Result<Json<MaintenanceActionResponse>, AppError> {
    let result = match params.action.as_str() {
        "integrity_check" => {
            match state.market_data_manager().get_service_status().await {
                Ok(status) => MaintenanceActionResponse {
                    success: true,
                    message: format!("状态检查完成，总交易对: {}, 健康: {}, 警告: {}, 严重: {}",
                        status.total_symbols, status.healthy_symbols, status.warning_symbols, status.critical_symbols),
                    details: Some(serde_json::to_value(status).unwrap_or_default()),
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("状态检查失败: {}", e),
                    details: None,
                },
            }
        },
        "manual_update" => {
            match state.market_data_manager().get_service_status().await {
                Ok(status) => MaintenanceActionResponse {
                    success: true,
                    message: format!("服务状态获取成功，最后更新: {}", status.last_update),
                    details: Some(serde_json::to_value(status).unwrap_or_default()),
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("服务状态获取失败: {}", e),
                    details: None,
                },
            }
        },
        "cache_cleanup" => {
            // 简化为状态检查
            match state.market_data_manager().get_service_status().await {
                Ok(status) => MaintenanceActionResponse {
                    success: true,
                    message: "状态检查完成".to_string(),
                    details: Some(serde_json::to_value(status).unwrap_or_default()),
                },
                Err(e) => MaintenanceActionResponse {
                    success: false,
                    message: format!("状态检查失败: {}", e),
                    details: None,
                },
            }
        },
        _ => MaintenanceActionResponse {
            success: false,
            message: format!("不支持的操作: {}", params.action),
            details: None,
        },
    };

    Ok(Json(result))
}

/// 管理符号
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/symbols/manage",
    request_body = SymbolManagementRequest,
    responses(
        (status = 200, description = "成功管理符号", body = SymbolManagementResponse),
        (status = 400, description = "请求参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn manage_symbols(
    State(state): State<AppState>,
    Json(request): Json<SymbolManagementRequest>,
) -> Result<Json<SymbolManagementResponse>, AppError> {
    // 目前简化实现，实际应该更新配置并重启服务
    let response = SymbolManagementResponse {
        updated_count: request.symbols.len() as u64,
        updated_symbols: request.symbols,
    };

    Ok(Json(response))
}

/// 获取活跃符号列表
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/active",
    responses(
        (status = 200, description = "成功获取活跃符号列表", body = Vec<String>),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_active_symbols(
    State(state): State<AppState>,
) -> Result<Json<Vec<String>>, AppError> {
    let service_status = state.market_data_manager().get_service_status().await
        .map_err(|e| AppError::Internal(format!("获取服务状态失败: {}", e)))?;
    Ok(Json(service_status.config.symbols.clone()))
}

/// 创建维护路由
pub fn maintenance_routes() -> Router<AppState> {
    Router::new()
        .route("/status", get(get_maintenance_status))
        .route("/progress", get(get_maintenance_progress))
        .route("/symbols/stats", get(get_symbol_stats))
        .route("/storage/stats", get(get_storage_stats))
        .route("/action", post(execute_maintenance_action))
        .route("/symbols/manage", post(manage_symbols))
        .route("/symbols/active", get(get_active_symbols))
}