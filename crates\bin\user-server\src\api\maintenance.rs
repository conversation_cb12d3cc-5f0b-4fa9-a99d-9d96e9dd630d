use axum::{
    extract::{Query, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
use common::domain::r#enum::error::{AppError, ErrorResponse};
use service::market_data::get_global_state_manager;
use crate::router::AppState;


/// 维护服务状态响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后全量扫描时间
    pub last_full_scan: Option<String>,
    /// 最后增量扫描时间  
    pub last_incremental_scan: Option<String>,
    /// 服务启动时间
    pub uptime_seconds: Option<i64>,
}

/// 符号统计响应
#[derive(Serialize, ToSchema)]
pub struct SymbolStatsResponse {
    /// 总符号数
    pub total_symbols: i64,
    /// 活跃符号数
    pub active_symbols: i64,
    /// 非活跃符号数
    pub inactive_symbols: i64,
    /// 交易所数量
    pub exchanges_count: i64,
}

/// 存储统计响应
#[derive(Serialize, ToSchema)]
pub struct StorageStatsResponse {
    /// 符号统计
    pub symbol_stats: SymbolStatsResponse,
    /// K线数据总记录数
    pub total_kline_records: i64,
    /// 数据时间范围
    pub data_time_range: Option<DataTimeRange>,
    /// 活跃符号列表
    pub active_symbols: Vec<String>,
}

/// 数据时间范围
#[derive(Serialize, ToSchema)]
pub struct DataTimeRange {
    /// 最早数据时间
    pub earliest: Option<String>,
    /// 最新数据时间
    pub latest: Option<String>,
}

/// 维护操作请求参数
#[derive(Deserialize, IntoParams, ToSchema)]
pub struct MaintenanceActionParams {
    /// 操作类型: full_scan, incremental_scan, cleanup
    pub action: String,
}

/// 维护操作响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceActionResponse {
    /// 操作是否成功
    pub success: bool,
    /// 操作消息
    pub message: String,
    /// 操作详情
    pub details: Option<serde_json::Value>,
}

/// 符号管理请求
#[derive(Deserialize, ToSchema)]
pub struct SymbolManagementRequest {
    /// 符号列表
    pub symbols: Vec<String>,
    /// 是否激活
    pub is_active: bool,
}

/// 符号管理响应
#[derive(Serialize, ToSchema)]
pub struct SymbolManagementResponse {
    /// 更新的符号数量
    pub updated_count: u64,
    /// 更新的符号列表
    pub updated_symbols: Vec<String>,
}

/// 缓存同步状态响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatusResponse {
    /// 服务是否运行中
    pub is_running: bool,
    /// 最后同步时间
    pub last_sync: Option<String>,
    /// 同步统计
    pub sync_stats: CacheSyncStatsResponse,
    /// 错误信息
    pub last_error: Option<String>,
}

/// 缓存同步统计响应
#[derive(Serialize, ToSchema)]
pub struct CacheSyncStatsResponse {
    /// 同步的记录数
    pub synced_records: u64,
    /// 同步的符号数
    pub synced_symbols: u32,
    /// 同步耗时（毫秒）
    pub sync_duration_ms: u64,
}

/// 维护进度响应
#[derive(Serialize, ToSchema)]
pub struct MaintenanceProgressResponse {
    /// 总符号数
    pub total_symbols: u32,
    /// 已处理符号数
    pub processed_symbols: u32,
    /// 当前阶段
    pub current_phase: String,
    /// 当前处理的符号
    pub current_symbol: Option<String>,
    /// 进度百分比
    pub progress_percentage: f64,
    /// 预计完成时间
    pub estimated_completion: Option<String>,
}

/// 获取维护服务状态
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/status",
    responses(
        (status = 200, description = "成功获取维护服务状态", body = MaintenanceStatusResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_status(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceStatusResponse>, AppError> {
    // 从全局状态管理器获取真实状态
    let state_manager = get_global_state_manager().await;
    let service_state = state_manager.get_state().await;

    let maintenance_state = &service_state.maintenance;

    // 计算运行时间
    let uptime_seconds = maintenance_state.started_at.map(|start| {
        (chrono::Utc::now() - start).num_seconds()
    });

    let status = MaintenanceStatusResponse {
        is_running: maintenance_state.is_running,
        last_full_scan: maintenance_state.last_full_scan.map(|t| t.to_rfc3339()),
        last_incremental_scan: maintenance_state.last_incremental_scan.map(|t| t.to_rfc3339()),
        uptime_seconds,
    };

    Ok(Json(status))
}

/// 获取缓存同步状态
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/cache/status",
    responses(
        (status = 200, description = "成功获取缓存同步状态", body = CacheSyncStatusResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_cache_sync_status(
    State(state): State<AppState>,
) -> Result<Json<CacheSyncStatusResponse>, AppError> {
    // 从全局状态管理器获取缓存同步状态
    let state_manager = get_global_state_manager().await;
    let service_state = state_manager.get_state().await;

    let cache_sync_state = &service_state.cache_sync;

    let status = CacheSyncStatusResponse {
        is_running: cache_sync_state.is_running,
        last_sync: cache_sync_state.last_sync.map(|t| t.to_rfc3339()),
        sync_stats: CacheSyncStatsResponse {
            synced_records: cache_sync_state.sync_stats.synced_records,
            synced_symbols: cache_sync_state.sync_stats.synced_symbols,
            sync_duration_ms: cache_sync_state.sync_stats.sync_duration_ms,
        },
        last_error: cache_sync_state.last_error.clone(),
    };

    Ok(Json(status))
}

/// 获取维护进度
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/progress",
    responses(
        (status = 200, description = "成功获取维护进度", body = MaintenanceProgressResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_maintenance_progress(
    State(state): State<AppState>,
) -> Result<Json<MaintenanceProgressResponse>, AppError> {
    // 从全局状态管理器获取维护进度
    let state_manager = get_global_state_manager().await;
    let service_state = state_manager.get_state().await;

    let maintenance_state = &service_state.maintenance;
    let progress = &maintenance_state.progress;

    // 计算进度百分比
    let progress_percentage = if progress.total_symbols > 0 {
        (progress.processed_symbols as f64 / progress.total_symbols as f64) * 100.0
    } else {
        0.0
    };

    let response = MaintenanceProgressResponse {
        total_symbols: progress.total_symbols,
        processed_symbols: progress.processed_symbols,
        current_phase: progress.current_phase.clone(),
        current_symbol: maintenance_state.current_symbol.clone(),
        progress_percentage,
        estimated_completion: progress.estimated_completion.map(|t| t.to_rfc3339()),
    };

    Ok(Json(response))
}

/// 获取符号统计信息
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/stats",
    responses(
        (status = 200, description = "成功获取符号统计信息", body = SymbolStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_symbol_stats(
    State(state): State<AppState>,
) -> Result<Json<SymbolStatsResponse>, AppError> {
    // 这里需要通过storage manager获取符号统计
    // 暂时返回模拟数据
    let stats = SymbolStatsResponse {
        total_symbols: 100,
        active_symbols: 50,
        inactive_symbols: 50,
        exchanges_count: 1,
    };

    Ok(Json(stats))
}

/// 获取存储统计信息
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/storage/stats",
    responses(
        (status = 200, description = "成功获取存储统计信息", body = StorageStatsResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_storage_stats(
    State(state): State<AppState>,
) -> Result<Json<StorageStatsResponse>, AppError> {
    // 这里需要通过storage manager获取存储统计
    // 暂时返回模拟数据
    let storage_stats = StorageStatsResponse {
        symbol_stats: SymbolStatsResponse {
            total_symbols: 100,
            active_symbols: 50,
            inactive_symbols: 50,
            exchanges_count: 1,
        },
        total_kline_records: 1000000,
        data_time_range: Some(DataTimeRange {
            earliest: Some("2024-01-01T00:00:00Z".to_string()),
            latest: Some("2024-01-01T23:59:59Z".to_string()),
        }),
        active_symbols: vec![
            "BTCUSDT".to_string(),
            "ETHUSDT".to_string(),
            "BNBUSDT".to_string(),
        ],
    };

    Ok(Json(storage_stats))
}

/// 执行维护操作
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/actions",
    params(MaintenanceActionParams),
    responses(
        (status = 200, description = "维护操作执行成功", body = MaintenanceActionResponse),
        (status = 400, description = "参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn execute_maintenance_action(
    State(state): State<AppState>,
    Query(params): Query<MaintenanceActionParams>,
) -> Result<Json<MaintenanceActionResponse>, AppError> {
    match params.action.as_str() {
        "full_scan" => {
            // 触发全量扫描
            let response = MaintenanceActionResponse {
                success: true,
                message: "全量扫描已触发".to_string(),
                details: Some(serde_json::json!({
                    "scan_type": "full",
                    "estimated_duration": "30 minutes"
                })),
            };
            Ok(Json(response))
        }
        "incremental_scan" => {
            // 触发增量扫描
            let response = MaintenanceActionResponse {
                success: true,
                message: "增量扫描已触发".to_string(),
                details: Some(serde_json::json!({
                    "scan_type": "incremental",
                    "estimated_duration": "5 minutes"
                })),
            };
            Ok(Json(response))
        }
        "cleanup" => {
            // 执行数据清理
            let response = MaintenanceActionResponse {
                success: true,
                message: "数据清理已开始".to_string(),
                details: Some(serde_json::json!({
                    "cleanup_type": "old_data",
                    "threshold_days": 365
                })),
            };
            Ok(Json(response))
        }
        _ => {
            Err(AppError::BadRequest("不支持的维护操作".to_string()))
        }
    }
}

/// 管理符号状态
#[utoipa::path(
    post,
    path = "/api/v1/maintenance/symbols/manage",
    request_body = SymbolManagementRequest,
    responses(
        (status = 200, description = "符号状态更新成功", body = SymbolManagementResponse),
        (status = 400, description = "参数错误", body = ErrorResponse),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn manage_symbols(
    State(state): State<AppState>,
    Json(request): Json<SymbolManagementRequest>,
) -> Result<Json<SymbolManagementResponse>, AppError> {
    // 这里需要通过symbol manager更新符号状态
    // 暂时返回模拟数据
    let response = SymbolManagementResponse {
        updated_count: request.symbols.len() as u64,
        updated_symbols: request.symbols,
    };

    Ok(Json(response))
}

/// 获取活跃符号列表
#[utoipa::path(
    get,
    path = "/api/v1/maintenance/symbols/active",
    responses(
        (status = 200, description = "成功获取活跃符号列表", body = Vec<String>),
        (status = 500, description = "服务器内部错误", body = ErrorResponse)
    ),
    tag = "maintenance"
)]
pub async fn get_active_symbols(
    State(state): State<AppState>,
) -> Result<Json<Vec<String>>, AppError> {
    // 这里需要通过symbol manager获取活跃符号
    // 暂时返回模拟数据
    let symbols = vec![
        "BTCUSDT".to_string(),
        "ETHUSDT".to_string(),
        "BNBUSDT".to_string(),
        "ADAUSDT".to_string(),
        "XRPUSDT".to_string(),
    ];

    Ok(Json(symbols))
}

/// 创建维护API路由
pub fn maintenance_routes() -> Router<AppState> {
    Router::new()
        .route("/status", get(get_maintenance_status))
        .route("/progress", get(get_maintenance_progress))
        .route("/cache/status", get(get_cache_sync_status))
        .route("/symbols/stats", get(get_symbol_stats))
        .route("/symbols/active", get(get_active_symbols))
        .route("/symbols/manage", post(manage_symbols))
        .route("/storage/stats", get(get_storage_stats))
        .route("/actions", post(execute_maintenance_action))
}