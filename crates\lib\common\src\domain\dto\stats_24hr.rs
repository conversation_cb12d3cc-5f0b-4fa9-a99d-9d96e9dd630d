use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use utoipa::ToSchema;
use serde::{Deserialize, Serialize};

use crate::domain::entity::Stats24hrEntity;

/// 24小时统计数据DTO - 用于API响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct Stats24hrDto {
    /// 交易对符号
    pub symbol: String,
    /// 价格变动
    pub price_change: Decimal,
    /// 价格变动百分比
    pub price_change_percent: Decimal,
    /// 加权平均价格
    pub weighted_avg_price: Decimal,
    /// 前一日收盘价
    pub prev_close_price: Decimal,
    /// 最新价格
    pub last_price: Decimal,
    /// 最新成交量
    pub last_qty: Decimal,
    /// 买一价
    pub bid_price: Decimal,
    /// 卖一价
    pub ask_price: Decimal,
    /// 开盘价
    pub open_price: Decimal,
    /// 最高价
    pub high_price: Decimal,
    /// 最低价
    pub low_price: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 计价资产成交量
    pub quote_volume: Decimal,
    /// 开盘时间
    pub open_time: DateTime<Utc>,
    /// 收盘时间
    pub close_time: DateTime<Utc>,
    /// 首次交易ID
    pub first_id: i64,
    /// 最后交易ID
    pub last_id: i64,
    /// 交易次数
    pub count: i64,
}

impl From<Stats24hrEntity> for Stats24hrDto {
    fn from(entity: Stats24hrEntity) -> Self {
        Self {
            symbol: entity.symbol,
            price_change: entity.price_change,
            price_change_percent: entity.price_change_percent,
            weighted_avg_price: entity.weighted_avg_price,
            prev_close_price: entity.prev_close_price,
            last_price: entity.last_price,
            last_qty: entity.last_qty,
            bid_price: entity.bid_price,
            ask_price: entity.ask_price,
            open_price: entity.open_price,
            high_price: entity.high_price,
            low_price: entity.low_price,
            volume: entity.volume,
            quote_volume: entity.quote_volume,
            open_time: entity.open_time,
            close_time: entity.close_time,
            first_id: entity.first_id,
            last_id: entity.last_id,
            count: entity.count,
        }
    }
}

impl From<Stats24hrDto> for Stats24hrEntity {
    fn from(dto: Stats24hrDto) -> Self {
        Self {
            time: dto.close_time, // 使用close_time作为主键时间
            symbol: dto.symbol,
            price_change: dto.price_change,
            price_change_percent: dto.price_change_percent,
            weighted_avg_price: dto.weighted_avg_price,
            prev_close_price: dto.prev_close_price,
            last_price: dto.last_price,
            last_qty: dto.last_qty,
            bid_price: dto.bid_price,
            ask_price: dto.ask_price,
            open_price: dto.open_price,
            high_price: dto.high_price,
            low_price: dto.low_price,
            volume: dto.volume,
            quote_volume: dto.quote_volume,
            open_time: dto.open_time,
            close_time: dto.close_time,
            first_id: dto.first_id,
            last_id: dto.last_id,
            count: dto.count,
        }
    }
} 