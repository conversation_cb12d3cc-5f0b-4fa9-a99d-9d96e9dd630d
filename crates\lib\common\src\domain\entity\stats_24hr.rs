use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 24小时统计数据实体 - 数据库映射
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Stats24hrEntity {
    /// 时间戳
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 价格变动
    pub price_change: Decimal,
    /// 价格变动百分比
    pub price_change_percent: Decimal,
    /// 加权平均价格
    pub weighted_avg_price: Decimal,
    /// 前一日收盘价
    pub prev_close_price: Decimal,
    /// 最新价格
    pub last_price: Decimal,
    /// 最新成交量
    pub last_qty: Decimal,
    /// 买一价
    pub bid_price: Decimal,
    /// 卖一价
    pub ask_price: Decimal,
    /// 开盘价
    pub open_price: Decimal,
    /// 最高价
    pub high_price: Decimal,
    /// 最低价
    pub low_price: Decimal,
    /// 成交量
    pub volume: Decimal,
    /// 计价资产成交量
    pub quote_volume: Decimal,
    /// 开盘时间
    pub open_time: DateTime<Utc>,
    /// 收盘时间
    pub close_time: DateTime<Utc>,
    /// 首次交易ID
    pub first_id: i64,
    /// 最后交易ID
    pub last_id: i64,
    /// 交易次数
    pub count: i64,
}