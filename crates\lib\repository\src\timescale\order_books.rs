use sqlx::{PgPool, Row};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 订单簿条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookEntry {
    pub price: Decimal,
    pub quantity: Decimal,
}

/// 订单簿数据实体
#[derive(Debug, Clone, sqlx::FromRow, Serialize, Deserialize)]
pub struct OrderBookEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub last_update_id: i64,
    pub bids: serde_json::Value, // JSON array of [price, quantity]
    pub asks: serde_json::Value, // JSON array of [price, quantity]
}

/// 订单簿数据插入请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateOrderBookRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub last_update_id: i64,
    pub bids: Vec<OrderBookEntry>,
    pub asks: Vec<OrderBookEntry>,
}

/// 订单簿查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookQuery {
    pub symbol: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub limit: Option<u32>,
}

/// 订单簿统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct OrderBookStats {
    pub symbol: String,
    pub avg_bid_price: Option<Decimal>,
    pub avg_ask_price: Option<Decimal>,
    pub avg_spread: Option<Decimal>,
    pub avg_spread_percent: Option<Decimal>,
    pub count: i64,
}

/// 简化的订单簿快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookSnapshot {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub best_bid: Option<Decimal>,
    pub best_ask: Option<Decimal>,
    pub spread: Option<Decimal>,
    pub bid_depth: Decimal,  // 前5档买单总量
    pub ask_depth: Decimal,  // 前5档卖单总量
}

/// 订单簿数据Repository
#[derive(Clone)]
pub struct OrderBookRepository {
    pool: PgPool,
}

impl OrderBookRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化订单簿表结构
    pub async fn initialize_table(&self) -> Result<()> {
        // 创建深度数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS order_books (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                last_update_id BIGINT NOT NULL,
                bids JSONB NOT NULL,
                asks JSONB NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将深度表转换为超级表
        log::info!("正在创建order_books超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("order_books")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建order_books超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("order_books超级表创建成功");

        // 添加唯一约束
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'order_books_unique' AND conrelid = 'order_books'::regclass
                ) THEN
                    ALTER TABLE order_books ADD CONSTRAINT order_books_unique UNIQUE (time, symbol);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_order_books_symbol_time
             ON order_books (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("订单簿表结构初始化完成");
        Ok(())
    }

    /// 插入单条订单簿数据
    pub async fn insert(&self, request: CreateOrderBookRequest) -> Result<OrderBookEntity> {
        let bids_json = serde_json::to_value(&request.bids)
            .map_err(|e| RepositoryError::serialization(e.to_string()))?;
        let asks_json = serde_json::to_value(&request.asks)
            .map_err(|e| RepositoryError::serialization(e.to_string()))?;

        let order_book = sqlx::query_as::<_, OrderBookEntity>(
            r#"
            INSERT INTO order_books (time, symbol, last_update_id, bids, asks)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (time, symbol) DO UPDATE SET
                last_update_id = EXCLUDED.last_update_id,
                bids = EXCLUDED.bids,
                asks = EXCLUDED.asks
            RETURNING time, symbol, last_update_id, bids, asks
            "#,
        )
        .bind(&request.timestamp)
        .bind(&request.symbol)
        .bind(&request.last_update_id)
        .bind(&bids_json)
        .bind(&asks_json)
        .fetch_one(&self.pool)
        .await?;

        Ok(order_book)
    }

    /// 批量插入订单簿数据
    pub async fn batch_insert(&self, requests: Vec<CreateOrderBookRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        // 预处理JSON数据，避免生命周期问题
        let json_data: Vec<(DateTime<Utc>, String, i64, serde_json::Value, serde_json::Value)> = requests
            .iter()
            .map(|request| {
                let bids_json = serde_json::to_value(&request.bids).unwrap_or(serde_json::json!([]));
                let asks_json = serde_json::to_value(&request.asks).unwrap_or(serde_json::json!([]));
                (request.timestamp, request.symbol.clone(), request.last_update_id, bids_json, asks_json)
            })
            .collect();

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO order_books (time, symbol, last_update_id, bids, asks) "
        );

        query_builder.push_values(json_data.iter(), |mut b, (timestamp, symbol, last_update_id, bids_json, asks_json)| {
            b.push_bind(timestamp)
                .push_bind(symbol)
                .push_bind(last_update_id)
                .push_bind(bids_json)
                .push_bind(asks_json);
        });

        // 添加 ON CONFLICT 子句处理重复数据
        query_builder.push(
            " ON CONFLICT (time, symbol) DO UPDATE SET \
             last_update_id = EXCLUDED.last_update_id, \
             bids = EXCLUDED.bids, \
             asks = EXCLUDED.asks"
        );

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询订单簿数据
    pub async fn query(&self, params: OrderBookQuery) -> Result<Vec<OrderBookEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT time, symbol, last_update_id, bids, asks FROM order_books WHERE symbol = "
        );
        query.push_bind(&params.symbol);
        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        query.push(" ORDER BY time ASC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let order_books: Vec<OrderBookEntity> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(order_books)
    }

    /// 获取最新订单簿
    pub async fn get_latest(&self, symbol: &str) -> Result<Option<OrderBookEntity>> {
        let order_book = sqlx::query_as::<_, OrderBookEntity>(
            "SELECT time, symbol, last_update_id, bids, asks FROM order_books WHERE symbol = $1 ORDER BY time DESC LIMIT 1"
        )
        .bind(symbol)
        .fetch_optional(&self.pool)
        .await?;

        Ok(order_book)
    }

    /// 获取订单簿快照（提取关键信息）
    pub async fn get_snapshots(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> Result<Vec<OrderBookSnapshot>> {
        let mut query = sqlx::QueryBuilder::new(
            r#"
            SELECT 
                time,
                symbol,
                CASE 
                    WHEN jsonb_array_length(bids) > 0 THEN
                        (bids->0->>0)::decimal
                    ELSE NULL
                END as best_bid,
                CASE 
                    WHEN jsonb_array_length(asks) > 0 THEN
                        (asks->0->>0)::decimal
                    ELSE NULL
                END as best_ask,
                CASE 
                    WHEN jsonb_array_length(bids) > 0 AND jsonb_array_length(asks) > 0 THEN
                        (asks->0->>0)::decimal - (bids->0->>0)::decimal
                    ELSE NULL
                END as spread
            FROM order_books 
            WHERE symbol = 
            "#
        );
        query.push_bind(symbol);
        query.push(" AND time >= ").push_bind(start_time);
        query.push(" AND time <= ").push_bind(end_time);
        query.push(" ORDER BY time ASC");

        if let Some(limit) = limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let rows = query.build().fetch_all(&self.pool).await?;

        let mut snapshots = Vec::new();
        for row in rows {
            let timestamp: DateTime<Utc> = row.get("time");
            let symbol: String = row.get("symbol");
            let best_bid: Option<Decimal> = row.get("best_bid");
            let best_ask: Option<Decimal> = row.get("best_ask");
            let spread: Option<Decimal> = row.get("spread");

            snapshots.push(OrderBookSnapshot {
                symbol,
                timestamp,
                best_bid,
                best_ask,
                spread,
                bid_depth: Decimal::from(0), // 可以后续计算
                ask_depth: Decimal::from(0), // 可以后续计算
            });
        }

        Ok(snapshots)
    }

    /// 获取订单簿统计
    pub async fn get_stats(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<OrderBookStats> {
        let stats = sqlx::query_as::<_, OrderBookStats>(
            r#"
            SELECT 
                $1 as symbol,
                AVG(CASE 
                    WHEN jsonb_array_length(bids) > 0 THEN
                        (bids->0->>0)::decimal
                    ELSE NULL
                END) as avg_bid_price,
                AVG(CASE 
                    WHEN jsonb_array_length(asks) > 0 THEN
                        (asks->0->>0)::decimal
                    ELSE NULL
                END) as avg_ask_price,
                AVG(CASE 
                    WHEN jsonb_array_length(bids) > 0 AND jsonb_array_length(asks) > 0 THEN
                        (asks->0->>0)::decimal - (bids->0->>0)::decimal
                    ELSE NULL
                END) as avg_spread,
                AVG(CASE 
                    WHEN jsonb_array_length(bids) > 0 AND jsonb_array_length(asks) > 0 
                         AND (bids->0->>0)::decimal > 0 THEN
                        ((asks->0->>0)::decimal - (bids->0->>0)::decimal) / (bids->0->>0)::decimal * 100
                    ELSE NULL
                END) as avg_spread_percent,
                COUNT(*) as count
            FROM order_books 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取买卖价差历史
    pub async fn get_spread_history(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        interval: &str, // 例如: '5 minutes', '1 hour'
    ) -> Result<Vec<(DateTime<Utc>, Option<Decimal>)>> {
        let query = format!(
            r#"
            SELECT 
                time_bucket('{}', time) as time_bucket,
                AVG(CASE 
                    WHEN jsonb_array_length(bids) > 0 AND jsonb_array_length(asks) > 0 THEN
                        (asks->0->>0)::decimal - (bids->0->>0)::decimal
                    ELSE NULL
                END) as avg_spread
            FROM order_books 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            GROUP BY time_bucket
            ORDER BY time_bucket ASC
            "#,
            interval
        );

        let rows = sqlx::query(&query)
            .bind(symbol)
            .bind(start_time)
            .bind(end_time)
            .fetch_all(&self.pool)
            .await?;

        let mut spreads = Vec::new();
        for row in rows {
            let time_bucket: DateTime<Utc> = row.get("time_bucket");
            let avg_spread: Option<Decimal> = row.get("avg_spread");
            spreads.push((time_bucket, avg_spread));
        }

        Ok(spreads)
    }

    /// 删除旧数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM order_books WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 获取所有活跃交易对
    pub async fn get_active_symbols(&self, within_duration: chrono::Duration) -> Result<Vec<String>> {
        let cutoff_time = Utc::now() - within_duration;

        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT symbol FROM order_books WHERE time >= $1 ORDER BY symbol"
        )
        .bind(cutoff_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(symbols)
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&self.pool).await?;
        Ok(())
    }
} 