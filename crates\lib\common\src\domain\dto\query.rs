use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

/// K线查询DTO
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct KlineQueryDto {
    /// 交易对符号
    pub symbol: String,
    /// 时间间隔
    pub interval: String,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 限制数量
    pub limit: Option<i64>,
}

/// 价格查询DTO
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TickerQueryDto {
    /// 交易对符号（可选，为空时查询所有）
    pub symbol: Option<String>,
}

/// 深度查询DTO
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct OrderBookQueryDto {
    /// 交易对符号
    pub symbol: String,
    /// 深度档位限制
    pub limit: Option<i32>,
}

/// 24小时统计查询DTO
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct StatsQueryDto {
    /// 交易对符号（可选，为空时查询所有）
    pub symbol: Option<String>,
}

/// 交易历史查询DTO
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TradeHistoryQueryDto {
    /// 交易对符号
    pub symbol: String,
    /// 起始交易ID
    pub from_id: Option<i64>,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 限制数量
    pub limit: Option<i64>,
} 