# 交易所统一接口 (Exchange Unified Interface)

## 📋 项目概述

这个模块提供了一个统一的交易所数据接入接口，支持多家数字货币交易所的数据获取。通过统一的 `ExchangeClient` trait，实现了不同交易所API的标准化访问。

## 🏗️ 架构设计

### 核心组件

1. **ExchangeClient Trait** - 统一的交易所客户端接口
2. **ExchangeClientFactory** - 交易所客户端工厂
3. **UniversalKline** - 通用K线数据结构
4. **具体实现** - 各交易所的具体实现

### 目录结构

```
crates/exchange/
├── src/
│   ├── lib.rs              # 核心接口定义
│   └── binance/            # Binance交易所实现
│       ├── mod.rs
│       ├── client.rs       # Binance客户端实现
│       └── types.rs        # Binance特定数据类型
└── Cargo.toml
```

## 🔧 核心接口

### ExchangeClient Trait

```rust
#[async_trait]
pub trait ExchangeClient: Send + Sync {
    /// 获取交易所信息
    fn get_exchange_info(&self) -> &ExchangeInfo;
    
    /// 获取K线数据
    async fn get_klines(&self, query: KlineQuery) -> Result<Vec<UniversalKline>>;
    
    /// 测试连接
    async fn test_connection(&self) -> Result<()>;
    
    /// 获取服务器时间
    async fn get_server_time(&self) -> Result<DateTime<Utc>>;
    
    /// 获取交易对信息
    async fn get_symbol_info(&self, symbol: &str) -> Result<SymbolInfo>;
}
```

### 通用数据结构

#### UniversalKline - 统一K线数据
```rust
pub struct UniversalKline {
    pub open_time: i64,
    pub open_price: String,
    pub high_price: String,
    pub low_price: String,
    pub close_price: String,
    pub volume: String,
    pub close_time: i64,
    pub quote_asset_volume: String,
    pub number_of_trades: i32,
    pub taker_buy_base_asset_volume: String,
    pub taker_buy_quote_asset_volume: String,
}
```

#### KlineQuery - 查询参数
```rust
pub struct KlineQuery {
    pub symbol: String,
    pub interval: String,
    pub start_time: Option<i64>,
    pub end_time: Option<i64>,
    pub limit: Option<i32>,
}
```

## 📖 使用方法

### 1. 创建交易所客户端

```rust
use exchange::{ExchangeClientFactory, KlineQuery};

// 创建Binance客户端
let client = ExchangeClientFactory::create_binance_client();

// 或者根据名称创建
let client = ExchangeClientFactory::create_client("binance")?;
```

### 2. 获取K线数据

```rust
let query = KlineQuery {
    symbol: "BTCUSDT".to_string(),
    interval: "1m".to_string(),
    start_time: Some(start_timestamp),
    end_time: Some(end_timestamp),
    limit: Some(1000),
};

let klines = client.get_klines(query).await?;
```

### 3. 测试连接

```rust
client.test_connection().await?;
```

## 🔌 支持的交易所

### ✅ 已实现

- **Binance** (`binance::BinanceClient`)
  - 支持K线数据获取
  - 支持连接测试
  - 支持服务器时间获取
  - 支持交易对信息查询

### 🔄 计划实现

- **OKX** - 待实现
- **Bybit** - 待实现  
- **Huobi** - 待实现

## 🚀 扩展新交易所

### 1. 创建新的交易所模块

```bash
mkdir src/exchange_name
```

### 2. 实现ExchangeClient trait

```rust
// src/exchange_name/client.rs
use async_trait::async_trait;
use crate::{ExchangeClient, ExchangeInfo, KlineQuery, UniversalKline, SymbolInfo};

pub struct ExchangeNameClient {
    // 客户端字段
}

#[async_trait]
impl ExchangeClient for ExchangeNameClient {
    // 实现所有必需的方法
}
```

### 3. 更新工厂方法

```rust
// src/lib.rs
impl ExchangeClientFactory {
    pub fn create_exchange_name_client() -> Box<dyn ExchangeClient> {
        Box::new(exchange_name::ExchangeNameClient::new())
    }
    
    pub fn create_client(exchange_name: &str) -> Result<Box<dyn ExchangeClient>> {
        match exchange_name.to_lowercase().as_str() {
            "binance" => Ok(Self::create_binance_client()),
            "exchange_name" => Ok(Self::create_exchange_name_client()),
            _ => Err(anyhow::anyhow!("Unsupported exchange: {}", exchange_name)),
        }
    }
}
```

## 🔧 技术特性

### 异步支持
- 所有API调用都是异步的
- 支持高并发数据获取

### 错误处理
- 统一的错误处理机制
- 详细的错误信息和日志

### 类型安全
- 强类型系统，减少运行时错误
- 统一的数据格式转换

### 扩展性
- 插件化架构，易于添加新交易所
- 标准化接口，便于维护

## 🏃‍♂️ 性能考虑

### 限流处理
- 各交易所都有自己的限流策略
- 建议在上层实现统一的限流管理

### 数据缓存
- 支持在上层实现数据缓存
- 减少不必要的API调用

### 连接池
- HTTP客户端内置连接复用
- 支持长连接和keep-alive

## 📊 数据维护集成

此模块已集成到数据维护服务中：

```rust
// 在 data_maintenance.rs 中使用
use exchange::{ExchangeClient, ExchangeClientFactory, KlineQuery};

let exchange_client = ExchangeClientFactory::create_binance_client();
let maintenance_service = DataMaintenanceService::new(
    market_data_service,
    &db_pool,
    exchange_client,
);
```

## 🔄 版本历史

- **v0.1.0** - 初始版本，支持Binance接口
- 统一接口架构设计
- 完整的Binance API实现
- 工厂模式支持

## 🤝 贡献指南

1. 遵循现有的代码风格和架构
2. 添加新交易所时需要完整实现ExchangeClient trait
3. 提供充分的测试覆盖
4. 更新文档和示例代码

## 📄 许可证

Apache-2.0 