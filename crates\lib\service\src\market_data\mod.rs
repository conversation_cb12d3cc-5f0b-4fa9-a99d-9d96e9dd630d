// 市场数据服务 - 重构版本
//
// 分离CRUD操作和数据维护功能，实现清晰的职责分离

pub mod crud_service;      // 纯CRUD操作服务
pub mod maintenance_service; // 数据维护服务
pub mod cache_sync_service;  // 缓存同步服务
pub mod gap_detector;      // 优化的缺口检测器
pub mod storage;
pub mod config;
pub mod enhanced_config;   // 增强的配置管理
pub mod shared_state;      // 共享状态管理
pub mod global_state;      // 全局状态管理

// 重新导出核心类型
pub use crud_service::MarketDataCrudService;
pub use maintenance_service::{MaintenanceService, MaintenanceStatus};
pub use cache_sync_service::CacheSyncService;
pub use storage::TimescaleStorageManager;
pub use config::MaintenanceConfig;
pub use enhanced_config::{EnhancedMaintenanceConfig, ConfigManager, Environment};
pub use shared_state::{ServiceState, ServiceStateManager};
pub use global_state::{init_global_state_manager, get_global_state_manager};

// 从repository重新导出CacheStats
pub use repository::CacheStats;

// 保持向后兼容的别名
pub use crud_service::MarketDataCrudService as MarketDataService;
pub use maintenance_service::MaintenanceService as MarketDataServiceImpl;

// 测试模块
#[cfg(test)]
mod tests;
#[cfg(test)]
mod config_tests;
#[cfg(test)]
mod integration_tests;
#[cfg(test)]
mod benchmarks;