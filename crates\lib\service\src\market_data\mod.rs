// 市场数据服务模块
// 提供全量数据检查、Redis缓存和增量更新功能

pub mod data_integrity_service;
pub mod redis_cache_service;
pub mod incremental_update_service;
pub mod market_data_manager;

// 重新导出主要服务
pub use data_integrity_service::DataIntegrityService;
pub use redis_cache_service::RedisCacheService;
pub use incremental_update_service::IncrementalUpdateService;
pub use market_data_manager::MarketDataManager;

use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 支持的K线时间间隔
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum KlineInterval {
    /// 1分钟
    OneMinute,
    /// 3分钟
    ThreeMinutes,
    /// 5分钟
    FiveMinutes,
    /// 15分钟
    FifteenMinutes,
    /// 30分钟
    ThirtyMinutes,
    /// 1小时
    OneHour,
    /// 2小时
    TwoHours,
    /// 4小时
    FourHours,
    /// 6小时
    SixHours,
    /// 8小时
    EightHours,
    /// 12小时
    TwelveHours,
    /// 1天
    OneDay,
    /// 3天
    ThreeDays,
    /// 1周
    OneWeek,
    /// 1月
    OneMonth,
}

impl KlineInterval {
    /// 获取所有支持的时间间隔
    pub fn all() -> Vec<KlineInterval> {
        vec![
            KlineInterval::OneMinute,
            KlineInterval::ThreeMinutes,
            KlineInterval::FiveMinutes,
            KlineInterval::FifteenMinutes,
            KlineInterval::ThirtyMinutes,
            KlineInterval::OneHour,
            KlineInterval::TwoHours,
            KlineInterval::FourHours,
            KlineInterval::SixHours,
            KlineInterval::EightHours,
            KlineInterval::TwelveHours,
            KlineInterval::OneDay,
            KlineInterval::ThreeDays,
            KlineInterval::OneWeek,
            KlineInterval::OneMonth,
        ]
    }

    /// 转换为Binance API的字符串格式
    pub fn to_binance_string(&self) -> &'static str {
        match self {
            KlineInterval::OneMinute => "1m",
            KlineInterval::ThreeMinutes => "3m",
            KlineInterval::FiveMinutes => "5m",
            KlineInterval::FifteenMinutes => "15m",
            KlineInterval::ThirtyMinutes => "30m",
            KlineInterval::OneHour => "1h",
            KlineInterval::TwoHours => "2h",
            KlineInterval::FourHours => "4h",
            KlineInterval::SixHours => "6h",
            KlineInterval::EightHours => "8h",
            KlineInterval::TwelveHours => "12h",
            KlineInterval::OneDay => "1d",
            KlineInterval::ThreeDays => "3d",
            KlineInterval::OneWeek => "1w",
            KlineInterval::OneMonth => "1M",
        }
    }

    /// 转换为 TimescaleDB time_bucket 函数的 bucket_width 参数
    pub fn to_timescale_bucket_string(&self) -> &'static str {
        match self {
            KlineInterval::OneMinute => "1 minute",
            KlineInterval::ThreeMinutes => "3 minutes",
            KlineInterval::FiveMinutes => "5 minutes",
            KlineInterval::FifteenMinutes => "15 minutes",
            KlineInterval::ThirtyMinutes => "30 minutes",
            KlineInterval::OneHour => "1 hour",
            KlineInterval::TwoHours => "2 hours",
            KlineInterval::FourHours => "4 hours",
            KlineInterval::SixHours => "6 hours",
            KlineInterval::EightHours => "8 hours",
            KlineInterval::TwelveHours => "12 hours",
            KlineInterval::OneDay => "1 day",
            KlineInterval::ThreeDays => "3 days",
            KlineInterval::OneWeek => "1 week",
            KlineInterval::OneMonth => "1 month",
        }
    }

    /// 从字符串解析时间间隔
    pub fn from_string(s: &str) -> Option<KlineInterval> {
        match s {
            "1m" => Some(KlineInterval::OneMinute),
            "3m" => Some(KlineInterval::ThreeMinutes),
            "5m" => Some(KlineInterval::FiveMinutes),
            "15m" => Some(KlineInterval::FifteenMinutes),
            "30m" => Some(KlineInterval::ThirtyMinutes),
            "1h" => Some(KlineInterval::OneHour),
            "2h" => Some(KlineInterval::TwoHours),
            "4h" => Some(KlineInterval::FourHours),
            "6h" => Some(KlineInterval::SixHours),
            "8h" => Some(KlineInterval::EightHours),
            "12h" => Some(KlineInterval::TwelveHours),
            "1d" => Some(KlineInterval::OneDay),
            "3d" => Some(KlineInterval::ThreeDays),
            "1w" => Some(KlineInterval::OneWeek),
            "1M" => Some(KlineInterval::OneMonth),
            _ => None,
        }
    }

    /// 获取时间间隔的分钟数
    pub fn to_minutes(&self) -> i64 {
        match self {
            KlineInterval::OneMinute => 1,
            KlineInterval::ThreeMinutes => 3,
            KlineInterval::FiveMinutes => 5,
            KlineInterval::FifteenMinutes => 15,
            KlineInterval::ThirtyMinutes => 30,
            KlineInterval::OneHour => 60,
            KlineInterval::TwoHours => 120,
            KlineInterval::FourHours => 240,
            KlineInterval::SixHours => 360,
            KlineInterval::EightHours => 480,
            KlineInterval::TwelveHours => 720,
            KlineInterval::OneDay => 1440,
            KlineInterval::ThreeDays => 4320,
            KlineInterval::OneWeek => 10080,
            KlineInterval::OneMonth => 43200, // 30天
        }
    }
}

impl std::fmt::Display for KlineInterval {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.to_binance_string())
    }
}

/// 数据完整性检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrityCheckResult {
    /// 交易对
    pub symbol: String,
    /// 时间间隔
    pub interval: KlineInterval,
    /// 检查的开始时间
    pub start_time: DateTime<Utc>,
    /// 检查的结束时间
    pub end_time: DateTime<Utc>,
    /// 缺失的时间段
    pub missing_periods: Vec<(DateTime<Utc>, DateTime<Utc>)>,
    /// 总的预期数据点数量
    pub expected_count: i64,
    /// 实际存在的数据点数量
    pub actual_count: i64,
    /// 完整性百分比
    pub completeness_percentage: f64,
}

/// 缓存状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStatus {
    /// 交易对
    pub symbol: String,
    /// 缓存的天数
    pub cached_days: i32,
    /// 最早的缓存时间
    pub earliest_cached: Option<DateTime<Utc>>,
    /// 最新的缓存时间
    pub latest_cached: Option<DateTime<Utc>>,
    /// 缓存大小（条目数）
    pub cache_size: i64,
}

/// 增量更新结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IncrementalUpdateResult {
    /// 交易对
    pub symbol: String,
    /// 更新的时间间隔
    pub intervals: Vec<KlineInterval>,
    /// 新增的数据点数量
    pub new_data_points: i64,
    /// 更新的数据点数量
    pub updated_data_points: i64,
    /// 更新时间
    pub update_time: DateTime<Utc>,
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果有）
    pub error_message: Option<String>,
}

/// 服务错误类型
#[derive(Debug, thiserror::Error)]
pub enum MarketDataServiceError {
    #[error("数据库错误: {0}")]
    Database(#[from] repository::RepositoryError),
    
    #[error("缓存错误: {0}")]
    Cache(String),
    
    #[error("API错误: {0}")]
    Api(String),
    
    #[error("配置错误: {0}")]
    Configuration(String),
    
    #[error("数据验证错误: {0}")]
    Validation(String),
    
    #[error("网络错误: {0}")]
    Network(String),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("数据转换错误: {0}")]
    DataConversion(String),
}

pub type Result<T> = std::result::Result<T, MarketDataServiceError>; 