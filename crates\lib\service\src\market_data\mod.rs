pub mod manager;
mod types;
mod checker;
mod fetcher;

// 重构的模块 (v2.0)
pub mod kline_maintenance_service_v2;

pub use types::{
    CacheStatus, IncrementalUpdateResult, IntegrityCheckResult, KlineInterval,
    MarketDataServiceError, Result,
};
pub use manager::{MarketDataManager, MarketDataManagerConfig};

// 重新导出v2.0模块
pub use kline_maintenance_service_v2::{KlineMaintenanceServiceV2, MaintenanceConfig, ServiceStatus};
