// 增量更新服务
// 负责定期从交易所API拉取最新数据，更新数据库和缓存

use chrono::{DateTime, Utc, Duration};
use repository::{
    timescale::MarketDataRepository,
    connection::timescale::TimescalePoolManager,
};
use exchange::binance::BinanceClient;
use common::domain::dto::KlineDto;
use crate::market_data::{
    KlineInterval, IncrementalUpdateResult, Result, MarketDataServiceError,
    RedisCacheService,
};
use std::collections::HashMap;
use tokio::time::{interval, Interval};
use exchange::ExchangeClient;

/// 增量更新服务
#[derive(Clone)]
pub struct IncrementalUpdateService {
    /// TimescaleDB数据仓库
    market_data_repo: MarketDataRepository,
    /// Redis缓存服务
    cache_service: RedisCacheService,
    /// Binance客户端
    binance_client: BinanceClient,
    /// 更新间隔（分钟）
    update_interval_minutes: u64,
    /// 活跃交易对列表
    active_symbols: Vec<String>,
}

impl IncrementalUpdateService {
    /// 创建新的增量更新服务
    pub async fn new(
        timescale_pool: TimescalePoolManager,
        cache_service: RedisCacheService,
        binance_client: BinanceClient,
        update_interval_minutes: u64,
    ) -> Result<Self> {
        let pool = timescale_pool.pool().clone();
        let market_data_repo = MarketDataRepository::new(pool);

        Ok(Self {
            market_data_repo,
            cache_service,
            binance_client,
            update_interval_minutes,
            active_symbols: Vec::new(),
        })
    }

    /// 设置活跃交易对列表
    pub fn set_active_symbols(&mut self, symbols: Vec<String>) {
        self.active_symbols = symbols;
        log::info!("设置活跃交易对列表，共 {} 个", self.active_symbols.len());
    }

    /// 启动增量更新服务
    /// 这是一个长期运行的任务，会定期拉取最新数据
    pub async fn start_update_loop(&self) -> Result<()> {
        log::info!("启动增量更新服务，更新间隔: {} 分钟", self.update_interval_minutes);
        
        let mut interval = interval(tokio::time::Duration::from_secs(
            self.update_interval_minutes * 60
        ));

        loop {
            interval.tick().await;
            
            log::info!("开始执行增量更新...");
            
            match self.perform_incremental_update().await {
                Ok(results) => {
                    let total_new_points: i64 = results.iter().map(|r| r.new_data_points).sum();
                    let total_updated_points: i64 = results.iter().map(|r| r.updated_data_points).sum();
                    let successful_updates = results.iter().filter(|r| r.success).count();
                    
                    log::info!(
                        "增量更新完成: {} 个交易对成功更新，新增 {} 个数据点，更新 {} 个数据点",
                        successful_updates,
                        total_new_points,
                        total_updated_points
                    );
                }
                Err(e) => {
                    log::error!("增量更新失败: {}", e);
                }
            }
        }
    }

    /// 执行一次增量更新
    pub async fn perform_incremental_update(&self) -> Result<Vec<IncrementalUpdateResult>> {
        if self.active_symbols.is_empty() {
            log::warn!("没有配置活跃交易对，跳过增量更新");
            return Ok(Vec::new());
        }

        let mut results = Vec::new();
        let end_time = Utc::now();
        
        // 获取最近的更新时间窗口（比如最近1小时的数据）
        let start_time = end_time - Duration::hours(1);

        for symbol in &self.active_symbols {
            log::debug!("更新交易对: {}", symbol);
            
            let result = self.update_symbol_data(symbol, start_time, end_time).await;
            results.push(result);
        }

        Ok(results)
    }

    /// 更新单个交易对的数据
    async fn update_symbol_data(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> IncrementalUpdateResult {
        let update_time = Utc::now();
        let mut new_data_points = 0i64;
        let mut updated_data_points = 0i64;
        let mut intervals_updated = Vec::new();

        // 只更新1分钟级别的数据（因为Redis只缓存1分钟数据）
        let interval = KlineInterval::OneMinute;
        
        match self.fetch_and_update_klines(symbol, &interval, start_time, end_time).await {
            Ok((new_count, updated_count)) => {
                new_data_points += new_count;
                updated_data_points += updated_count;
                intervals_updated.push(interval.clone());
                
                log::debug!(
                    "{} {} 更新完成: 新增 {} 个，更新 {} 个数据点",
                    symbol, interval, new_count, updated_count
                );
            }
            Err(e) => {
                log::error!("更新 {} {} 失败: {}", symbol, interval, e);
                return IncrementalUpdateResult {
                    symbol: symbol.to_string(),
                    intervals: Vec::new(),
                    new_data_points: 0,
                    updated_data_points: 0,
                    update_time,
                    success: false,
                    error_message: Some(e.to_string()),
                };
            }
        }

        IncrementalUpdateResult {
            symbol: symbol.to_string(),
            intervals: intervals_updated,
            new_data_points,
            updated_data_points,
            update_time,
            success: true,
            error_message: None,
        }
    }

    /// 获取并更新K线数据
    async fn fetch_and_update_klines(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<(i64, i64)> {
        // 从Binance API获取最新数据
        let new_klines = self.fetch_latest_klines_from_binance(
            symbol, 
            interval, 
            start_time, 
            end_time
        ).await?;

        if new_klines.is_empty() {
            return Ok((0, 0));
        }

        // 更新数据库
        let (new_count, updated_count) = self.update_database_with_klines(
            symbol, 
            interval, 
            &new_klines
        ).await?;

        // 更新Redis缓存（只有1分钟数据才缓存）
        if interval == &KlineInterval::OneMinute {
            if let Err(e) = self.cache_service.update_cache_with_new_klines(symbol, &new_klines).await {
                log::warn!("更新Redis缓存失败: {}", e);
                // 缓存更新失败不影响整体流程
            }
        }

        Ok((new_count, updated_count))
    }

    /// 从Binance API获取最新K线数据
    async fn fetch_latest_klines_from_binance(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<KlineDto>> {
        let interval_str = interval.to_binance_string();
        
        log::debug!(
            "从Binance获取 {} {} 的数据，时间范围: {} 到 {}",
            symbol, interval_str, start_time, end_time
        );

        // 调用Binance API
        let query = exchange::KlineQuery {
            symbol: symbol.to_string(),
            interval: interval_str.to_string(),
            start_time: Some(start_time.timestamp_millis()),
            end_time: Some(end_time.timestamp_millis()),
            limit: Some(1000), // API限制
        };
        
        let klines = self.binance_client
            .get_klines(query)
            .await
            .map_err(|e| MarketDataServiceError::Api(format!("Binance API错误: {}", e)))?;

        // 转换为KlineDto
        let kline_dtos: Vec<KlineDto> = klines
            .into_iter()
            .map(|kline| KlineDto {
                time: DateTime::from_timestamp_millis(kline.open_time).unwrap_or_else(|| Utc::now()),
                symbol: symbol.to_string(),
                interval: interval_str.to_string(),
                open_time: DateTime::from_timestamp_millis(kline.open_time).unwrap_or_else(|| Utc::now()),
                close_time: DateTime::from_timestamp_millis(kline.close_time).unwrap_or_else(|| Utc::now()),
                open_price: kline.open_price.parse().unwrap(),
                high_price: kline.high_price.parse().unwrap(),
                low_price: kline.low_price.parse().unwrap(),
                close_price: kline.close_price.parse().unwrap(),
                volume: kline.volume.parse().unwrap(),
                quote_asset_volume: kline.quote_asset_volume.parse().unwrap(),
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume.parse().unwrap(),
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume.parse().unwrap(),
            })
            .collect();

        log::debug!("从Binance获取到 {} 条K线数据", kline_dtos.len());
        Ok(kline_dtos)
    }

    /// 使用新K线数据更新数据库
    async fn update_database_with_klines(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        klines: &[KlineDto],
    ) -> Result<(i64, i64)> {
        if klines.is_empty() {
            return Ok((0, 0));
        }

        // 检查哪些数据是新的，哪些是更新的
        let mut new_klines = Vec::new();
        let mut updated_klines = Vec::new();

        for kline in klines {
            // 检查数据库中是否已存在相同时间的数据
            match self.market_data_repo.klines
                .get_klines(
                    symbol, 
                    &interval.to_binance_string(), 
                    kline.time, 
                    kline.time + Duration::minutes(1), 
                    Some(1)
                )
                .await {
                Ok(existing) => {
                    if existing.is_empty() {
                        new_klines.push(kline);
                    } else {
                        updated_klines.push(kline);
                    }
                }
                Err(_) => {
                    // 查询失败，假设是新数据
                    new_klines.push(kline);
                }
            }
        }

        let mut total_new = 0i64;
        let mut total_updated = 0i64;

        // 插入新数据
        if !new_klines.is_empty() {
            let requests: Vec<_> = new_klines
                .into_iter()
                .map(|kline| self.kline_dto_to_create_request(kline.clone(), interval))
                .collect();

            match self.market_data_repo.klines.batch_insert(requests).await {
                Ok(count) => {
                    total_new = count as i64;
                    log::debug!("插入 {} 条新K线数据", total_new);
                }
                Err(e) => {
                    log::error!("插入新K线数据失败: {}", e);
                    return Err(MarketDataServiceError::Database(e));
                }
            }
        }

        // 更新现有数据（使用UPSERT语义）
        if !updated_klines.is_empty() {
            let requests: Vec<_> = updated_klines
                .into_iter()
                .map(|kline| self.kline_dto_to_create_request(kline.clone(), interval))
                .collect();

            match self.market_data_repo.klines.batch_insert(requests).await {
                Ok(count) => {
                    total_updated = count as i64;
                    log::debug!("更新 {} 条K线数据", total_updated);
                }
                Err(e) => {
                    log::error!("更新K线数据失败: {}", e);
                    return Err(MarketDataServiceError::Database(e));
                }
            }
        }

        Ok((total_new, total_updated))
    }

    /// 将KlineDto转换为CreateMarketDataRequest
    fn kline_dto_to_create_request(
        &self, 
        kline: KlineDto, 
        interval: &KlineInterval
    ) -> repository::timescale::CreateMarketDataRequest {
        repository::timescale::CreateMarketDataRequest {
            symbol: kline.symbol,
            timestamp: kline.time,
            open: kline.open_price,
            high: kline.high_price,
            low: kline.low_price,
            close: kline.close_price,
            volume: kline.volume,
            interval_type: interval.to_binance_string().to_string(),
            quote_asset_volume: kline.quote_asset_volume,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            close_time: kline.close_time,
        }
    }

    /// 手动触发一次更新（用于测试或立即更新）
    pub async fn trigger_immediate_update(&self) -> Result<Vec<IncrementalUpdateResult>> {
        log::info!("手动触发增量更新");
        self.perform_incremental_update().await
    }

    /// 获取更新统计信息
    pub async fn get_update_statistics(&self) -> Result<HashMap<String, i64>> {
        let mut stats = HashMap::new();
        
        // 这里可以添加更多统计信息
        stats.insert("active_symbols_count".to_string(), self.active_symbols.len() as i64);
        stats.insert("update_interval_minutes".to_string(), self.update_interval_minutes as i64);
        
        Ok(stats)
    }

    /// 停止更新服务（优雅关闭）
    pub async fn stop(&self) -> Result<()> {
        log::info!("停止增量更新服务");
        // 这里可以添加清理逻辑
        Ok(())
    }
} 