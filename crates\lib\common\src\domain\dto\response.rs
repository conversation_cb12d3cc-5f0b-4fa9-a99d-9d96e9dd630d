use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use chrono::{DateTime, Utc};

/// 通用成功响应格式
#[derive(Debug, Serialize, ToSchema)]
pub struct ApiResponse<T> {
    /// 操作是否成功
    pub success: bool,
    /// 响应数据
    pub data: T,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data,
        }
    }
}

/// 通用错误响应格式
#[derive(Debug, Serialize, ToSchema)]
pub struct ApiErrorResponse {
    /// 操作是否成功
    pub success: bool,
    /// 错误消息
    pub message: String,
}

impl ApiErrorResponse {
    /// 创建错误响应
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            message,
        }
    }
}

/// 分页数据结构
#[derive(Debug, Serialize, ToSchema)]
pub struct PaginationData<T> {
    /// 列表数据
    pub list: Vec<T>,
    /// 总条目数
    pub total: Option<u64>,
    /// 每页显示条目个数
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    /// 当前页数
    #[serde(rename = "currentPage")]
    pub current_page: Option<u32>,
}

impl<T> PaginationData<T> {
    /// 创建分页数据
    pub fn new(list: Vec<T>, total: Option<u64>, page_size: Option<u32>, current_page: Option<u32>) -> Self {
        Self {
            list,
            total,
            page_size,
            current_page,
        }
    }
}

/// 健康检查响应数据
#[derive(Debug, Serialize, ToSchema)]
pub struct HealthData {
    /// 服务状态
    pub status: String,
    /// 时间戳
    pub timestamp: i64,
    /// 版本信息
    pub version: Option<String>,
    /// 环境信息
    pub environment: Option<String>,
}

impl Default for HealthData {
    fn default() -> Self {
        Self {
            status: "healthy".to_string(),
            timestamp: Utc::now().timestamp(),
            version: Some(env!("CARGO_PKG_VERSION").to_string()),
            environment: std::env::var("ENVIRONMENT").ok(),
        }
    }
}

/// JWT用户信息响应数据
#[derive(Debug, Serialize, ToSchema)]
pub struct JwtUserInfo {
    /// 用户ID
    pub user_id: String,
    /// 邮箱
    pub email: String,
    /// 昵称
    pub nickname: String,
    /// 角色列表
    pub roles: Vec<String>,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 令牌颁发时间
    pub issued_at: i64,
    /// 令牌过期时间
    pub expires_at: i64,
}

/// 受保护资源访问数据
#[derive(Debug, Serialize, ToSchema)]
pub struct ProtectedResourceData {
    /// 访问者用户ID
    pub accessed_by: String,
    /// 访问时间
    pub access_time: i64,
    /// 资源信息
    pub resource_info: String,
    /// 操作类型
    pub operation: String,
}

/// 操作结果数据
#[derive(Debug, Serialize, ToSchema)]
pub struct OperationResult {
    /// 操作类型
    pub operation: String,
    /// 操作者用户ID
    pub user_id: String,
    /// 操作者昵称
    pub username: String,
    /// 用户权限
    pub permissions: Vec<String>,
    /// 操作时间
    pub timestamp: i64,
    /// 操作状态
    pub status: String,
}

/// 系统统计数据
#[derive(Debug, Serialize, ToSchema)]
pub struct SystemStats {
    /// 总用户数
    pub total_users: u64,
    /// 活跃会话数
    pub active_sessions: u64,
    /// 系统健康状态
    pub system_health: String,
    /// 系统负载
    pub system_load: Option<f64>,
    /// 内存使用率
    pub memory_usage: Option<f64>,
}

impl Default for SystemStats {
    fn default() -> Self {
        Self {
            total_users: 1000,
            active_sessions: 50,
            system_health: "excellent".to_string(),
            system_load: Some(0.3),
            memory_usage: Some(0.6),
        }
    }
} 