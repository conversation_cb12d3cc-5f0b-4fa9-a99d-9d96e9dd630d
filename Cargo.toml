[workspace]
members = [
  "crates/bin/user-server",
  "crates/bin/test-kline-v2",
  "crates/lib/nacos",
  "crates/lib/service",
  "crates/lib/repository",
  "crates/lib/common",
  "crates/lib/exchange",

]
resolver = "3"
[workspace.package]
authors = ["huang <<EMAIL>>"]
edition = "2024"
homepage = "https://home.risc-v.pro"
license = "Apache-2.0"
publish = false
repository = ""
version = "0.1.0"

[profile.release]
debug = true
opt-level = 3 # Optimize for size. "z"
panic = 'abort'
strip = true
lto = "thin"

[workspace.dependencies]
# Internal crates
common = { path = "crates/lib/common" }
service = { path = "crates/lib/service" }
repository = { path = "crates/lib/repository" }
nacos = { path = "crates/lib/nacos" }
exchange = { path = "crates/lib/exchange" }


log = "0.4.27"
log4rs = "1.3.0"
toml = "0.8.22"
once_cell = "1.21.3"
bigdecimal = { version = "0.4.8", features = ["std", "serde"] }
validator = { version = "0.20.0", features = ["derive"] }
tokio = { version = "1.45.0", features = ["full"] }
tokio-stream = { version = "0.1", features = ["sync"] }
redis = { version = "0.31.0",features = ["tokio-comp"]}
fred = { version = "9.4.0", features = ["enable-rustls"] }
uuid = {version ="1.17.0", features = ["v4", "serde"] }

# 序列化/反序列化
serde = { version = "1", features = ["derive"] }
serde_json = "1.0.140"
serde_yaml = "0.9"
dotenvy = "0.15.7"

# 异步与并发
futures = "0.3.31"
async-trait = "0.1.88"

# 错误处理
anyhow = "1.0.98"
thiserror = "2.0.8"

# 工具库
taos = { version = "0.12.3", default-features = false, features = ["ws"] }
rdkafka = "0.37.0"
dashmap = "7.0.0-rc2"
chrono = { version = "0.4", features = ["serde"] }
rs-snowflake = "0.6.0"
lazy_static = "1.5.0"

# 网络与API
reqwest = { version = "0.12.15", features = ["json"] }
tonic = { version = "0.13.1", features = ["transport"] }
tower-http = { version = "0.6.4", features = ["cors", "trace", "compression-full"] }
tower = { version = "0.5.2", features = ["limit", "load-shed", "timeout"] }
r2d2 = "0.8.10"
r2d2_redis = "0.14.0"


# 数据库
sqlx = { version = "0.8.6", features = [
  "runtime-tokio-rustls",
  "postgres",
  "migrate",
  "uuid",
  "chrono",
  "rust_decimal",
] }

# 其他
captcha = "1.0.0"
md5 = "0.7.0"
sysinfo = "0.35.0"
byte-unit = "5.1.6"
tracing = "0.1"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "json"] }
tempfile = "3.20.0"
nacos-sdk = { version = "0.5.0", features = ["naming"] }
structopt = "0.3.26"
clap = { version = "4.0", features = ["derive"] }
rand = "0.9.1"
rand_distr = "0.5.1"  # 提供正态分布支持
rust_decimal = { version = "1.36", features = ["serde", "db-postgres"] }
prost = "0.13.5"

# Web框架
axum = { version = "0.8.4", features = ["macros", "tracing"] }
axum-extra = { version = "0.9.3", features = ["typed-header"] }
headers = "0.4"

# OpenAPI文档
utoipa = { version = "5.3.1", features = ["axum_extras", "chrono", "uuid", "decimal"] }
utoipa-swagger-ui = { version = "9.0.2", features = ["axum"] }

# 性能优化
mimalloc = { version = "0.1", default-features = false }
num_cpus = "1"

# 枚举处理
strum = "0.27"
strum_macros = "0.27"
tower-sessions = "0.14"
tower-sessions-redis-store = "0.14"
jsonwebtoken = "9.3.1"

firebase-auth = "0.3.2" # 推荐使用最新版本


