-- =====================================================
-- TimescaleDB K线数据表结构定义
-- 核心设计原则：1m K线作为唯一真相源(SSOT)
-- =====================================================

-- 1. 创建基础1分钟K线表 (唯一真相源)
CREATE TABLE IF NOT EXISTS kline_1m (
    time TIMESTAMPTZ NOT NULL,           -- K线开始时间 (主分区键)
    symbol TEXT NOT NULL,                -- 交易对符号 (如 BTCUSDT)
    exchange TEXT NOT NULL DEFAULT 'binance', -- 交易所名称
    open_price DECIMAL(20,8) NOT NULL,   -- 开盘价
    high_price DECIMAL(20,8) NOT NULL,   -- 最高价
    low_price DECIMAL(20,8) NOT NULL,    -- 最低价
    close_price DECIMAL(20,8) NOT NULL,  -- 收盘价
    volume DECIMAL(20,8) NOT NULL,       -- 成交量
    quote_volume DECIMAL(20,8) NOT NULL, -- 报价资产成交量
    trades_count INTEGER NOT NULL,       -- 成交笔数
    taker_buy_volume DECIMAL(20,8) NOT NULL,       -- 主动买入成交量
    taker_buy_quote_volume DECIMAL(20,8) NOT NULL, -- 主动买入报价成交量
    created_at TIMESTAMPTZ DEFAULT NOW() -- 数据插入时间
);

-- 2. 转换为TimescaleDB超级表 (hypertable)
SELECT create_hypertable(
    'kline_1m', 
    'time',
    chunk_time_interval => INTERVAL '1 day',  -- 按天分块
    if_not_exists => TRUE
);

-- 3. 创建唯一性约束 (time, symbol) - 确保数据唯一性
CREATE UNIQUE INDEX IF NOT EXISTS idx_kline_1m_unique 
ON kline_1m (time, symbol);

-- 4. 创建查询优化索引
CREATE INDEX IF NOT EXISTS idx_kline_1m_symbol_time 
ON kline_1m (symbol, time DESC);

CREATE INDEX IF NOT EXISTS idx_kline_1m_exchange_symbol_time 
ON kline_1m (exchange, symbol, time DESC);

-- 5. 设置数据保留策略 (保留2年数据)
SELECT add_retention_policy('kline_1m', INTERVAL '2 years', if_not_exists => TRUE);

-- =====================================================
-- 连续聚合视图 - 从1m数据平行聚合所有时间级别
-- =====================================================

-- 6. 创建5分钟连续聚合视图
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_5m
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('5 minutes', time) AS time,
    symbol,
    exchange,
    FIRST(open_price, time) AS open_price,
    MAX(high_price) AS high_price,
    MIN(low_price) AS low_price,
    LAST(close_price, time) AS close_price,
    SUM(volume) AS volume,
    SUM(quote_volume) AS quote_volume,
    SUM(trades_count) AS trades_count,
    SUM(taker_buy_volume) AS taker_buy_volume,
    SUM(taker_buy_quote_volume) AS taker_buy_quote_volume
FROM kline_1m
GROUP BY time_bucket('5 minutes', time), symbol, exchange;

-- 7. 创建15分钟连续聚合视图
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_15m
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('15 minutes', time) AS time,
    symbol,
    exchange,
    FIRST(open_price, time) AS open_price,
    MAX(high_price) AS high_price,
    MIN(low_price) AS low_price,
    LAST(close_price, time) AS close_price,
    SUM(volume) AS volume,
    SUM(quote_volume) AS quote_volume,
    SUM(trades_count) AS trades_count,
    SUM(taker_buy_volume) AS taker_buy_volume,
    SUM(taker_buy_quote_volume) AS taker_buy_quote_volume
FROM kline_1m
GROUP BY time_bucket('15 minutes', time), symbol, exchange;

-- 8. 创建1小时连续聚合视图
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_1h
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS time,
    symbol,
    exchange,
    FIRST(open_price, time) AS open_price,
    MAX(high_price) AS high_price,
    MIN(low_price) AS low_price,
    LAST(close_price, time) AS close_price,
    SUM(volume) AS volume,
    SUM(quote_volume) AS quote_volume,
    SUM(trades_count) AS trades_count,
    SUM(taker_buy_volume) AS taker_buy_volume,
    SUM(taker_buy_quote_volume) AS taker_buy_quote_volume
FROM kline_1m
GROUP BY time_bucket('1 hour', time), symbol, exchange;

-- 9. 创建4小时连续聚合视图
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_4h
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('4 hours', time) AS time,
    symbol,
    exchange,
    FIRST(open_price, time) AS open_price,
    MAX(high_price) AS high_price,
    MIN(low_price) AS low_price,
    LAST(close_price, time) AS close_price,
    SUM(volume) AS volume,
    SUM(quote_volume) AS quote_volume,
    SUM(trades_count) AS trades_count,
    SUM(taker_buy_volume) AS taker_buy_volume,
    SUM(taker_buy_quote_volume) AS taker_buy_quote_volume
FROM kline_1m
GROUP BY time_bucket('4 hours', time), symbol, exchange;

-- 10. 创建1天连续聚合视图
CREATE MATERIALIZED VIEW IF NOT EXISTS kline_1d
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', time) AS time,
    symbol,
    exchange,
    FIRST(open_price, time) AS open_price,
    MAX(high_price) AS high_price,
    MIN(low_price) AS low_price,
    LAST(close_price, time) AS close_price,
    SUM(volume) AS volume,
    SUM(quote_volume) AS quote_volume,
    SUM(trades_count) AS trades_count,
    SUM(taker_buy_volume) AS taker_buy_volume,
    SUM(taker_buy_quote_volume) AS taker_buy_quote_volume
FROM kline_1m
GROUP BY time_bucket('1 day', time), symbol, exchange;

-- =====================================================
-- 连续聚合刷新策略
-- =====================================================

-- 11. 设置连续聚合自动刷新策略
SELECT add_continuous_aggregate_policy('kline_5m',
    start_offset => INTERVAL '1 hour',
    end_offset => INTERVAL '5 minutes',
    schedule_interval => INTERVAL '5 minutes',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('kline_15m',
    start_offset => INTERVAL '2 hours',
    end_offset => INTERVAL '15 minutes',
    schedule_interval => INTERVAL '15 minutes',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('kline_1h',
    start_offset => INTERVAL '4 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('kline_4h',
    start_offset => INTERVAL '8 hours',
    end_offset => INTERVAL '4 hours',
    schedule_interval => INTERVAL '4 hours',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('kline_1d',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day',
    if_not_exists => TRUE);

-- =====================================================
-- 数据压缩策略 (冷热数据分层)
-- =====================================================

-- 12. 设置数据压缩策略 - 7天后压缩数据
SELECT add_compression_policy('kline_1m', INTERVAL '7 days', if_not_exists => TRUE);

-- 13. 为聚合视图设置压缩策略
SELECT add_compression_policy('kline_5m', INTERVAL '30 days', if_not_exists => TRUE);
SELECT add_compression_policy('kline_15m', INTERVAL '60 days', if_not_exists => TRUE);
SELECT add_compression_policy('kline_1h', INTERVAL '90 days', if_not_exists => TRUE);
SELECT add_compression_policy('kline_4h', INTERVAL '180 days', if_not_exists => TRUE);
SELECT add_compression_policy('kline_1d', INTERVAL '365 days', if_not_exists => TRUE);
