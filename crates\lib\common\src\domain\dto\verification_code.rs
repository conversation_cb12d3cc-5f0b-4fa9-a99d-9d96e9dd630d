use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct VerificationCode {
    pub id: String,
    pub email: String,
    pub code: String,
    pub created_at: DateTime<Utc>,
    pub expired_at: DateTime<Utc>,
    pub is_used: bool,
}

impl VerificationCode {
    pub fn new(email: String, code: String) -> Self {
        let now = Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            email,
            code,
            created_at: now,
            expired_at: now + chrono::Duration::minutes(5),
            is_used: false,
        }
    }

    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expired_at
    }
} 