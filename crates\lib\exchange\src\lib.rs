pub mod binance;

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

/// 通用K线数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniversalKline {
    pub open_time: i64,
    pub open_price: String,
    pub high_price: String,
    pub low_price: String,
    pub close_price: String,
    pub volume: String,
    pub close_time: i64,
    pub quote_asset_volume: String,
    pub number_of_trades: i32,
    pub taker_buy_base_asset_volume: String,
    pub taker_buy_quote_asset_volume: String,
}

/// 交易所查询参数
#[derive(Debug, <PERSON>lone)]
pub struct KlineQuery {
    pub symbol: String,
    pub interval: String,
    pub start_time: Option<i64>,
    pub end_time: Option<i64>,
    pub limit: Option<i32>,
}

/// 交易所信息
#[derive(Debug, Clone)]
pub struct ExchangeInfo {
    pub name: String,
    pub base_url: String,
    pub rate_limit: u32,  // 每分钟请求限制
}

/// 统一的交易所客户端接口
#[async_trait]
pub trait ExchangeClient: Send + Sync {
    /// 获取交易所信息
    fn get_exchange_info(&self) -> &ExchangeInfo;
    
    /// 获取K线数据
    async fn get_klines(&self, query: KlineQuery) -> Result<Vec<UniversalKline>>;
    
    /// 测试连接
    async fn test_connection(&self) -> Result<()>;
    
    /// 获取服务器时间
    async fn get_server_time(&self) -> Result<DateTime<Utc>>;
    
    /// 获取交易对信息
    async fn get_symbol_info(&self, symbol: &str) -> Result<SymbolInfo>;

    /// 克隆自身为一个新的 Box<dyn ExchangeClient>
    /// 实现者需要确保克隆后的实例是独立的，并且可以安全地在不同的异步任务中使用。
    fn clone_box(&self) -> Box<dyn ExchangeClient>;
}

/// 交易对信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SymbolInfo {
    pub symbol: String,
    pub base_asset: String,
    pub quote_asset: String,
    pub status: String,
    pub min_qty: String,
    pub max_qty: String,
    pub step_size: String,
}

/// 交易所客户端工厂
pub struct ExchangeClientFactory;

impl ExchangeClientFactory {
    /// 创建Binance客户端
    pub fn create_binance_client() -> Box<dyn ExchangeClient> {
        Box::new(binance::BinanceClient::new())
    }
    
    /// 根据名称创建客户端
    pub fn create_client(exchange_name: &str) -> Result<Box<dyn ExchangeClient>> {
        match exchange_name.to_lowercase().as_str() {
            "binance" => Ok(Self::create_binance_client()),
            _ => Err(anyhow::anyhow!("Unsupported exchange: {}", exchange_name)),
        }
    }
} 