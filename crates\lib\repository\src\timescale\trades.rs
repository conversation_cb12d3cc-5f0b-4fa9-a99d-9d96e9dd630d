use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::{Result, RepositoryError};

/// 交易数据实体
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Trade {
    pub id: i64,
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub price: Decimal,
    pub quantity: Decimal,
    pub side: String, // 'buy' or 'sell'
    pub trade_type: String, // 'market', 'limit', etc.
    pub trader_id: Option<String>,
    pub order_id: Option<String>,
}

/// 交易数据创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTradeRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub price: Decimal,
    pub quantity: Decimal,
    pub side: String,
    pub trade_type: String,
    pub trader_id: Option<String>,
    pub order_id: Option<String>,
}

/// 交易查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeQuery {
    pub symbol: Option<String>,
    pub trader_id: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub side: Option<String>,
    pub limit: Option<u32>,
}

/// 交易统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TradeStats {
    pub symbol: String,
    pub total_trades: i64,
    pub total_volume: Decimal,
    pub total_value: Decimal,
    pub avg_price: Decimal,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub buy_volume: Decimal,
    pub sell_volume: Decimal,
}

/// 交易Repository
pub struct TradeRepository {
    pool: PgPool,
}

impl TradeRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 插入单条交易记录
    pub async fn insert(&self, request: CreateTradeRequest) -> Result<Trade> {
        let trade = sqlx::query_as::<_, Trade>(
            r#"
            INSERT INTO trades (symbol, timestamp, price, quantity, side, trade_type, trader_id, order_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
            "#,
        )
        .bind(&request.symbol)
        .bind(&request.timestamp)
        .bind(&request.price)
        .bind(&request.quantity)
        .bind(&request.side)
        .bind(&request.trade_type)
        .bind(&request.trader_id)
        .bind(&request.order_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(trade)
    }

    /// 批量插入交易记录
    pub async fn batch_insert(&self, requests: Vec<CreateTradeRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO trades (symbol, timestamp, price, quantity, side, trade_type, trader_id, order_id) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.symbol)
                .push_bind(&request.timestamp)
                .push_bind(&request.price)
                .push_bind(&request.quantity)
                .push_bind(&request.side)
                .push_bind(&request.trade_type)
                .push_bind(&request.trader_id)
                .push_bind(&request.order_id);
        });

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询交易记录
    pub async fn query(&self, params: TradeQuery) -> Result<Vec<Trade>> {
        let mut query = sqlx::QueryBuilder::new("SELECT * FROM trades WHERE 1=1");

        if let Some(symbol) = &params.symbol {
            query.push(" AND symbol = ").push_bind(symbol);
        }
        if let Some(trader_id) = &params.trader_id {
            query.push(" AND trader_id = ").push_bind(trader_id);
        }
        if let Some(side) = &params.side {
            query.push(" AND side = ").push_bind(side);
        }

        query.push(" AND timestamp >= ").push_bind(&params.start_time);
        query.push(" AND timestamp <= ").push_bind(&params.end_time);
        query.push(" ORDER BY timestamp DESC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let trades: Vec<Trade> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(trades)
    }

    /// 获取交易统计
    pub async fn get_stats(
        &self, 
        symbol: &str, 
        start_time: DateTime<Utc>, 
        end_time: DateTime<Utc>
    ) -> Result<TradeStats> {
        let stats = sqlx::query_as::<_, TradeStats>(
            r#"
            SELECT 
                $1 as symbol,
                COUNT(*) as total_trades,
                SUM(quantity) as total_volume,
                SUM(price * quantity) as total_value,
                AVG(price) as avg_price,
                MIN(price) as min_price,
                MAX(price) as max_price,
                SUM(CASE WHEN side = 'buy' THEN quantity ELSE 0 END) as buy_volume,
                SUM(CASE WHEN side = 'sell' THEN quantity ELSE 0 END) as sell_volume
            FROM trades 
            WHERE symbol = $1 AND timestamp >= $2 AND timestamp <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取交易者的交易历史
    pub async fn get_trader_trades(
        &self,
        trader_id: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>
    ) -> Result<Vec<Trade>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT * FROM trades WHERE trader_id = "
        );
        query.push_bind(trader_id);
        query.push(" AND timestamp >= ").push_bind(start_time);
        query.push(" AND timestamp <= ").push_bind(end_time);
        query.push(" ORDER BY timestamp DESC");

        if let Some(limit) = limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let trades: Vec<Trade> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(trades)
    }

    /// 获取最近的交易记录
    pub async fn get_recent_trades(&self, symbol: &str, limit: u32) -> Result<Vec<Trade>> {
        let trades = sqlx::query_as::<_, Trade>(
            "SELECT * FROM trades WHERE symbol = $1 ORDER BY timestamp DESC LIMIT $2"
        )
        .bind(symbol)
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;

        Ok(trades)
    }

    /// 计算VWAP (成交量加权平均价格)
    pub async fn calculate_vwap(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Option<Decimal>> {
        let result: Option<(Option<Decimal>,)> = sqlx::query_as(
            r#"
            SELECT 
                CASE 
                    WHEN SUM(quantity) > 0 THEN SUM(price * quantity) / SUM(quantity)
                    ELSE NULL
                END as vwap
            FROM trades 
            WHERE symbol = $1 AND timestamp >= $2 AND timestamp <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_optional(&self.pool)
        .await?;

        Ok(result.and_then(|(vwap,)| vwap))
    }

    /// 删除旧交易数据
    pub async fn cleanup_old_trades(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM trades WHERE timestamp < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }
} 