use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use crate::error::RepositoryError;

type Result<T> = std::result::Result<T, RepositoryError>;

/// 实时交易数据实体（来自币安交易流）
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TradeEntity {
    pub time: DateTime<Utc>,
    pub symbol: String,
    pub trade_id: i64,
    pub price: Decimal,
    pub qty: Decimal,
    pub quote_qty: Decimal,
    pub is_buyer_maker: bool,
    pub is_best_match: bool,
}

/// 交易数据创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTradeRequest {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub trade_id: i64,
    pub price: Decimal,
    pub qty: Decimal,
    pub quote_qty: Decimal,
    pub is_buyer_maker: bool,
    pub is_best_match: bool,
}

/// 交易查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeQuery {
    pub symbol: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub limit: Option<u32>,
}

/// 交易统计
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct TradeStats {
    pub symbol: String,
    pub total_trades: i64,
    pub total_volume: Decimal,
    pub total_quote_volume: Decimal,
    pub avg_price: Decimal,
    pub min_price: Decimal,
    pub max_price: Decimal,
    pub maker_volume: Decimal,
    pub taker_volume: Decimal,
}

/// 交易Repository
#[derive(Clone)]
pub struct TradeRepository {
    pool: PgPool,
}

impl TradeRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化交易表结构
    pub async fn initialize_table(&self) -> Result<()> {
        // 创建交易数据表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS trades (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                trade_id BIGINT NOT NULL,
                price DECIMAL NOT NULL,
                qty DECIMAL NOT NULL,
                quote_qty DECIMAL NOT NULL,
                is_buyer_maker BOOLEAN NOT NULL,
                is_best_match BOOLEAN NOT NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 将交易表转换为超级表
        log::info!("正在创建trades超级表...");
        sqlx::query("SELECT create_hypertable($1::regclass, $2::name, if_not_exists => $3::boolean);")
            .bind("trades")
            .bind("time")
            .bind(true)
            .execute(&self.pool)
            .await
            .map_err(|e| {
                log::error!("创建trades超级表失败: {}", e);
                RepositoryError::Database(e)
            })?;
        log::info!("trades超级表创建成功");

        // 添加唯一约束
        sqlx::query(
            r#"
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'trades_unique' AND conrelid = 'trades'::regclass
                ) THEN
                    ALTER TABLE trades ADD CONSTRAINT trades_unique UNIQUE (time, symbol, trade_id);
                END IF;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建索引优化查询
        sqlx::query(
            "CREATE INDEX IF NOT EXISTS idx_trades_symbol_time
             ON trades (symbol, time DESC);",
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::info!("交易表结构初始化完成");
        Ok(())
    }

    /// 插入单条交易记录
    pub async fn insert(&self, request: CreateTradeRequest) -> Result<TradeEntity> {
        let trade = sqlx::query_as::<_, TradeEntity>(
            r#"
            INSERT INTO trades (time, symbol, trade_id, price, qty, quote_qty, is_buyer_maker, is_best_match)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            ON CONFLICT (time, symbol, trade_id) DO UPDATE SET
                price = EXCLUDED.price,
                qty = EXCLUDED.qty,
                quote_qty = EXCLUDED.quote_qty,
                is_buyer_maker = EXCLUDED.is_buyer_maker,
                is_best_match = EXCLUDED.is_best_match
            RETURNING time, symbol, trade_id, price, qty, quote_qty, is_buyer_maker, is_best_match
            "#,
        )
        .bind(&request.timestamp)
        .bind(&request.symbol)
        .bind(&request.trade_id)
        .bind(&request.price)
        .bind(&request.qty)
        .bind(&request.quote_qty)
        .bind(&request.is_buyer_maker)
        .bind(&request.is_best_match)
        .fetch_one(&self.pool)
        .await?;

        Ok(trade)
    }

    /// 批量插入交易记录
    pub async fn batch_insert(&self, requests: Vec<CreateTradeRequest>) -> Result<u64> {
        if requests.is_empty() {
            return Ok(0);
        }

        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO trades (time, symbol, trade_id, price, qty, quote_qty, is_buyer_maker, is_best_match) "
        );

        query_builder.push_values(requests.iter(), |mut b, request| {
            b.push_bind(&request.timestamp)
                .push_bind(&request.symbol)
                .push_bind(&request.trade_id)
                .push_bind(&request.price)
                .push_bind(&request.qty)
                .push_bind(&request.quote_qty)
                .push_bind(&request.is_buyer_maker)
                .push_bind(&request.is_best_match);
        });

        // 添加 ON CONFLICT 子句处理重复数据
        query_builder.push(
            " ON CONFLICT (time, symbol, trade_id) DO UPDATE SET \
             price = EXCLUDED.price, \
             qty = EXCLUDED.qty, \
             quote_qty = EXCLUDED.quote_qty, \
             is_buyer_maker = EXCLUDED.is_buyer_maker, \
             is_best_match = EXCLUDED.is_best_match"
        );

        let result = query_builder.build().execute(&self.pool).await?;
        Ok(result.rows_affected())
    }

    /// 查询交易记录
    pub async fn query(&self, params: TradeQuery) -> Result<Vec<TradeEntity>> {
        let mut query = sqlx::QueryBuilder::new(
            "SELECT time, symbol, trade_id, price, qty, quote_qty, is_buyer_maker, is_best_match FROM trades WHERE symbol = "
        );
        query.push_bind(&params.symbol);
        query.push(" AND time >= ").push_bind(&params.start_time);
        query.push(" AND time <= ").push_bind(&params.end_time);
        query.push(" ORDER BY time DESC");

        if let Some(limit) = params.limit {
            query.push(" LIMIT ").push_bind(limit as i64);
        }

        let trades: Vec<TradeEntity> = query
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        Ok(trades)
    }

    /// 获取交易统计
    pub async fn get_stats(
        &self, 
        symbol: &str, 
        start_time: DateTime<Utc>, 
        end_time: DateTime<Utc>
    ) -> Result<TradeStats> {
        let stats = sqlx::query_as::<_, TradeStats>(
            r#"
            SELECT 
                $1 as symbol,
                COUNT(*) as total_trades,
                SUM(qty) as total_volume,
                SUM(quote_qty) as total_quote_volume,
                AVG(price) as avg_price,
                MIN(price) as min_price,
                MAX(price) as max_price,
                SUM(CASE WHEN is_buyer_maker = true THEN qty ELSE 0 END) as maker_volume,
                SUM(CASE WHEN is_buyer_maker = false THEN qty ELSE 0 END) as taker_volume
            FROM trades 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_one(&self.pool)
        .await?;

        Ok(stats)
    }

    /// 获取最近的交易记录
    pub async fn get_recent_trades(&self, symbol: &str, limit: u32) -> Result<Vec<TradeEntity>> {
        let trades = sqlx::query_as::<_, TradeEntity>(
            "SELECT time, symbol, trade_id, price, qty, quote_qty, is_buyer_maker, is_best_match FROM trades WHERE symbol = $1 ORDER BY time DESC LIMIT $2"
        )
        .bind(symbol)
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;

        Ok(trades)
    }

    /// 计算VWAP (成交量加权平均价格)
    pub async fn calculate_vwap(
        &self,
        symbol: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>
    ) -> Result<Option<Decimal>> {
        let result: Option<(Option<Decimal>,)> = sqlx::query_as(
            r#"
            SELECT 
                CASE 
                    WHEN SUM(qty) > 0 THEN SUM(price * qty) / SUM(qty)
                    ELSE NULL
                END as vwap
            FROM trades 
            WHERE symbol = $1 AND time >= $2 AND time <= $3
            "#,
        )
        .bind(symbol)
        .bind(start_time)
        .bind(end_time)
        .fetch_optional(&self.pool)
        .await?;

        Ok(result.and_then(|(vwap,)| vwap))
    }

    /// 删除旧交易数据
    pub async fn cleanup_old_data(&self, before_time: DateTime<Utc>) -> Result<u64> {
        let result = sqlx::query(
            "DELETE FROM trades WHERE time < $1"
        )
        .bind(before_time)
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }

    /// 获取所有活跃交易对
    pub async fn get_active_symbols(&self, within_duration: chrono::Duration) -> Result<Vec<String>> {
        let cutoff_time = Utc::now() - within_duration;

        let symbols = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT symbol FROM trades WHERE time >= $1 ORDER BY symbol"
        )
        .bind(cutoff_time)
        .fetch_all(&self.pool)
        .await?;

        Ok(symbols)
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1").execute(&self.pool).await?;
        Ok(())
    }
} 