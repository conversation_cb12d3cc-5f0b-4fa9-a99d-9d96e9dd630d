use anyhow::Result;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::sync::Arc;
use tracing::{info, warn, debug};

// 使用repository模块
use repository::{
    timescale::{KlineRepository, TradeRepository, CreateMarketDataRequest, CreateTradeRequest, MarketDataQuery},
    connection::{timescale::TimescalePoolManager, DatabaseConfig},
    CacheRepository, CacheError, TtlCalculator,
};

use common::domain::entity::{
    KlineEntity, TickerEntity, OrderBookEntity, 
    Stats24hrEntity, TradeEntity
};

use common::domain::dto::{
    KlineQueryDto, TickerQueryDto, OrderBookQueryDto,
    StatsQueryDto, TradeHistoryQueryDto, KlineDto
};

/// 纯CRUD市场数据服务
/// 
/// 专注于数据查询和基本CRUD操作，不包含维护功能
pub struct MarketDataCrudService {
    // Repository层
    kline_repo: KlineRepository,
    trade_repo: TradeRepository,
    cache_repo: Arc<dyn CacheRepository>,
}

impl MarketDataCrudService {
    /// 创建新的CRUD服务实例
    pub async fn new(
        timescale_pool: TimescalePoolManager,
        cache_repo: Arc<dyn CacheRepository>,
    ) -> Result<Self> {
        let pool = timescale_pool.pool().clone();
        
        Ok(Self {
            kline_repo: KlineRepository::new(pool.clone()),
            trade_repo: TradeRepository::new(pool),
            cache_repo,
        })
    }

    /// 获取K线数据 - 实现Cache-Aside模式
    /// 
    /// 1. 首先检查缓存
    /// 2. 缓存未命中时查询数据库
    /// 3. 将数据库结果异步写入缓存
    /// 4. 返回数据
    pub async fn get_kline_data(&self, query: KlineQueryDto) -> Result<Vec<KlineEntity>> {
        let start_time = query.start_time.unwrap_or_else(|| chrono::Utc::now() - chrono::Duration::days(1));
        let end_time = query.end_time.unwrap_or_else(|| chrono::Utc::now());
        
        debug!(
            "Getting klines for {} {} from {} to {} (limit: {:?})",
            query.symbol, query.interval, start_time, end_time, query.limit
        );

        // 1. 首先尝试从缓存获取数据
        match self.cache_repo.get_klines_by_range(
            &query.symbol,
            &query.interval,
            start_time,
            end_time,
            query.limit.map(|l| l as usize),
        ).await {
            Ok(cached_klines) if !cached_klines.is_empty() => {
                info!("Cache hit: Retrieved {} klines from cache for {}", cached_klines.len(), query.symbol);
                
                // 将KlineDto转换为KlineEntity
                let klines = cached_klines.into_iter().map(|dto| KlineEntity {
                    time: dto.time,
                    symbol: dto.symbol,
                    interval: dto.interval,
                    open_price: dto.open_price,
                    high_price: dto.high_price,
                    low_price: dto.low_price,
                    close_price: dto.close_price,
                    volume: dto.volume,
                    quote_asset_volume: dto.quote_asset_volume,
                    number_of_trades: dto.number_of_trades,
                    taker_buy_base_asset_volume: dto.taker_buy_base_asset_volume,
                    taker_buy_quote_asset_volume: dto.taker_buy_quote_asset_volume,
                    close_time: dto.close_time,
                }).collect();
                
                return Ok(klines);
            }
            Ok(_) => {
                debug!("Cache miss: No data found in cache for {}", query.symbol);
            }
            Err(e) => {
                warn!("Cache error: {}, falling back to database", e);
            }
        }

        // 2. 缓存未命中，查询数据库
        let market_query = MarketDataQuery {
            symbol: query.symbol.clone(),
            start_time,
            end_time,
            interval_type: query.interval.clone(),
            limit: query.limit.map(|l| l as u32),
        };

        let market_data = self.kline_repo.query(market_query).await?;
        
        let klines: Vec<KlineEntity> = market_data.into_iter().map(|data| KlineEntity {
            time: data.timestamp,
            symbol: data.symbol,
            interval: data.interval_type,
            open_price: data.open,
            high_price: data.high,
            low_price: data.low,
            close_price: data.close,
            volume: data.volume,
            quote_asset_volume: Decimal::ZERO,
            number_of_trades: 0,
            taker_buy_base_asset_volume: Decimal::ZERO,
            taker_buy_quote_asset_volume: Decimal::ZERO,
            close_time: data.timestamp,
        }).collect();

        info!("Database query: Retrieved {} klines from database for {}", klines.len(), query.symbol);

        // 3. 异步将数据写入缓存（不阻塞返回）
        if !klines.is_empty() {
            let cache_repo = self.cache_repo.clone();
            let symbol = query.symbol.clone();
            let interval = query.interval.clone();
            let klines_for_cache = klines.clone();
            
            tokio::spawn(async move {
                if let Err(e) = Self::write_klines_to_cache(cache_repo, &symbol, &interval, &klines_for_cache).await {
                    warn!("Failed to write klines to cache: {}", e);
                }
            });
        }

        // 4. 返回数据
        Ok(klines)
    }

    /// 将K线数据写入缓存的辅助方法
    async fn write_klines_to_cache(
        cache_repo: Arc<dyn CacheRepository>,
        symbol: &str,
        interval: &str,
        klines: &[KlineEntity],
    ) -> Result<(), CacheError> {
        use std::collections::HashMap;

        // 按日期分组K线数据
        let mut klines_by_date: HashMap<String, Vec<KlineDto>> = HashMap::new();
        
        for kline in klines {
            let date = KlineDto::timestamp_to_date_string(kline.time.timestamp());
            let dto = KlineDto {
                time: kline.time,
                symbol: kline.symbol.clone(),
                interval: kline.interval.clone(),
                open_time: kline.time,
                close_time: kline.close_time,
                open_price: kline.open_price,
                high_price: kline.high_price,
                low_price: kline.low_price,
                close_price: kline.close_price,
                volume: kline.volume,
                quote_asset_volume: kline.quote_asset_volume,
                number_of_trades: kline.number_of_trades,
                taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
                taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            };
            
            klines_by_date.entry(date).or_insert_with(Vec::new).push(dto);
        }

        // 为每个日期写入缓存并设置TTL
        for (date, date_klines) in klines_by_date {
            // 添加K线数据
            cache_repo.add_klines_for_day(symbol, interval, &date, &date_klines).await?;
            
            // 设置TTL
            let current_date = TtlCalculator::get_current_date_string();
            let ttl = if date == current_date {
                // 当天的数据使用固定TTL
                TtlCalculator::get_fixed_ttl()
            } else {
                // 历史数据使用动态TTL
                TtlCalculator::calculate_dynamic_ttl(&date, &current_date)?
            };
            
            cache_repo.set_expiry_for_day(symbol, &date, ttl).await?;
        }

        Ok(())
    }
    
    /// 获取实时价格
    pub async fn get_ticker_price(&self, query: TickerQueryDto) -> Result<Vec<TickerEntity>> {
        if let Some(ref symbol) = query.symbol {
            if let Some(latest) = self.kline_repo.get_latest(symbol, "1m").await? {
                let ticker = TickerEntity {
                    time: latest.timestamp,
                    symbol: latest.symbol,
                    price: latest.close,
                };
                Ok(vec![ticker])
            } else {
                Ok(vec![])
            }
        } else {
            Ok(vec![])
        }
    }
    
    /// 获取深度数据
    pub async fn get_order_book(&self, query: OrderBookQueryDto) -> Result<OrderBookEntity> {
        Ok(OrderBookEntity {
            time: chrono::Utc::now(),
            symbol: query.symbol,
            last_update_id: 0,
            bids: sqlx::types::Json(vec![]),
            asks: sqlx::types::Json(vec![]),
        })
    }
    
    /// 获取24小时统计数据
    pub async fn get_24hr_stats(&self, query: StatsQueryDto) -> Result<Vec<Stats24hrEntity>> {
        if let Some(ref symbol) = query.symbol {
            let end_time = chrono::Utc::now();
            let start_time = end_time - chrono::Duration::hours(24);
            
            if let Some(stats) = self.kline_repo.get_stats(symbol, start_time, end_time).await.ok() {
                let stats_entity = Stats24hrEntity {
                    time: end_time,
                    symbol: symbol.clone(),
                    price_change: Decimal::ZERO,
                    price_change_percent: Decimal::ZERO,
                    weighted_avg_price: stats.avg_price.unwrap_or(Decimal::ZERO),
                    prev_close_price: Decimal::ZERO,
                    last_price: stats.max_price.unwrap_or(Decimal::ZERO),
                    last_qty: Decimal::ZERO,
                    bid_price: Decimal::ZERO,
                    ask_price: Decimal::ZERO,
                    open_price: stats.min_price.unwrap_or(Decimal::ZERO),
                    high_price: stats.max_price.unwrap_or(Decimal::ZERO),
                    low_price: stats.min_price.unwrap_or(Decimal::ZERO),
                    volume: stats.total_volume.unwrap_or(Decimal::ZERO),
                    quote_volume: Decimal::ZERO,
                    open_time: start_time,
                    close_time: end_time,
                    first_id: 0,
                    last_id: 0,
                    count: stats.count as i64,
                };
                Ok(vec![stats_entity])
            } else {
                Ok(vec![])
            }
        } else {
            Ok(vec![])
        }
    }
    
    /// 获取交易历史
    pub async fn get_trade_history(&self, query: TradeHistoryQueryDto) -> Result<Vec<TradeEntity>> {
        let trade_query = repository::timescale::TradeQuery {
            symbol: Some(query.symbol.clone()),
            trader_id: None,
            start_time: query.start_time.unwrap_or_else(|| chrono::Utc::now() - chrono::Duration::hours(1)),
            end_time: query.end_time.unwrap_or_else(|| chrono::Utc::now()),
            side: None,
            limit: query.limit.map(|l| l as u32),
        };

        let trades = self.trade_repo.query(trade_query).await?;
        
        let trade_entities = trades.into_iter().map(|trade| TradeEntity {
            trade_id: trade.id,
            time: trade.timestamp,
            symbol: trade.symbol,
            price: trade.price,
            qty: trade.quantity,
            quote_qty: trade.price * trade.quantity,
            is_buyer_maker: trade.side == "sell",
            is_best_match: true,
        }).collect();

        Ok(trade_entities)
    }

    /// 保存K线数据
    pub async fn save_kline_data(&self, klines: Vec<KlineEntity>) -> Result<()> {
        let requests: Vec<CreateMarketDataRequest> = klines.iter()
            .map(Self::kline_to_market_data_request)
            .collect();
        
        self.kline_repo.batch_insert(requests).await?;
        Ok(())
    }
    
    /// 保存交易数据
    pub async fn save_trade_data(&self, trades: Vec<TradeEntity>) -> Result<()> {
        let requests: Vec<CreateTradeRequest> = trades.iter()
            .map(Self::trade_to_trade_request)
            .collect();
        
        self.trade_repo.batch_insert(requests).await?;
        Ok(())
    }

    /// 将KlineEntity转换为CreateMarketDataRequest
    fn kline_to_market_data_request(kline: &KlineEntity) -> CreateMarketDataRequest {
        CreateMarketDataRequest {
            symbol: kline.symbol.clone(),
            timestamp: kline.time,
            open: kline.open_price,
            high: kline.high_price,
            low: kline.low_price,
            close: kline.close_price,
            volume: kline.volume,
            interval_type: kline.interval.clone(),
            quote_asset_volume: kline.quote_asset_volume,
            number_of_trades: kline.number_of_trades,
            taker_buy_base_asset_volume: kline.taker_buy_base_asset_volume,
            taker_buy_quote_asset_volume: kline.taker_buy_quote_asset_volume,
            close_time: kline.close_time,
        }
    }

    /// 将TradeEntity转换为CreateTradeRequest
    fn trade_to_trade_request(trade: &TradeEntity) -> CreateTradeRequest {
        CreateTradeRequest {
            symbol: trade.symbol.clone(),
            timestamp: trade.time,
            price: trade.price,
            quantity: trade.qty,
            side: if trade.is_buyer_maker { "sell".to_string() } else { "buy".to_string() },
            trade_type: "market".to_string(),
            trader_id: None,
            order_id: None,
        }
    }
}
