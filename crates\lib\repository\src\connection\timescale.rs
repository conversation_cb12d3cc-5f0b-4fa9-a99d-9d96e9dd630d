use sqlx::{PgPool, Pool, Postgres, Row};
use sqlx::postgres::PgPoolOptions;
use crate::{Result, RepositoryError};
use super::{DatabaseConfig, PoolStatus};

/// TimescaleDB连接池管理器
/// 基于PostgreSQL但针对时间序列数据进行优化
pub struct TimescalePoolManager {
    pool: PgPool,
    config: DatabaseConfig,
}

impl TimescalePoolManager {
    /// 创建新的TimescaleDB连接池管理器
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        let pool = PgPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .acquire_timeout(std::time::Duration::from_secs(config.connect_timeout))
            .idle_timeout(Some(std::time::Duration::from_secs(config.idle_timeout)))
            .connect(&config.connection_string())
            .await?;

        // 立即创建TimescaleDB扩展（如果不存在）
        log::info!("正在初始化TimescaleDB扩展...");
        Self::ensure_timescaledb_extension(&pool).await?;
        log::info!("TimescaleDB扩展初始化完成");

        Ok(Self { pool, config })
    }

    /// 确保TimescaleDB扩展已启用
    async fn ensure_timescaledb_extension(pool: &PgPool) -> Result<()> {
        // 检查TimescaleDB扩展是否已存在
        let extension_exists = sqlx::query_scalar::<_, bool>(
            "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb')"
        )
        .fetch_one(pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        if !extension_exists {
            // 创建TimescaleDB扩展
            log::info!("正在创建TimescaleDB扩展...");
            sqlx::query("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;")
                .execute(pool)
                .await
                .map_err(|e| {
                    log::error!("创建TimescaleDB扩展失败: {}. 请确保: 1) TimescaleDB已安装 2) 用户有创建扩展权限 3) 数据库支持TimescaleDB", e);
                    RepositoryError::Database(e)
                })?;
            log::info!("TimescaleDB扩展创建成功");
        } else {
            log::info!("TimescaleDB扩展已存在，跳过创建");
        }

        // 验证TimescaleDB核心功能
        // 1. 验证time_bucket函数
        sqlx::query("SELECT time_bucket('1 hour', NOW())")
            .execute(pool)
            .await
            .map_err(|e| {
                log::error!("TimescaleDB time_bucket函数验证失败: {}", e);
                RepositoryError::Database(e)
            })?;

        // 2. 验证create_hypertable函数存在
        let hypertable_func_exists = sqlx::query_scalar::<_, bool>(
            "SELECT EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'create_hypertable')"
        )
        .fetch_one(pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        if !hypertable_func_exists {
            log::error!("create_hypertable函数不存在，TimescaleDB扩展可能安装不完整");
            return Err(RepositoryError::configuration("create_hypertable function not available"));
        }

        log::info!("TimescaleDB核心功能验证成功");

        Ok(())
    }

    /// 获取连接池引用
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// 获取连接池状态
    pub fn status(&self) -> PoolStatus {
        let size = self.pool.size() as u32;
        PoolStatus {
            active_connections: size,
            idle_connections: 0, // SQLx不直接提供idle_size方法
            total_connections: size,
            is_healthy: !self.pool.is_closed(),
        }
    }

    /// 测试连接和TimescaleDB功能
    pub async fn test_connection(&self) -> Result<()> {
        // 测试基础连接
        sqlx::query("SELECT 1")
            .execute(&self.pool)
            .await?;
            
        // 测试TimescaleDB功能
        sqlx::query("SELECT time_bucket('1 hour', NOW())")
            .execute(&self.pool)
            .await?;
            
        Ok(())
    }

    /// 获取TimescaleDB版本信息
    pub async fn get_timescale_version(&self) -> Result<String> {
        let row = sqlx::query("SELECT extversion FROM pg_extension WHERE extname = 'timescaledb'")
            .fetch_one(&self.pool)
            .await?;
            
        let version: String = row.get(0);
        Ok(version)
    }

    /// 关闭连接池
    pub async fn close(&self) {
        self.pool.close().await;
    }

    /// 获取配置信息
    pub fn config(&self) -> &DatabaseConfig {
        &self.config
    }
} 