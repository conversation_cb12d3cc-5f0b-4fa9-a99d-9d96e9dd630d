#[cfg(test)]
mod tests {
    use crate::market_data::enhanced_config::*;
    use std::fs;
    use tempfile::tempdir;

    #[test]
    fn test_default_config_creation() {
        let config = EnhancedMaintenanceConfig::default();
        
        assert_eq!(config.environment, Environment::Development);
        assert_eq!(config.service.name, "market-data-maintenance");
        assert_eq!(config.maintenance.full_scan_interval_hours, 24);
        assert_eq!(config.maintenance.incremental_scan_interval_minutes, 5);
        assert!(config.maintenance.enable_auto_repair);
        
        // 验证配置
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_validation() {
        let mut config = EnhancedMaintenanceConfig::default();
        
        // 测试有效配置
        assert!(config.validate().is_ok());
        
        // 测试无效配置
        config.maintenance.full_scan_interval_hours = 0;
        assert!(config.validate().is_err());
        
        config.maintenance.full_scan_interval_hours = 24;
        config.gap_detection.sampling_rate = 1.5; // 超出范围
        assert!(config.validate().is_err());
        
        config.gap_detection.sampling_rate = 0.1;
        config.maintenance.max_concurrent_symbols = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_environment_adjustment() {
        let mut config = EnhancedMaintenanceConfig::default();
        
        // 测试开发环境调整
        config.environment = Environment::Development;
        config.adjust_for_environment();
        assert_eq!(config.maintenance.full_scan_interval_hours, 1);
        assert_eq!(config.gap_detection.sampling_rate, 0.5);
        assert_eq!(config.monitoring.logging.level, "debug");
        
        // 测试生产环境调整
        config = EnhancedMaintenanceConfig::default();
        config.environment = Environment::Production;
        config.adjust_for_environment();
        assert_eq!(config.gap_detection.sampling_rate, 0.05);
        assert!(config.performance.enable_compression);
        assert_eq!(config.monitoring.logging.level, "info");
        
        // 测试测试环境调整
        config = EnhancedMaintenanceConfig::default();
        config.environment = Environment::Testing;
        config.adjust_for_environment();
        assert!(!config.maintenance.enable_auto_repair);
        assert!(!config.monitoring.alerts.enabled);
    }

    #[test]
    fn test_config_serialization() {
        let config = EnhancedMaintenanceConfig::default();

        // 测试TOML序列化
        let toml_str = toml::to_string_pretty(&config).unwrap();
        println!("TOML serialization:\n{}", toml_str);
        assert!(toml_str.contains("environment = \"Development\""));
        assert!(toml_str.contains("[service]"));
        assert!(toml_str.contains("[maintenance]"));

        // 测试TOML反序列化
        let deserialized: EnhancedMaintenanceConfig = toml::from_str(&toml_str).unwrap();
        assert_eq!(deserialized.environment, config.environment);
        assert_eq!(deserialized.service.name, config.service.name);

        // 测试JSON序列化
        let json_str = serde_json::to_string_pretty(&config).unwrap();
        let deserialized_json: EnhancedMaintenanceConfig = serde_json::from_str(&json_str).unwrap();
        assert_eq!(deserialized_json.environment, config.environment);
    }

    #[tokio::test]
    async fn test_config_file_loading() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("test_config.toml");
        
        // 创建测试配置文件
        let test_config = r#"
environment = "Testing"

[service]
name = "test-service"
version = "2.0.0"
startup_mode = "Fast"
graceful_shutdown_timeout_seconds = 30
health_check_interval_seconds = 60

[maintenance]
full_scan_interval_hours = 12
incremental_scan_interval_minutes = 10
batch_size = 500
max_concurrent_symbols = 5
data_retention_days = 180
enable_auto_repair = false
repair_retry_count = 2

[cache_sync]
sync_interval_seconds = 120
warmup_days = 3
enable_smart_warmup = false
max_cache_size_mb = 512

[cache_sync.ttl_strategy.Fixed]
hours = 12

[gap_detection]
intervals = ["1m", "5m"]
batch_window_days = 3
enable_smart_sampling = false
sampling_rate = 0.3
severe_gap_threshold = 0.7

[monitoring]
enable_metrics = false
metrics_interval_seconds = 120

[monitoring.alerts]
enabled = false
gap_detection_threshold = 20
sync_delay_threshold_minutes = 20
error_rate_threshold = 0.1

[monitoring.logging]
level = "debug"
structured = false
rotation_size_mb = 50
retention_days = 14

[performance]
db_pool_size = 10
cache_pool_size = 5
enable_compression = false

[exchanges.binance]
name = "Binance"
base_url = "https://api.binance.com"
timeout_seconds = 20
rate_limit_per_minute = 600
enabled = true

[exchanges.binance.retry]
max_attempts = 2
base_delay_ms = 500
max_delay_ms = 5000
backoff_strategy = "Linear"
"#;
        
        fs::write(&config_path, test_config).unwrap();
        
        // 测试从文件加载配置
        let config = EnhancedMaintenanceConfig::from_env_and_file(Some(config_path.to_str().unwrap())).unwrap();
        
        assert_eq!(config.environment, Environment::Testing);
        assert_eq!(config.service.name, "test-service");
        assert_eq!(config.service.version, "2.0.0");
        assert_eq!(config.maintenance.full_scan_interval_hours, 12);
        assert_eq!(config.maintenance.incremental_scan_interval_minutes, 10);
        assert_eq!(config.maintenance.batch_size, 500);
        assert!(!config.maintenance.enable_auto_repair);
        assert_eq!(config.gap_detection.intervals, vec!["1m", "5m"]);
        assert_eq!(config.gap_detection.sampling_rate, 0.3);
    }

    #[tokio::test]
    async fn test_config_manager() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("manager_test.toml");
        
        // 创建初始配置
        let initial_config = EnhancedMaintenanceConfig::default();
        let toml_content = toml::to_string_pretty(&initial_config).unwrap();
        fs::write(&config_path, &toml_content).unwrap();
        
        // 创建配置管理器
        let mut manager = ConfigManager::new(Some(config_path.to_str().unwrap())).unwrap();
        
        // 验证初始配置
        assert_eq!(manager.get_config().environment, Environment::Development);
        
        // 修改配置文件
        let mut modified_config = initial_config.clone();
        modified_config.environment = Environment::Production;
        modified_config.maintenance.full_scan_interval_hours = 48;
        
        let modified_toml = toml::to_string_pretty(&modified_config).unwrap();
        
        // 等待一小段时间确保文件时间戳不同
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        fs::write(&config_path, &modified_toml).unwrap();
        
        // 检查并重载配置
        let reloaded = manager.check_and_reload().unwrap();
        assert!(reloaded);
        assert_eq!(manager.get_config().environment, Environment::Production);
        assert_eq!(manager.get_config().maintenance.full_scan_interval_hours, 48);
        
        // 再次检查（应该没有变化）
        let no_change = manager.check_and_reload().unwrap();
        assert!(!no_change);
    }

    #[test]
    fn test_config_summary() {
        let config = EnhancedMaintenanceConfig::default();
        let summary = config.get_config_summary();
        
        assert!(summary.contains("Development"));
        assert!(summary.contains("Standard"));
        assert!(summary.contains("24h"));
        assert!(summary.contains("5m"));
        assert!(summary.contains("60s"));
    }

    #[test]
    fn test_exchange_config() {
        let config = EnhancedMaintenanceConfig::default();
        
        // 验证默认交易所配置
        assert!(config.exchanges.contains_key("binance"));
        let binance_config = &config.exchanges["binance"];
        assert_eq!(binance_config.name, "Binance");
        assert!(binance_config.enabled);
        assert_eq!(binance_config.rate_limit_per_minute, 1200);
        assert_eq!(binance_config.retry.max_attempts, 3);
        assert_eq!(binance_config.retry.backoff_strategy, BackoffStrategy::Exponential);
    }

    #[test]
    fn test_ttl_strategy() {
        let config = EnhancedMaintenanceConfig::default();
        
        match &config.cache_sync.ttl_strategy {
            TtlStrategy::Dynamic { base_hours, decay_factor } => {
                assert_eq!(*base_hours, 24);
                assert_eq!(*decay_factor, 0.1);
            }
            _ => panic!("Expected Dynamic TTL strategy"),
        }
    }

    #[test]
    fn test_startup_modes() {
        let mut config = EnhancedMaintenanceConfig::default();
        
        // 测试不同启动模式
        config.service.startup_mode = StartupMode::Fast;
        assert!(matches!(config.service.startup_mode, StartupMode::Fast));
        
        config.service.startup_mode = StartupMode::Standard;
        assert!(matches!(config.service.startup_mode, StartupMode::Standard));
        
        config.service.startup_mode = StartupMode::Full;
        assert!(matches!(config.service.startup_mode, StartupMode::Full));
    }
}
