# 生产环境配置
environment = "Production"

[service]
name = "market-data-maintenance"
version = "1.0.0"
startup_mode = "Standard"
graceful_shutdown_timeout_seconds = 60
health_check_interval_seconds = 30

[maintenance]
full_scan_interval_hours = 24
incremental_scan_interval_minutes = 5
batch_size = 2000
max_concurrent_symbols = 20
data_retention_days = 730  # 2年数据保留
enable_auto_repair = true
repair_retry_count = 5

[cache_sync]
sync_interval_seconds = 60
warmup_days = 30  # 生产环境预热更多数据
enable_smart_warmup = true
max_cache_size_mb = 4096  # 更大的缓存

[cache_sync.ttl_strategy]
type = "Smart"
min_hours = 12
max_hours = 72

[gap_detection]
intervals = ["1m", "5m", "15m", "1h", "4h", "1d"]
batch_window_days = 14
enable_smart_sampling = true
sampling_rate = 0.05  # 生产环境较低采样率
severe_gap_threshold = 0.9

[exchanges.binance]
name = "Binance"
base_url = "https://api.binance.com"
timeout_seconds = 45
rate_limit_per_minute = 1200
enabled = true

[exchanges.binance.retry]
max_attempts = 5
base_delay_ms = 2000
max_delay_ms = 30000
backoff_strategy = "Exponential"

[monitoring]
enable_metrics = true
metrics_interval_seconds = 30

[monitoring.alerts]
enabled = true
gap_detection_threshold = 5
sync_delay_threshold_minutes = 5
error_rate_threshold = 0.01

[monitoring.logging]
level = "info"
structured = true
rotation_size_mb = 500
retention_days = 90

[performance]
db_pool_size = 50
cache_pool_size = 30
worker_threads = 16
memory_limit_mb = 8192
enable_compression = true
