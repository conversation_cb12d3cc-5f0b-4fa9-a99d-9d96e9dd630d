use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 交易数据实体 - 数据库映射
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct TradeEntity {
    /// 时间戳
    pub time: DateTime<Utc>,
    /// 交易对符号
    pub symbol: String,
    /// 交易ID
    pub trade_id: i64,
    /// 价格
    pub price: Decimal,
    /// 数量
    pub qty: Decimal,
    /// 计价资产数量
    pub quote_qty: Decimal,
    /// 是否为买方主动成交
    pub is_buyer_maker: bool,
    /// 是否为最佳匹配
    pub is_best_match: bool,
} 