use serde::{Deserialize, Serialize};

/// Binance K线数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BinanceKline {
    #[serde(rename = "0")]
    pub open_time: i64,
    #[serde(rename = "1")]
    pub open_price: String,
    #[serde(rename = "2")]
    pub high_price: String,
    #[serde(rename = "3")]
    pub low_price: String,
    #[serde(rename = "4")]
    pub close_price: String,
    #[serde(rename = "5")]
    pub volume: String,
    #[serde(rename = "6")]
    pub close_time: i64,
    #[serde(rename = "7")]
    pub quote_asset_volume: String,
    #[serde(rename = "8")]
    pub number_of_trades: i32,
    #[serde(rename = "9")]
    pub taker_buy_base_asset_volume: String,
    #[serde(rename = "10")]
    pub taker_buy_quote_asset_volume: String,
    #[serde(rename = "11")]
    pub ignore: String,
}

/// Binance 价格数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BinanceTicker {
    pub symbol: String,
    pub price: String,
}

/// Binance 深度数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BinanceOrderBook {
    #[serde(rename = "lastUpdateId")]
    pub last_update_id: i64,
    pub bids: Vec<[String; 2]>,
    pub asks: Vec<[String; 2]>,
}

/// Binance 24小时统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Binance24hrStats {
    pub symbol: String,
    #[serde(rename = "priceChange")]
    pub price_change: String,
    #[serde(rename = "priceChangePercent")]
    pub price_change_percent: String,
    #[serde(rename = "weightedAvgPrice")]
    pub weighted_avg_price: String,
    #[serde(rename = "prevClosePrice")]
    pub prev_close_price: String,
    #[serde(rename = "lastPrice")]
    pub last_price: String,
    #[serde(rename = "lastQty")]
    pub last_qty: String,
    #[serde(rename = "bidPrice")]
    pub bid_price: String,
    #[serde(rename = "askPrice")]
    pub ask_price: String,
    #[serde(rename = "openPrice")]
    pub open_price: String,
    #[serde(rename = "highPrice")]
    pub high_price: String,
    #[serde(rename = "lowPrice")]
    pub low_price: String,
    pub volume: String,
    #[serde(rename = "quoteVolume")]
    pub quote_volume: String,
    #[serde(rename = "openTime")]
    pub open_time: i64,
    #[serde(rename = "closeTime")]
    pub close_time: i64,
    #[serde(rename = "firstId")]
    pub first_id: i64,
    #[serde(rename = "lastId")]
    pub last_id: i64,
    pub count: i64,
}

/// Binance 交易数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceTrade {
    pub id: i64,
    pub price: String,
    pub qty: String,
    #[serde(rename = "quoteQty")]
    pub quote_qty: String,
    pub time: i64,
    #[serde(rename = "isBuyerMaker")]
    pub is_buyer_maker: bool,
    #[serde(rename = "isBestMatch")]
    pub is_best_match: bool,
} 