#![allow(warnings)]
#[global_allocator]
static GLOBAL: mimalloc::MiMalloc = mimalloc::MiMalloc;
use anyhow::Result;
use axum::Router;
use dotenvy::dotenv;
use std::{net::SocketAddr, sync::Arc};
use tower_http::{
    compression::CompressionLayer,
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
};
use tracing::error;
use tracing::log::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::OpenApi;
use utoipa::openapi::security::{SecurityRequirement, SecurityScheme};
use utoipa::openapi::security::HttpBuilder;
use common::log::init_simple_logging;
use nacos::init_all_nacos;


use service::{
    // 导入简化维护服务
    MarketDataService,
    MaintenanceConfig,
    TimescaleStorageManager,
};
use service::market_data::service::MarketDataServiceImpl;
use user_server::ApiDoc;
use user_server::config::AppConfig;
use user_server::router::router::init_app;
use repository::connection::timescale::TimescalePoolManager;
use repository::connection::redis::{RedisPoolManager, RedisOperations};
use repository::{CacheRepository, RedisCacheRepository};
use common::{TRADERDATABASE_CONFIG, REDIS_CONFIG};

pub const APP_NAME: &str = "user-server";
pub const SERVER_ADDRESS: &str = "0.0.0.0:8091";
#[tokio::main]
async fn main() {
    // 加载环境变量
    dotenv().ok();

    // 初始化日志
    init_simple_logging().expect("初始化日志失败");
    // tracing_subscriber::registry()
    //     .with(tracing_subscriber::EnvFilter::from_default_env())
    //     .with(tracing_subscriber::fmt::layer())
    //     .init();
    tracing::info!("🚀 启动用户服务器...");
    let nacos_client = init_all_nacos(APP_NAME).await.expect("Nacos init failed");
    
    // 等待一小段时间确保Nacos配置完全加载
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    tracing::info!("✅ Nacos配置加载完成");
    
    // 加载配置
    let app_config = AppConfig::from_env();
    
    // 构建应用
    let app = init_app().await;
    // 启动服务器
    let addr: SocketAddr = app_config.listen_addr.parse().expect("listen_addr parse failed");
    tracing::warn!("🌐 服务器启动在: http://{}", addr);
    tracing::warn!("📚 API文档: http://{}/swagger-ui", addr);

    let server_handle = tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(addr).await.expect("failed to bind address");
        axum::serve(listener, app).await.expect("AxumServer start failed!");
    });

    // 等待各任务完成
    tokio::select! {
        _ = tokio::signal::ctrl_c() => {
            info!("Ctrl-C received. Initiating shutdown...");
            // Gracefully shutdown Nacos
            if let Err(e) = nacos_client.read().await.deregister_service().await {
                error!("Failed to deregister service from Nacos: {}", e);
            } else {
                info!("Service deregistered from Nacos successfully.");
            }
        }
        // 初始化并运行市场数据维护服务
        maintenance_result = init_maintenance_service() => {
            match maintenance_result {
                Ok(_) => info!("Market data maintenance service completed successfully."),
                Err(e) => error!("Market data maintenance service failed: {:?}", e),
            }
            info!("Market data maintenance service has stopped.");
        }

        // Wait for the HTTP server to complete or error
        res_server = server_handle => {
            match res_server {
                Ok(_) => info!("HTTP Server task completed."), // This usually means it was shut down
                Err(e) => error!("HTTP Server task encountered an error: {:?}", e),
            }
            info!("HTTP server has stopped.");
        }

    };
    info!("Application main finished.");
}

/// 初始化并运行市场数据维护服务
async fn init_maintenance_service() -> Result<()> {
    use repository::connection::timescale::TimescalePoolManager;
    use repository::connection::redis::{RedisPoolManager, RedisOperations};
    use repository::{CacheRepository, RedisCacheRepository};
    use common::{TRADERDATABASE_CONFIG, REDIS_CONFIG};
    
    tracing::info!("🔧 初始化市场数据维护服务...");
    
    // 等待数据库配置可用
    let mut retry_count = 0;
    let timescale_config = loop {
        if let Some(config) = TRADERDATABASE_CONFIG.get() {
            tracing::info!("✅ 获取到TimescaleDB配置");
            break config;
        }
        
        retry_count += 1;
        if retry_count > 10 {
            tracing::error!("❌ 等待TimescaleDB配置超时");
            return Err(anyhow::anyhow!("DATABASE_CONFIG not initialized after 10 retries"));
        }
        
        tracing::warn!("⏳ 等待TimescaleDB配置加载... (尝试 {}/10)", retry_count);
        tokio::time::sleep(tokio::time::Duration::from_millis(5000)).await;
    };
    
    // 等待Redis配置可用
    let redis_config = loop {
        if let Some(config) = REDIS_CONFIG.get() {
            tracing::info!("✅ 获取到Redis配置");
            break config;
        }
        
        retry_count += 1;
        if retry_count > 10 {
            tracing::error!("❌ 等待Redis配置超时");
            return Err(anyhow::anyhow!("REDIS_CONFIG not initialized after 10 retries"));
        }
        
        tracing::warn!("⏳ 等待Redis配置加载... (尝试 {}/10)", retry_count);
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    };
    
    tracing::info!("🔗 创建TimescaleDB连接池...");
    // 创建数据库连接池
    let timescale_pool = TimescalePoolManager::new(timescale_config.clone()).await?;
    let pool = timescale_pool.pool().clone();
    tracing::info!("✅ TimescaleDB连接池创建成功");
    
    // 创建Redis缓存仓库
    tracing::info!("🔗 创建Redis缓存连接...");
    let redis_pool_manager = RedisPoolManager::new(redis_config.clone()).await?;
    let redis_ops = RedisOperations::new(redis_pool_manager);
    let cache_repo = Arc::new(RedisCacheRepository::new(redis_ops)) as Arc<dyn CacheRepository>;
    tracing::info!("✅ Redis缓存连接创建成功");
    
    // 创建存储管理器
    let storage_manager = std::sync::Arc::new(
        TimescaleStorageManager::new(pool).await?
    );
    
    // 创建维护配置
    let maintenance_config = MaintenanceConfig::from_env();
    
    // 创建维护服务
    let mut maintenance_service = MarketDataService::with_maintenance(
        maintenance_config,
        storage_manager.clone(),
        cache_repo,
    ).await?;
    
    tracing::info!("✅ 市场数据维护服务初始化完成");
    tracing::info!("🚀 启动市场数据维护服务...");
    
    // 直接运行维护服务（这将是一个长期运行的任务）
    maintenance_service.start_maintenance().await?;
    
    tracing::info!("市场数据维护服务正常退出");
    Ok(())
}




