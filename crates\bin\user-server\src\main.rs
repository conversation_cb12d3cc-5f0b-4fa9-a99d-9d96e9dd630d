#![allow(warnings)]
#[global_allocator]
static GLOBAL: mimalloc::MiMalloc = mimalloc::MiMalloc;
use anyhow::Result;
use axum::Router;
use dotenvy::dotenv;
use std::{net::SocketAddr, sync::Arc};
use tower_http::{
    compression::CompressionLayer,
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
};
use tracing::error;
use tracing::log::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::OpenApi;
use utoipa::openapi::security::{SecurityRequirement, SecurityScheme};
use utoipa::openapi::security::HttpBuilder;
use common::log::init_simple_logging;
use nacos::init_all_nacos;

use user_server::ApiDoc;
use user_server::config::AppConfig;
use user_server::router::router::init_app;

pub const APP_NAME: &str = "user-server";
pub const SERVER_ADDRESS: &str = "0.0.0.0:8091";

#[tokio::main]
async fn main() {
    // 加载环境变量
    dotenv().ok();

    // 初始化日志
    init_simple_logging().expect("初始化日志失败");
    // tracing_subscriber::registry()
    //     .with(tracing_subscriber::EnvFilter::from_default_env())
    //     .with(tracing_subscriber::fmt::layer())
    //     .init();
    tracing::info!("🚀 启动用户服务器...");
    
    let nacos_client = init_all_nacos(APP_NAME).await.expect("Nacos init failed");
    
    // 等待一小段时间确保Nacos配置完全加载
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    tracing::info!("✅ Nacos配置加载完成");
    
    // 加载配置
    let app_config = AppConfig::from_env();
    
    // 构建应用（包含市场数据管理器的初始化）
    let app = init_app().await;
    
    // 启动服务器
    let addr: SocketAddr = app_config.listen_addr.parse().expect("listen_addr parse failed");
    tracing::warn!("🌐 服务器启动在: http://{}", addr);
    tracing::warn!("📚 API文档: http://{}/swagger-ui", addr);

    let server_handle = tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(addr).await.expect("failed to bind address");
        axum::serve(listener, app).await.expect("AxumServer start failed!");
    });

    // 启动市场数据服务后台任务
    let market_data_handle = tokio::spawn(async move {
        if let Err(e) = run_market_data_services().await {
            error!("市场数据服务运行失败: {:?}", e);
        }
    });

    // 等待各任务完成
    tokio::select! {
        _ = tokio::signal::ctrl_c() => {
            info!("Ctrl-C received. Initiating shutdown...");
            // Gracefully shutdown Nacos
            if let Err(e) = nacos_client.read().await.deregister_service().await {
                error!("Failed to deregister service from Nacos: {}", e);
            } else {
                info!("Service deregistered from Nacos successfully.");
            }
        }

        // 监控市场数据服务
        result = market_data_handle => {
            match result {
                Ok(_) => info!("市场数据服务正常退出"),
                Err(e) => error!("市场数据服务异常退出: {:?}", e),
            }
        }

        // Wait for the HTTP server to complete or error
        res_server = server_handle => {
            match res_server {
                Ok(_) => info!("HTTP Server task completed."), // This usually means it was shut down
                Err(e) => error!("HTTP Server task encountered an error: {:?}", e),
            }
            info!("HTTP server has stopped.");
        }
    };
    
    info!("Application main finished.");
}

/// 运行市场数据服务
/// 包括全量扫描、增量更新等后台任务
async fn run_market_data_services() -> Result<()> {
    use service::initialize_and_start_app_state;
    
    tracing::info!("🔧 启动市场数据服务后台任务...");
    
    // 初始化service状态（如果还没有初始化）
    let service_state = initialize_and_start_app_state().await
        .map_err(|e| anyhow::anyhow!("初始化服务状态失败: {}", e))?;
    
    tracing::info!("✅ 市场数据管理器已启动，开始后台服务...");
    
    // 获取市场数据管理器
    let manager = service_state.market_data_manager.clone();
    
    // 🚀 启动时立即执行一次服务状态检查
    tracing::info!("🔍 执行启动时服务状态检查...");
    match manager.get_service_status().await {
        Ok(status) => {
            tracing::info!("✅ 启动状态检查完成: 总交易对{}, 健康{}, 警告{}, 严重{}",
                status.total_symbols, status.healthy_symbols, status.warning_symbols, status.critical_symbols);
        }
        Err(e) => {
            tracing::error!("❌ 启动状态检查失败: {}", e);
            // 不要因为启动检查失败就退出，继续运行定期任务
        }
    }
    
    // 定期执行健康检查和统计报告
    let health_check_interval = tokio::time::Duration::from_secs(300); // 5分钟
    let mut health_check_timer = tokio::time::interval(health_check_interval);
    
    // 定期执行缓存清理
    let cache_cleanup_interval = tokio::time::Duration::from_secs(3600); // 1小时
    let mut cache_cleanup_timer = tokio::time::interval(cache_cleanup_interval);
    
    // 定期执行完整性检查
    let integrity_check_interval = tokio::time::Duration::from_secs(86400); // 24小时
    let mut integrity_check_timer = tokio::time::interval(integrity_check_interval);
    
    // 定期执行增量更新（每5分钟，补充自动更新）
    let manual_update_interval = tokio::time::Duration::from_secs(300); // 5分钟
    let mut manual_update_timer = tokio::time::interval(manual_update_interval);
    
    tracing::info!("📊 市场数据服务定时任务已配置:");
    tracing::info!("  - 健康检查: 每5分钟");
    tracing::info!("  - 手动增量更新: 每5分钟");
    tracing::info!("  - 缓存清理: 每1小时");
    tracing::info!("  - 完整性检查: 每24小时");
    
    loop {
        tokio::select! {
            _ = health_check_timer.tick() => {
                // 健康检查和统计报告
                match manager.get_service_status().await {
                    Ok(status) => {
                        let total_symbols = status.total_symbols;
                        let healthy_symbols = status.healthy_symbols;
                        tracing::info!("💚 健康检查通过: {}/{} 交易对正常", healthy_symbols, total_symbols);

                        if status.warning_symbols > 0 {
                            tracing::warn!("⚠️ {} 个交易对有警告", status.warning_symbols);
                        }
                        if status.critical_symbols > 0 {
                            tracing::error!("❌ {} 个交易对状态严重", status.critical_symbols);
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 健康检查失败: {}", e);
                    }
                }
            }
            
            _ = manual_update_timer.tick() => {
                // 手动触发状态检查（替代增量更新）
                tracing::debug!("🔄 执行状态检查...");
                match manager.get_service_status().await {
                    Ok(status) => {
                        tracing::debug!("✅ 状态检查完成: 总交易对{}", status.total_symbols);
                    }
                    Err(e) => {
                        tracing::error!("❌ 状态检查失败: {}", e);
                    }
                }
            }
            
            _ = cache_cleanup_timer.tick() => {
                // 定期状态报告
                tracing::info!("📊 定期状态报告...");
                match manager.get_service_status().await {
                    Ok(status) => {
                        tracing::info!("📈 服务状态报告:");
                        tracing::info!("   总交易对: {}", status.total_symbols);
                        tracing::info!("   健康: {}", status.healthy_symbols);
                        tracing::info!("   警告: {}", status.warning_symbols);
                        tracing::info!("   严重: {}", status.critical_symbols);
                        tracing::info!("   最后更新: {}", status.last_update);
                    }
                    Err(e) => {
                        tracing::error!("❌ 状态报告失败: {}", e);
                    }
                }
            }
            
            _ = integrity_check_timer.tick() => {
                // 详细状态检查
                tracing::info!("🔍 开始详细状态检查...");
                match manager.get_service_status().await {
                    Ok(status) => {
                        tracing::info!("✅ 详细状态检查完成:");
                        tracing::info!("   配置信息: 交易所={}, 更新间隔={}分钟",
                            status.config.exchange_name, status.config.update_interval_minutes);
                        tracing::info!("   数据保留: {}天, 自动修复={}, 缓存启用={}",
                            status.config.retention_days, status.config.enable_auto_fix, status.config.enable_cache);

                        if status.critical_symbols > 0 {
                            tracing::error!("⚠️ 发现 {} 个严重问题的交易对，建议检查", status.critical_symbols);
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 详细状态检查失败: {}", e);
                    }
                }
            }
        }
    }
}




