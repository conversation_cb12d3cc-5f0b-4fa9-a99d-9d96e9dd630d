#![allow(warnings)]
#[global_allocator]
static GLOBAL: mimalloc::MiMalloc = mimalloc::MiMalloc;
use anyhow::Result;
use axum::Router;
use dotenvy::dotenv;
use std::{net::SocketAddr, sync::Arc};
use tower_http::{
    compression::CompressionLayer,
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
};
use tracing::error;
use tracing::log::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::OpenApi;
use utoipa::openapi::security::{SecurityRequirement, SecurityScheme};
use utoipa::openapi::security::HttpBuilder;
use common::log::init_simple_logging;
use nacos::init_all_nacos;

use user_server::ApiDoc;
use user_server::config::AppConfig;
use user_server::router::router::init_app;

pub const APP_NAME: &str = "user-server";
pub const SERVER_ADDRESS: &str = "0.0.0.0:8091";

#[tokio::main]
async fn main() {
    // 加载环境变量
    dotenv().ok();

    // 初始化日志
    init_simple_logging().expect("初始化日志失败");
    // tracing_subscriber::registry()
    //     .with(tracing_subscriber::EnvFilter::from_default_env())
    //     .with(tracing_subscriber::fmt::layer())
    //     .init();
    tracing::info!("🚀 启动用户服务器...");
    
    let nacos_client = init_all_nacos(APP_NAME).await.expect("Nacos init failed");
    
    // 等待一小段时间确保Nacos配置完全加载
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    tracing::info!("✅ Nacos配置加载完成");
    
    // 加载配置
    let app_config = AppConfig::from_env();
    
    // 构建应用（包含市场数据管理器的初始化）
    let app = init_app().await;
    
    // 启动服务器
    let addr: SocketAddr = app_config.listen_addr.parse().expect("listen_addr parse failed");
    tracing::warn!("🌐 服务器启动在: http://{}", addr);
    tracing::warn!("📚 API文档: http://{}/swagger-ui", addr);

    let server_handle = tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(addr).await.expect("failed to bind address");
        axum::serve(listener, app).await.expect("AxumServer start failed!");
    });

    // 启动市场数据服务后台任务
    let market_data_handle = tokio::spawn(async move {
        if let Err(e) = run_market_data_services().await {
            error!("市场数据服务运行失败: {:?}", e);
        }
    });

    // 等待各任务完成
    tokio::select! {
        _ = tokio::signal::ctrl_c() => {
            info!("Ctrl-C received. Initiating shutdown...");
            // Gracefully shutdown Nacos
            if let Err(e) = nacos_client.read().await.deregister_service().await {
                error!("Failed to deregister service from Nacos: {}", e);
            } else {
                info!("Service deregistered from Nacos successfully.");
            }
        }

        // 监控市场数据服务
        result = market_data_handle => {
            match result {
                Ok(_) => info!("市场数据服务正常退出"),
                Err(e) => error!("市场数据服务异常退出: {:?}", e),
            }
        }

        // Wait for the HTTP server to complete or error
        res_server = server_handle => {
            match res_server {
                Ok(_) => info!("HTTP Server task completed."), // This usually means it was shut down
                Err(e) => error!("HTTP Server task encountered an error: {:?}", e),
            }
            info!("HTTP server has stopped.");
        }
    };
    
    info!("Application main finished.");
}

/// 运行市场数据服务
/// 包括全量扫描、增量更新等后台任务
async fn run_market_data_services() -> Result<()> {
    use service::initialize_and_start_app_state;
    
    tracing::info!("🔧 启动市场数据服务后台任务...");
    
    // 初始化service状态（如果还没有初始化）
    let service_state = initialize_and_start_app_state().await
        .map_err(|e| anyhow::anyhow!("初始化服务状态失败: {}", e))?;
    
    tracing::info!("✅ 市场数据管理器已启动，开始后台服务...");
    
    // 获取市场数据管理器
    let manager = service_state.market_data_manager.clone();
    
    // 🚀 启动时立即执行一次完整性检查和数据同步
    tracing::info!("🔍 执行启动时数据检查...");
    match manager.perform_startup_tasks().await {
        Ok(_) => {
            tracing::info!("✅ 启动数据检查完成");
        }
        Err(e) => {
            tracing::error!("❌ 启动数据检查失败: {}", e);
            // 不要因为启动检查失败就退出，继续运行定期任务
        }
    }
    
    // 定期执行健康检查和统计报告
    let health_check_interval = tokio::time::Duration::from_secs(300); // 5分钟
    let mut health_check_timer = tokio::time::interval(health_check_interval);
    
    // 定期执行缓存清理
    let cache_cleanup_interval = tokio::time::Duration::from_secs(3600); // 1小时
    let mut cache_cleanup_timer = tokio::time::interval(cache_cleanup_interval);
    
    // 定期执行完整性检查
    let integrity_check_interval = tokio::time::Duration::from_secs(86400); // 24小时
    let mut integrity_check_timer = tokio::time::interval(integrity_check_interval);
    
    // 定期执行增量更新（每5分钟，补充自动更新）
    let manual_update_interval = tokio::time::Duration::from_secs(300); // 5分钟
    let mut manual_update_timer = tokio::time::interval(manual_update_interval);
    
    tracing::info!("📊 市场数据服务定时任务已配置:");
    tracing::info!("  - 健康检查: 每5分钟");
    tracing::info!("  - 手动增量更新: 每5分钟");
    tracing::info!("  - 缓存清理: 每1小时");
    tracing::info!("  - 完整性检查: 每24小时");
    
    loop {
        tokio::select! {
            _ = health_check_timer.tick() => {
                // 健康检查和统计报告
                match manager.health_check().await {
                    Ok(health_status) => {
                        let healthy_services = health_status.values().filter(|&&v| v).count();
                        let total_services = health_status.len();
                        tracing::info!("💚 健康检查通过: {}/{} 服务正常", healthy_services, total_services);
                        
                        // 获取服务统计
                        if let Ok(stats) = manager.get_service_statistics().await {
                            tracing::debug!("📈 服务统计: {:?}", stats);
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 健康检查失败: {}", e);
                    }
                }
            }
            
            _ = manual_update_timer.tick() => {
                // 手动触发增量更新
                tracing::debug!("🔄 执行增量更新...");
                match manager.trigger_manual_update().await {
                    Ok(results) => {
                        if !results.is_empty() {
                            tracing::info!("✅ 增量更新完成: 更新了 {} 个结果", results.len());
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 增量更新失败: {}", e);
                    }
                }
            }
            
            _ = cache_cleanup_timer.tick() => {
                // 缓存清理
                tracing::info!("🧹 开始缓存清理...");
                match manager.cleanup_expired_cache().await {
                    Ok(cleaned_count) => {
                        if cleaned_count > 0 {
                            tracing::info!("✅ 缓存清理完成，清理了 {} 个过期项", cleaned_count);
                        } else {
                            tracing::debug!("✅ 缓存清理完成，无过期项");
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 缓存清理失败: {}", e);
                    }
                }
            }
            
            _ = integrity_check_timer.tick() => {
                // 完整性检查
                tracing::info!("🔍 开始定期数据完整性检查...");
                match manager.perform_full_integrity_check().await {
                    Ok(results) => {
                        let total_checks = results.len();
                        let incomplete_count = results.iter()
                            .filter(|r| r.completeness_percentage < 100.0)
                            .count();
                        
                        tracing::info!("✅ 完整性检查完成: 检查了 {} 个项目，{} 个需要补充数据", 
                            total_checks, incomplete_count);
                        
                        // 记录详细的完整性信息
                        for result in &results {
                            if result.completeness_percentage < 100.0 {
                                tracing::warn!("⚠️  {} {} 完整性: {:.2}% ({}/{})", 
                                    result.symbol, 
                                    result.interval.to_binance_string(),
                                    result.completeness_percentage,
                                    result.actual_count,
                                    result.expected_count
                                );
                            }
                        }
                    }
                    Err(e) => {
                        tracing::error!("❌ 完整性检查失败: {}", e);
                    }
                }
            }
        }
    }
}




