pub mod health;
pub mod market_data;
pub mod debug;
pub mod auth;
pub mod maintenance;
use axum::Router;

use crate::api::{
    auth::auth_routes,
    debug::debug_routes,
    health::health_routes,
    market_data::market_data_routes,
    maintenance::maintenance_routes,
};
use crate::router::AppState;

/// 创建 API v1 路由
pub fn create_api_v1_router(
) ->  Router<AppState> {
    // 创建需要认证的路由 (暂时移除中间件，稍后修复)
    let protected_routes = Router::new()
        .merge(health_routes())
        .layer(axum::middleware::from_fn(crate::router::auth_jwt::auth_middleware));

    // 创建公开路由（不需要认证）
    let public_routes = Router::new()
        .merge(debug_routes())
        .merge(market_data_routes())
        .nest("/maintenance", maintenance_routes());

    // 合并所有路由
    Router::new()
        .merge(protected_routes)
        .nest("/v1", public_routes)
        .nest("/auth", auth_routes())
}
