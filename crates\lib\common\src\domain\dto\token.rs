use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use axum::{response::IntoResponse, Json, http::StatusCode};
use serde_json::json;

/// JWT Claims 结构体 - 统一定义
/// 包含所有必要的标准JWT字段和自定义字段
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct Claims {
    /// 用户ID (subject)
    pub sub: String,
    /// 用户邮箱
    pub email: String,
    /// 用户昵称
    pub nickname: String,
    /// 过期时间 (expiration time)
    pub exp: usize,
    /// 签发时间 (issued at)
    pub iat: usize,
    /// 不早于时间 (not before)
    pub nbf: usize,
    /// 签发者 (issuer)
    pub iss: String,
    /// 接收者 (audience)
    pub aud: String,
    /// JWT唯一标识符
    pub jti: String,
    /// 用户角色
    pub roles: Vec<String>,
    /// 用户权限
    pub permissions: Vec<String>,
}

/// 刷新令牌Claims结构体
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct RefreshClaims {
    /// 用户ID
    pub sub: String,
    /// 过期时间 (30天)
    pub exp: usize,
    /// 签发时间
    pub iat: usize,
    /// 签发者
    pub iss: String,
    /// 令牌类型
    pub token_type: String, // "refresh"
    /// JWT唯一标识符
    pub jti: String,
}

/// JWT 认证错误
#[derive(Debug)]
pub enum AuthError {
    /// 缺少认证令牌
    MissingToken,
    /// 无效的认证令牌
    InvalidToken(String),
    /// 令牌已过期
    ExpiredToken,
    /// 其他错误
    Other(String),
}

impl std::fmt::Display for AuthError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuthError::MissingToken => write!(f, "Missing authentication token"),
            AuthError::InvalidToken(msg) => write!(f, "Invalid token: {}", msg),
            AuthError::ExpiredToken => write!(f, "Token has expired"),
            AuthError::Other(msg) => write!(f, "Authentication error: {}", msg),
        }
    }
}

impl std::error::Error for AuthError {}

impl IntoResponse for AuthError {
    fn into_response(self) -> axum::response::Response {
        let (status, error_message) = match self {
            AuthError::MissingToken => (StatusCode::UNAUTHORIZED, "Missing authentication token".to_string()),
            AuthError::InvalidToken(msg) => (StatusCode::UNAUTHORIZED, msg),
            AuthError::ExpiredToken => (StatusCode::UNAUTHORIZED, "Token has expired".to_string()),
            AuthError::Other(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
        };

        let body = Json(json!({
            "error": error_message,
            "code": status.as_u16()
        }));

        (status, body).into_response()
    }
}

/// JWT常量定义
pub mod constants {
    /// JWT签发者
    pub const JWT_ISSUER: &str = "market-data-server";
    /// JWT受众
    pub const JWT_AUDIENCE: &str = "market-data-client";
    /// 访问令牌过期时间（24小时，单位：秒）
    pub const ACCESS_TOKEN_EXPIRY: i64 = 86400;
    /// 刷新令牌过期时间（30天，单位：秒）
    pub const REFRESH_TOKEN_EXPIRY: i64 = 2592000;
    /// 环境变量：JWT密钥
    pub const JWT_SECRET_ENV: &str = "JWT_SECRET";
    /// 开发环境默认密钥
    pub const JWT_SECRET_DEFAULT: &str = "dev-secret-key-should-be-changed-in-production";
}

#[cfg(test)]
mod tests {
    use super::*;
    use jsonwebtoken::{encode, decode, EncodingKey, DecodingKey, Header, Validation};
    use chrono::{Utc, Duration};
    use uuid::Uuid;

    #[test]
    fn test_claims_serialization() {
        let now = Utc::now();
        let claims = Claims {
            sub: "user123".to_string(),
            email: "<EMAIL>".to_string(),
            nickname: "testuser".to_string(),
            exp: (now + Duration::seconds(constants::ACCESS_TOKEN_EXPIRY)).timestamp() as usize,
            iat: now.timestamp() as usize,
            nbf: now.timestamp() as usize,
            iss: constants::JWT_ISSUER.to_string(),
            aud: constants::JWT_AUDIENCE.to_string(),
            jti: Uuid::new_v4().to_string(),
            roles: vec!["user".to_string()],
            permissions: vec!["read".to_string()],
        };

        // 测试JWT编码和解码
        let secret = constants::JWT_SECRET_DEFAULT;
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        ).expect("JWT编码失败");

        let mut validation = Validation::default();
        validation.set_issuer(&[constants::JWT_ISSUER]);
        validation.set_audience(&[constants::JWT_AUDIENCE]);

        let decoded = decode::<Claims>(
            &token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        ).expect("JWT解码失败");

        assert_eq!(decoded.claims.sub, claims.sub);
        assert_eq!(decoded.claims.email, claims.email);
        assert_eq!(decoded.claims.nickname, claims.nickname);
        assert_eq!(decoded.claims.iss, claims.iss);
        assert_eq!(decoded.claims.aud, claims.aud);
    }

    #[test]
    fn test_refresh_claims_serialization() {
        let now = Utc::now();
        let refresh_claims = RefreshClaims {
            sub: "user123".to_string(),
            exp: (now + Duration::seconds(constants::REFRESH_TOKEN_EXPIRY)).timestamp() as usize,
            iat: now.timestamp() as usize,
            iss: constants::JWT_ISSUER.to_string(),
            token_type: "refresh".to_string(),
            jti: Uuid::new_v4().to_string(),
        };

        // 测试刷新令牌JWT编码和解码
        let secret = constants::JWT_SECRET_DEFAULT;
        let token = encode(
            &Header::default(),
            &refresh_claims,
            &EncodingKey::from_secret(secret.as_bytes()),
        ).expect("刷新令牌编码失败");

        let mut validation = Validation::default();
        validation.set_issuer(&[constants::JWT_ISSUER]);

        let decoded = decode::<RefreshClaims>(
            &token,
            &DecodingKey::from_secret(secret.as_bytes()),
            &validation,
        ).expect("刷新令牌解码失败");

        assert_eq!(decoded.claims.sub, refresh_claims.sub);
        assert_eq!(decoded.claims.token_type, refresh_claims.token_type);
        assert_eq!(decoded.claims.iss, refresh_claims.iss);
    }

    #[test]
    fn test_constants_values() {
        assert_eq!(constants::JWT_ISSUER, "market-data-server");
        assert_eq!(constants::JWT_AUDIENCE, "market-data-client");
        assert_eq!(constants::ACCESS_TOKEN_EXPIRY, 86400); // 24小时
        assert_eq!(constants::REFRESH_TOKEN_EXPIRY, 2592000); // 30天
        assert_eq!(constants::JWT_SECRET_ENV, "JWT_SECRET");
        assert!(constants::JWT_SECRET_DEFAULT.contains("dev-secret"));
    }
}

