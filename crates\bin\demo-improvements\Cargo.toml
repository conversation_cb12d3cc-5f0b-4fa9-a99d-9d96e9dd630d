[package]
name = "demo-improvements"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "demo-improvements"
path = "src/main.rs"

[dependencies]
# 核心依赖
tokio = { workspace = true, features = ["full"] }
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
clap = { workspace = true, features = ["derive"] }

# 本地依赖
service = { path = "../../lib/service" }
repository = { path = "../../lib/repository" }
exchange = { path = "../../lib/exchange" }
