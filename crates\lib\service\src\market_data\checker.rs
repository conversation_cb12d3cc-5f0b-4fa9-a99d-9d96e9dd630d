use chrono::{DateTime, Duration, Utc};
use repository::{
    connection::timescale::TimescalePoolManager,
    timescale::{KlineRepository, SymbolRepository},
    RepositoryError,
};
use crate::market_data::{IntegrityCheckResult, KlineInterval};
// 移除重复的导入

/// 数据完整性检查器
#[derive(Clone)]
pub struct DataChecker {
    kline_repo: KlineRepository,
}

impl DataChecker {
    /// 创建新的数据检查器
    pub fn new(pool: &TimescalePoolManager) -> Self {
        Self {
            kline_repo: KlineRepository::new(pool.pool().clone()),
        }
    }

    /// 检查单个交易对和时间间隔的数据完整性
    pub async fn check_symbol_integrity(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<IntegrityCheckResult> {
        let interval_str = interval.to_binance_string();
        let bucket_interval = interval.to_timescale_bucket_string();

        // 1. 获取数据库中实际存在的数据统计
        let stats = self.kline_repo
            .analyze_data_stats(symbol, interval_str, start_time, end_time)
            .await?;

        // 2. 计算理论上预期的数据点数量
        let expected_count = self.calculate_expected_data_points(interval, start_time, end_time);
        let actual_count = stats.total_records;
        
        // 3. 识别缺失的时间段
        let missing_periods = if let (Some(first_kline), Some(last_kline)) = (stats.earliest_time, stats.latest_time) {
            // 策略: 分三段检查
            // a. 检查配置的开始时间到第一条数据之间
            let mut periods = Vec::new();
            if start_time < first_kline {
                periods.push((start_time, first_kline));
            }

            // b. 检查数据内部的缝隙
            let internal_missing = self.identify_internal_gaps(
                symbol,
                interval,
                bucket_interval,
                first_kline,
                last_kline,
            ).await?;
            periods.extend(internal_missing);

            // c. 检查最后一条数据到检查的结束时间之间
            if last_kline < end_time {
                periods.push((last_kline, end_time));
            }
            
            periods
        } else {
            // 如果数据库中完全没有这个交易对的数据，则整个时间范围都是缺失的
            log::warn!("在 {} 到 {} 范围内没有找到 {} 的任何数据，将整个范围标记为缺失", start_time, end_time, symbol);
            vec![(start_time, end_time)]
        };

        // 4. 计算完整性百分比
        let completeness_percentage = if expected_count > 0 {
            (actual_count as f64 / expected_count as f64) * 100.0
        } else {
            // 如果预期为0（例如时间范围小于一个间隔），且实际也为0，则为100%
            if actual_count == 0 { 100.0 } else { 0.0 }
        };

        Ok(IntegrityCheckResult {
            symbol: symbol.to_string(),
            interval: interval.clone(),
            start_time,
            end_time,
            missing_periods,
            expected_count,
            actual_count,
            completeness_percentage,
        })
    }

    /// 计算预期的数据点数量
    fn calculate_expected_data_points(
        &self,
        interval: &KlineInterval,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> i64 {
        let duration = end_time - start_time;
        // 确保时长为正
        if duration <= Duration::zero() {
            return 0;
        }
        let interval_minutes = interval.to_minutes();
        if interval_minutes == 0 { return 0; }
        
        // 向上取整，以确保即使是部分间隔也被计入
        (duration.num_minutes() as f64 / interval_minutes as f64).ceil() as i64
    }

    /// 使用 time_bucket_gapfill 精确识别数据区间的内部缝隙
    async fn identify_internal_gaps(
        &self,
        symbol: &str,
        interval: &KlineInterval,
        bucket_interval: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<(DateTime<Utc>, DateTime<Utc>)>> {
        
        let time_buckets = self.kline_repo
            .find_missing_time_buckets(symbol, interval.to_binance_string(), bucket_interval, start_time, end_time)
            .await?;

        let mut missing_ranges = Vec::new();
        let mut current_missing_start: Option<DateTime<Utc>> = None;
        let interval_duration = Duration::minutes(interval.to_minutes());

        for bucket in time_buckets {
            if bucket.data_count.unwrap_or(0) == 0 {
                // 当前时间桶缺失数据
                if current_missing_start.is_none() {
                    current_missing_start = Some(bucket.minute_bucket);
                }
            } else {
                // 当前时间桶有数据，终结前一个缺失范围
                if let Some(start) = current_missing_start.take() {
                    // 结束时间是当前桶的开始时间
                    let end = bucket.minute_bucket;
                    missing_ranges.push((start, end));
                }
            }
        }
        
        // 如果循环结束后仍然有未终结的缺失范围，这不应该在内部检查中发生，因为end_time是最后一条数据的时间。
        // 但为保险起见，如果发生，则记录一个警告。
        if let Some(start) = current_missing_start {
           log::warn!("内部缝隙检查在数据末尾发现未闭合的缺失段: {} to {}", start, end_time);
           missing_ranges.push((start, end_time));
        }

        Ok(missing_ranges)
    }
}
