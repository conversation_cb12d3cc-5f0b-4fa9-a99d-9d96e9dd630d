use nacos_sdk::api::config::ConfigService;
use nacos_sdk::api::naming::{NamingChangeEvent, NamingEventListener, NamingService};
use nacos_sdk::api::{
    config::{ConfigChangeListener, ConfigResponse},
    naming::{NamingServiceBuilder, ServiceInstance},
    props::ClientProps,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use thiserror::Error;

/// Nacos配置
#[derive(Debug, Deserialize, Clone, Serialize)]
pub struct NacosConfig {
    pub server_addr: String,
    pub namespace: String,
    pub group: String,
    pub username: String,
    pub password: String,
    pub app_name: String,
    pub client_ip: String,
    pub client_port: i32,
}
impl NacosConfig {
    pub fn new(app_name: &str) -> Self {
        NacosConfig {
            server_addr: std::env::var("NACOS_SERVER_ADDR").unwrap_or_else(|_| "127.0.0.1:8848".to_string()),
            namespace: std::env::var("NACOS_NAMESPACE").unwrap_or_else(|_| "".to_string()),
            group: std::env::var("NACOS_GROUP").unwrap_or_else(|_| "DEFAULT_GROUP".to_string()),
            username: std::env::var("NACOS_USERNAME").unwrap_or_else(|_| "admin".to_string()),
            password: std::env::var("NACOS_PASSWORD").unwrap_or_else(|_| "admin".to_string()),
            app_name: app_name.to_string(),
            client_ip: std::env::var("NACOS_CLIENT_IP").unwrap_or_else(|_| "rust_app".to_string()),
            client_port: std::env::var("NACOS_CLIENT_PORT").unwrap_or_else(|_| "8080".to_string()).parse().unwrap(),
        }
    }
}

struct SimpleConfigChangeListener;

impl ConfigChangeListener for SimpleConfigChangeListener {
    fn notify(&self, config_resp: ConfigResponse) {
        tracing::info!("listen the config={}", config_resp);
    }
}

pub struct SimpleInstanceChangeListener;

impl NamingEventListener for SimpleInstanceChangeListener {
    fn event(&self, event: Arc<NamingChangeEvent>) {
        tracing::info!("subscriber notify: {:?}", event);
    }
}

pub struct NacosClient {
    pub config_service: Arc<ConfigService>,
    pub naming_service: Arc<NamingService>,
    pub config: NacosConfig,
    pub instance: ServiceInstance,
}