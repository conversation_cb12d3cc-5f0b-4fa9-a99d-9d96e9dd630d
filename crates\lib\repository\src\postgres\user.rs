use sqlx::PgPool;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use crate::{Result, RepositoryError};
use super::common::{Pagination, PagedResult, PostgresHelper};
use argon2::{
    password_hash::{rand_core::OsRng, PasswordHasher, SaltString},
    Argon2,
};
use uuid::Uuid;
use common::domain::dto::user::UserStatus;

/// 用户实体
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct User {
    pub id: String,
    pub email: String,
    pub username: String,
    pub password_hash: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub deleted_at: Option<DateTime<Utc>>,
}

/// 用户创建参数
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub email: String,
    pub username: String,
    pub password_hash: String,
}

/// 用户更新参数
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateUserRequest {
    pub email: Option<String>,
    pub username: Option<String>,
    pub password_hash: Option<String>,
    pub is_active: Option<bool>,
}

/// 用户Repository
pub struct UserRepository {
    pool: PgPool,
}

impl UserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 初始化用户数据库表结构
    pub async fn initialize_tables(&self) -> Result<()> {
        // 创建用户状态枚举类型
        sqlx::query(
            r#"
            DO $$ BEGIN
                CREATE TYPE user_status AS ENUM ('pending', 'active', 'frozen');
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 创建用户表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(36) PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                username VARCHAR(100) NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                deleted_at TIMESTAMPTZ NULL
            );
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        // 为用户表创建索引
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);")
            .execute(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        // 创建默认管理员账号
        self.create_default_admin_user().await?;
        
        log::info!("用户数据库表结构初始化完成");
        Ok(())
    }

    /// 创建默认管理员用户
    async fn create_default_admin_user(&self) -> Result<()> {
        // 检查管理员账号是否已存在
        let existing_admin = sqlx::query("SELECT id FROM users WHERE email = $1 AND deleted_at IS NULL")
            .bind("<EMAIL>")
            .fetch_optional(&self.pool)
            .await
            .map_err(|e| RepositoryError::Database(e))?;

        if existing_admin.is_some() {
            log::info!("管理员账号已存在，跳过创建");
            return Ok(());
        }

        // 生成管理员密码的哈希值
        let default_password = "admin123456"; // 默认密码，建议首次登录后修改
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        let password_hash = argon2
            .hash_password(default_password.as_bytes(), &salt)
            .map_err(|e| RepositoryError::Internal(format!("密码加密失败: {}", e)))?
            .to_string();

        let admin_id = Uuid::new_v4().to_string();

        // 插入管理员账号
        sqlx::query(
            r#"
            INSERT INTO users (id, email, username, password_hash, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            "#,
        )
        .bind(&admin_id)
        .bind("<EMAIL>")
        .bind("系统管理员")
        .bind(&password_hash)
        .bind(true)
        .execute(&self.pool)
        .await
        .map_err(|e| RepositoryError::Database(e))?;

        log::warn!("🔐 默认管理员账号已创建:");
        log::warn!("📧 邮箱: <EMAIL>");
        log::warn!("🔑 密码: admin123456");
        log::warn!("⚠️  请首次登录后立即修改密码！");
        
        Ok(())
    }

    /// 创建用户
    pub async fn create(&self, request: CreateUserRequest) -> Result<User> {
        let user = sqlx::query_as::<_, User>(
            r#"
            INSERT INTO users (id, email, username, password_hash, is_active, created_at, updated_at)
            VALUES (gen_random_uuid(), $1, $2, $3, true, NOW(), NOW())
            RETURNING *
            "#,
        )
        .bind(&request.email)
        .bind(&request.username)
        .bind(&request.password_hash)
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    /// 根据ID查找用户
    pub async fn find_by_id(&self, id: &str) -> Result<Option<User>> {
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE id = $1 AND deleted_at IS NULL"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// 根据邮箱查找用户
    pub async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE email = $1 AND deleted_at IS NULL"
        )
        .bind(email)
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// 根据用户名查找用户
    pub async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
        let user = sqlx::query_as::<_, User>(
            "SELECT * FROM users WHERE username = $1 AND deleted_at IS NULL"
        )
        .bind(username)
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// 更新用户
    pub async fn update(&self, id: &str, request: UpdateUserRequest) -> Result<Option<User>> {
        let mut query_parts = Vec::new();
        let mut bind_count = 1;

        if request.email.is_some() {
            query_parts.push(format!("email = ${}", bind_count));
            bind_count += 1;
        }
        if request.username.is_some() {
            query_parts.push(format!("username = ${}", bind_count));
            bind_count += 1;
        }
        if request.password_hash.is_some() {
            query_parts.push(format!("password_hash = ${}", bind_count));
            bind_count += 1;
        }
        if request.is_active.is_some() {
            query_parts.push(format!("is_active = ${}", bind_count));
            bind_count += 1;
        }

        if query_parts.is_empty() {
            return self.find_by_id(id).await;
        }

        query_parts.push("updated_at = NOW()".to_string());
        let set_clause = query_parts.join(", ");
        let sql = format!(
            "UPDATE users SET {} WHERE id = ${} AND deleted_at IS NULL RETURNING *",
            set_clause, bind_count
        );

        let mut query = sqlx::query_as::<_, User>(&sql);
        
        if let Some(email) = &request.email {
            query = query.bind(email);
        }
        if let Some(username) = &request.username {
            query = query.bind(username);
        }
        if let Some(password_hash) = &request.password_hash {
            query = query.bind(password_hash);
        }
        if let Some(is_active) = &request.is_active {
            query = query.bind(is_active);
        }
        query = query.bind(id);

        let user = query.fetch_optional(&self.pool).await?;
        Ok(user)
    }

    /// 软删除用户
    pub async fn soft_delete(&self, id: &str) -> Result<bool> {
        let affected = PostgresHelper::soft_delete(&self.pool, "users", "id", id).await?;
        Ok(affected > 0)
    }

    /// 恢复用户
    pub async fn restore(&self, id: &str) -> Result<bool> {
        let affected = PostgresHelper::restore(&self.pool, "users", "id", id).await?;
        Ok(affected > 0)
    }

    /// 分页查询用户
    pub async fn list(&self, pagination: &Pagination) -> Result<PagedResult<User>> {
        PostgresHelper::paginated_query(
            &self.pool,
            || {
                let mut builder = sqlx::QueryBuilder::new("SELECT * FROM users WHERE deleted_at IS NULL ORDER BY created_at DESC");
                builder
            },
            pagination,
        ).await
    }

    /// 检查邮箱是否已存在
    pub async fn email_exists(&self, email: &str) -> Result<bool> {
        PostgresHelper::exists(&self.pool, "users", "email", email).await
    }

    /// 检查用户名是否已存在
    pub async fn username_exists(&self, username: &str) -> Result<bool> {
        PostgresHelper::exists(&self.pool, "users", "username", username).await
    }
} 