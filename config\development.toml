# 开发环境配置
environment = "Development"

[service]
name = "market-data-maintenance-dev"
version = "1.0.0-dev"
startup_mode = "Fast"  # 开发环境快速启动
graceful_shutdown_timeout_seconds = 10
health_check_interval_seconds = 120

[maintenance]
full_scan_interval_hours = 1  # 开发环境更频繁的扫描
incremental_scan_interval_minutes = 2
batch_size = 100
max_concurrent_symbols = 3
data_retention_days = 30
enable_auto_repair = false  # 开发环境不自动修复，便于调试
repair_retry_count = 1

[cache_sync]
sync_interval_seconds = 30
warmup_days = 1
enable_smart_warmup = false
max_cache_size_mb = 256

[cache_sync.ttl_strategy]
type = "Fixed"
hours = 6

[gap_detection]
intervals = ["1m", "5m"]  # 开发环境只检测基本间隔
batch_window_days = 1
enable_smart_sampling = true
sampling_rate = 0.5  # 开发环境高采样率
severe_gap_threshold = 0.5

[exchanges.binance]
name = "Binance"
base_url = "https://api.binance.com"
timeout_seconds = 15
rate_limit_per_minute = 600
enabled = true

[exchanges.binance.retry]
max_attempts = 2
base_delay_ms = 500
max_delay_ms = 5000
backoff_strategy = "Linear"

[monitoring]
enable_metrics = true
metrics_interval_seconds = 120

[monitoring.alerts]
enabled = false  # 开发环境不发送告警
gap_detection_threshold = 50
sync_delay_threshold_minutes = 30
error_rate_threshold = 0.2

[monitoring.logging]
level = "debug"
structured = false
rotation_size_mb = 50
retention_days = 7

[performance]
db_pool_size = 5
cache_pool_size = 3
worker_threads = 2
memory_limit_mb = 512
enable_compression = false
