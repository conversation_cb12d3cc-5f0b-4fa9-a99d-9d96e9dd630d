[package]
name = "service"
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true
version.workspace = true

[dependencies]
common.workspace = true
nacos.workspace = true
exchange.workspace = true
repository.workspace = true


tokio.workspace = true
anyhow.workspace = true
log.workspace = true
tracing.workspace = true
lazy_static.workspace = true
serde.workspace = true
serde_json.workspace = true
serde_yaml.workspace = true
toml.workspace = true
async-trait.workspace = true
chrono.workspace = true
sqlx.workspace = true  # 保留sqlx依赖，因为common模块的Entity使用了sqlx::types
rust_decimal.workspace = true
uuid.workspace = true

rand_core = "0.6.4"
rand = "0.8.5"

argon2 = "0.5.3"
jsonwebtoken.workspace = true
thiserror = "1.0.69"

[dev-dependencies]
tempfile.workspace = true

[target.'cfg(windows)'.dependencies]
# 在 Windows 平台下，重新定义 rdkafka 依赖，并添加 cmake-build 特性。
# Cargo 会合并或覆盖全局 dependencies 中的配置，确保在 Windows 上启用这个特性。
rdkafka = { version = "0.37.0", features = ["cmake-build"] }